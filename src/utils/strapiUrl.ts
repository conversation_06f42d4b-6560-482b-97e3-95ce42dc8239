/**
 * Formats a Strapi URL by combining the base URL with the provided path
 * Handles both absolute URLs (starting with http:// or https://) and relative URLs
 *
 * @param url The URL or path to format
 * @returns The formatted URL
 */
export const getStrapiUrl = (url?: string): string => {
  if (!url) return "";

  // If the URL is already absolute, return it as is
  if (url.startsWith("http://") || url.startsWith("https://")) {
    return url;
  }

  // If the URL doesn't start with 'upload', return it as is (for local images)
  if (!url.startsWith("/upload") && !url.startsWith("upload")) {
    return url;
  }

  // Make sure the URL starts with a slash
  const path = url.startsWith("/") ? url : `/${url}`;

  // Combine with the Strapi URL from environment variables
  // Use NEXT_PUBLIC_ prefix for client-side access
  const baseUrl =
    process.env.NEXT_PUBLIC_STRAPI_URL ||
    process.env.STRAPI_URL ||
    "http://localhost:1337";

  // Remove trailing slash from baseUrl if it exists
  const formattedBaseUrl = baseUrl.endsWith("/")
    ? baseUrl.slice(0, -1)
    : baseUrl;

  return `${formattedBaseUrl}${path}`;
};
