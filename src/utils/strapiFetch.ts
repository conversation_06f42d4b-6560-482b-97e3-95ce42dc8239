import { NextRequest } from "next/server";
import { STRAPI_TOKEN, STRAPI_URL } from "../../config";

export default async function strapiGraphqlFetch({
  query,
  variables,
  options,
}: {
  query: string;
  variables?: Record<string, unknown>;
  options?: NextRequest;
}) {
  try {
    const response = await fetch(`${STRAPI_URL}/graphql`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${STRAPI_TOKEN}`,
      },
      body: JSON.stringify({ query, variables }),
      ...options,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(
        `Strapi GraphQL request failed (${response.status}): ${errorText}`
      );
    }

    const result = await response.json();

    if (result.errors) {
      throw new Error(JSON.stringify(result.errors));
    }

    return result.data;
  } catch (error) {
    throw error instanceof Error ? error : new Error(String(error));
  }
}
