import { CertificateManagerType } from "../Components/Certificates";
import { CashbackType } from "../Components/Common/Cashback";
import { AnnouncementBarType } from "../Components/Common/AnnouncementBar";
import { RealPeopleReviewsType } from "../Components/Reviews/RealPeopleReviews";

export interface GlobalSettingType {
  documentId: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  certificates?: CertificateManagerType;
  real_people_reviews?: RealPeopleReviewsType;
  tax_description?: string;
  cashback?: CashbackType;
  announcement_bar?: AnnouncementBarType;
}
