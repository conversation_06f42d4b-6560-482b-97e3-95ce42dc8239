export interface CollectionType {
  id?: string;
  title: string;
  handle?: string;
  description?: string;
  image?: {
    url?: string;
    alternativeText?: string;
  };
  products?: any[]; // Replace 'any' with a more specific type if available
  strapi?: {
    title?: string;
    banner_image?: {
      desktop?: {
        media?: {
          url?: string;
        };
        action_link?: string;
      };
      mobile?: {
        media?: {
          url?: string;
        };
        action_link?: string;
      };
    };
    banner_image_carousel?: Array<{
      images: Array<{
        desktop?: {
          media?: {
            url?: string;
          };
          action_link?: string;
        };
        mobile?: {
          media?: {
            url?: string;
          };
          action_link?: string;
        };
        redirect_link?: {
          title?: string;
          link?: string;
        };
      }>;
      endless_banner?: boolean;
      position?: number;
    }>;
    breadcrumb?: {
      breadcrumb_items?: any[]; // Replace 'any' with a more specific type if available
    };
  };
} 