interface Media {
  url?: string;
}

interface ImageVariant {
  media?: Media;
  action_link?: string;
}

interface BannerImage {
  desktop?: ImageVariant;
  mobile?: ImageVariant;
  redirect_link?: {
    title?: string;
    link?: string;
  };
}

export interface BannerImageCarouselType {
  show: boolean;
  position?: number;
  endless_banner?: boolean;
  auto_play?: boolean;
  images: BannerImage[];
} 