import { StoreProduct, StoreProductVariant } from "@medusajs/types";
import { ProductDetailsType } from "../Collections/ProductDetails";

export interface VariantImage {
  id: string;
  url: string;
  rank: number;
  variant_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

//extended variant type
export interface ExtendedVariant extends StoreProductVariant {
  variant_image?: VariantImage[];
  variant_rank: number;
  prices: Array<{
    id: string;
    currency_code: string;
    amount: number;
    rules_count: number;
    raw_amount: {
      value: string;
      precision: number;
    };
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }>;
  extended_product_variants?: {
    id: string;
    compare_at_price: number | null;
    sdd: boolean;
    gtin: string | null;
    google_product_category: string;
    primary_image_id: string | null;
    bundle_type: string;
    max_bundle_per_order_quantity: number | null;
    products_per_bundle: number | null;
    online_payment_only: boolean;
    bundle_discount_type: string | null;
    bundle_discount: number | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  };
}

// Extend StoreProduct to have variants as ExtendedVariant[]
export interface ExtendedMedusaProduct extends StoreProduct {
  variants: ExtendedVariant[];
}

// Final type that combines everything
export type ExtendedMedusaProductWithStrapiProduct = ExtendedMedusaProduct & {
  cms_product: ProductDetailsType;
};
