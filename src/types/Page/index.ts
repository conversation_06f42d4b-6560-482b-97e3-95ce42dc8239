import { Title } from '@/types/Common';

export enum PageTypeEnum {
  Page = "PAGE",
  Index = "INDEX",
  Collection = "COLLECTION",
  Product = "PRODUCT",
}

export interface PageType {
  documentId: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
}

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor: string;
  endCursor: string;
}

export interface PageBlock {
  __typename: string;
  id: string;
  title: Title;
  show_component: boolean | null;
  reference_field?: string;
}
