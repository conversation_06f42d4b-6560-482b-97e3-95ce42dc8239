import { ProductType } from '@/types/Collections/Product';
import { TitleLinkType } from '@/types/Components/Elements/TitleLink';

export interface TitleColorType {
  title: string;
}

export interface MenuType {
  title: string;
  action_link?: string;
  is_external_link: boolean;
  sub_menu: MenuType[];
  show?: boolean;
  products: ProductType[];
  tag?: TitleColorType;
  sub_menu_title?: string;
  see_all?: TitleLinkType;
}
