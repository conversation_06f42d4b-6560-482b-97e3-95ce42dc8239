// Interface automatically generated by schemas-to-ts

import { ContactInfoItems } from './ContactInfoItems';
import { ContactInfoItems_Plain } from './ContactInfoItems';
import { ContactInfoItems_NoRelations } from './ContactInfoItems';

export interface ContactInfo {
  contact_info_items: ContactInfoItems[];
}
export interface ContactInfo_Plain {
  contact_info_items: ContactInfoItems_Plain[];
}

export interface ContactInfo_NoRelations {
  contact_info_items: ContactInfoItems_NoRelations[];
}

