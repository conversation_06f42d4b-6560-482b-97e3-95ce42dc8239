// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ChatButton {
  text?: string;
  icon?: { data: Media };
  link?: string;
}
export interface ChatButton_Plain {
  text?: string;
  icon?: Media_Plain;
  link?: string;
}

export interface ChatButton_NoRelations {
  text?: string;
  icon?: number;
  link?: string;
}

