// Interface automatically generated by schemas-to-ts

import { FaqSection } from './FaqSection';
import { ContactInfo } from './ContactInfo';
import { WorkingHours } from './WorkingHours';
import { FaqSection_Plain } from './FaqSection';
import { ContactInfo_Plain } from './ContactInfo';
import { WorkingHours_Plain } from './WorkingHours';
import { FaqSection_NoRelations } from './FaqSection';
import { ContactInfo_NoRelations } from './ContactInfo';
import { WorkingHours_NoRelations } from './WorkingHours';

export interface FaQs {
  title?: string;
  description?: any;
  faq_section?: FaqSection;
  contact_info?: ContactInfo;
  working_hours?: WorkingHours;
}
export interface FaQs_Plain {
  title?: string;
  description?: any;
  faq_section?: FaqSection_Plain;
  contact_info?: ContactInfo_Plain;
  working_hours?: WorkingHours_Plain;
}

export interface FaQs_NoRelations {
  title?: string;
  description?: any;
  faq_section?: FaqSection_NoRelations;
  contact_info?: ContactInfo_NoRelations;
  working_hours?: WorkingHours_NoRelations;
}

