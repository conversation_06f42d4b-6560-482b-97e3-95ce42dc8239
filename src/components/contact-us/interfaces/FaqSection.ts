// Interface automatically generated by schemas-to-ts

import { FaqSectionItems } from './FaqSectionItems';
import { FaqSectionItems_Plain } from './FaqSectionItems';
import { FaqSectionItems_NoRelations } from './FaqSectionItems';

export interface FaqSection {
  faq_section_items: FaqSectionItems[];
}
export interface FaqSection_Plain {
  faq_section_items: FaqSectionItems_Plain[];
}

export interface FaqSection_NoRelations {
  faq_section_items: FaqSectionItems_NoRelations[];
}

