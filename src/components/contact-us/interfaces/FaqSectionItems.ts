// Interface automatically generated by schemas-to-ts

import { FaqItems } from '../../faqs/interfaces/FaqItems';
import { FaqItems_Plain } from '../../faqs/interfaces/FaqItems';
import { FaqItems_NoRelations } from '../../faqs/interfaces/FaqItems';

export interface FaqSectionItems {
  header?: string;
  faq_items: FaqItems[];
}
export interface FaqSectionItems_Plain {
  header?: string;
  faq_items: FaqItems_Plain[];
}

export interface FaqSectionItems_NoRelations {
  header?: string;
  faq_items: FaqItems_NoRelations[];
}

