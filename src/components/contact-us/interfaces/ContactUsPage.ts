// Interface automatically generated by schemas-to-ts

import { ContactUs } from './ContactUs';
import { FaQs } from './FaQs';
import { ContactUs_Plain } from './ContactUs';
import { FaQs_Plain } from './FaQs';
import { ContactUs_NoRelations } from './ContactUs';
import { FaQs_NoRelations } from './FaQs';

export interface ContactUsPage {
  contact_us?: ContactUs;
  faqs?: FaQs;
}
export interface ContactUsPage_Plain {
  contact_us?: ContactUs_Plain;
  faqs?: FaQs_Plain;
}

export interface ContactUsPage_NoRelations {
  contact_us?: ContactUs_NoRelations;
  faqs?: FaQs_NoRelations;
}

