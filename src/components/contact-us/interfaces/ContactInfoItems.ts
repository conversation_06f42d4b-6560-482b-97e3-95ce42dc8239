// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ContactInfoItems {
  icon?: { data: Media };
  description?: any;
}
export interface ContactInfoItems_Plain {
  icon?: Media_Plain;
  description?: any;
}

export interface ContactInfoItems_NoRelations {
  icon?: number;
  description?: any;
}

