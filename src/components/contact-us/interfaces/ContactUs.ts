// Interface automatically generated by schemas-to-ts

import { ChatButton } from './ChatButton';
import { Media } from '../../../common/schemas-to-ts/Media';
import { ChatButton_Plain } from './ChatButton';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { ChatButton_NoRelations } from './ChatButton';

export interface ContactUs {
  title?: string;
  description?: string;
  bg_color?: any;
  primary_color?: any;
  chat_button?: ChatButton;
  bg_icon?: { data: Media };
}
export interface ContactUs_Plain {
  title?: string;
  description?: string;
  bg_color?: any;
  primary_color?: any;
  chat_button?: ChatButton_Plain;
  bg_icon?: Media_Plain;
}

export interface ContactUs_NoRelations {
  title?: string;
  description?: string;
  bg_color?: any;
  primary_color?: any;
  chat_button?: ChatButton_NoRelations;
  bg_icon?: number;
}

