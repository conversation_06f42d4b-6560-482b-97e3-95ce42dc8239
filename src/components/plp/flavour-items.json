{"collectionName": "components_plp_flavour_items", "info": {"displayName": "Flavour Items", "description": ""}, "options": {}, "attributes": {"title": {"type": "text"}, "title_icon": {"type": "component", "repeatable": false, "component": "media.icon"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "flavour_product_item": {"displayName": "Flavour Product Item", "type": "component", "repeatable": false, "component": "plp.flavour-product-item"}, "ingredient_items": {"displayName": "Flavour Ingredient Items", "type": "component", "repeatable": true, "component": "plp.flavour-ingredient-items"}, "button": {"type": "component", "repeatable": false, "component": "elements.button"}}}