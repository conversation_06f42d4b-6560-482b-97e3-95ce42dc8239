// Interface automatically generated by schemas-to-ts

import { FlavourItems } from './FlavourItems';
import { FlavourItems_Plain } from './FlavourItems';
import { FlavourItems_NoRelations } from './FlavourItems';

export interface IndulgentFlavours {
  title?: string;
  subtitle?: string;
  flavour_items: FlavourItems[];
}
export interface IndulgentFlavours_Plain {
  title?: string;
  subtitle?: string;
  flavour_items: FlavourItems_Plain[];
}

export interface IndulgentFlavours_NoRelations {
  title?: string;
  subtitle?: string;
  flavour_items: FlavourItems_NoRelations[];
}

