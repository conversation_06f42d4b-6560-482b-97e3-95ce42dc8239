// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface FlavourProductItem {
  image?: Icon;
  title?: string;
  subtitle?: string;
  description?: string;
}
export interface FlavourProductItem_Plain {
  image?: Icon_Plain;
  title?: string;
  subtitle?: string;
  description?: string;
}

export interface FlavourProductItem_NoRelations {
  image?: Icon_NoRelations;
  title?: string;
  subtitle?: string;
  description?: string;
}

