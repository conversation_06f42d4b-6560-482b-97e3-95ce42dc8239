// Interface automatically generated by schemas-to-ts

import { ExternalMediaEmbed } from '../../media/interfaces/ExternalMediaEmbed';
import { ExternalMediaEmbed_Plain } from '../../media/interfaces/ExternalMediaEmbed';
import { ExternalMediaEmbed_NoRelations } from '../../media/interfaces/ExternalMediaEmbed';

export interface NothingToHide {
  title?: string;
  bg_color?: any;
  youtube_link?: ExternalMediaEmbed;
}
export interface NothingToHide_Plain {
  title?: string;
  bg_color?: any;
  youtube_link?: ExternalMediaEmbed_Plain;
}

export interface NothingToHide_NoRelations {
  title?: string;
  bg_color?: any;
  youtube_link?: ExternalMediaEmbed_NoRelations;
}

