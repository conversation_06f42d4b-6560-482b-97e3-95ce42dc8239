// Interface automatically generated by schemas-to-ts

import { PickYourProteinItems } from './PickYourProteinItems';
import { PickYourProteinItems_Plain } from './PickYourProteinItems';
import { PickYourProteinItems_NoRelations } from './PickYourProteinItems';

export interface PickYourProtein {
  bg_color?: any;
  protein_items: PickYourProteinItems[];
}
export interface PickYourProtein_Plain {
  bg_color?: any;
  protein_items: PickYourProteinItems_Plain[];
}

export interface PickYourProtein_NoRelations {
  bg_color?: any;
  protein_items: PickYourProteinItems_NoRelations[];
}

