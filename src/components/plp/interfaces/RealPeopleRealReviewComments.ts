// Interface automatically generated by schemas-to-ts

import { ReviewCommentItems } from './ReviewCommentItems';
import { ReviewCommentItems_Plain } from './ReviewCommentItems';
import { ReviewCommentItems_NoRelations } from './ReviewCommentItems';

export interface RealPeopleRealReviewComments {
  title?: string;
  bg_color?: any;
  comment_items: ReviewCommentItems[];
}
export interface RealPeopleRealReviewComments_Plain {
  title?: string;
  bg_color?: any;
  comment_items: ReviewCommentItems_Plain[];
}

export interface RealPeopleRealReviewComments_NoRelations {
  title?: string;
  bg_color?: any;
  comment_items: ReviewCommentItems_NoRelations[];
}

