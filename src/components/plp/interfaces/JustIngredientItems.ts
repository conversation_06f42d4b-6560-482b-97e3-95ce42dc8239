// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { KeyValue } from '../../elements/interfaces/KeyValue';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { KeyValue_Plain } from '../../elements/interfaces/KeyValue';
import { Icon_NoRelations } from '../../media/interfaces/Icon';
import { KeyValue_NoRelations } from '../../elements/interfaces/KeyValue';

export interface JustIngredientItems {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  image?: Icon;
  ingredient_items: KeyValue[];
}
export interface JustIngredientItems_Plain {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  image?: Icon_Plain;
  ingredient_items: KeyValue_Plain[];
}

export interface JustIngredientItems_NoRelations {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  image?: Icon_NoRelations;
  ingredient_items: KeyValue_NoRelations[];
}

