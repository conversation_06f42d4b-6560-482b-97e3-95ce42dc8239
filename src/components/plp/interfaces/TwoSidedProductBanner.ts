// Interface automatically generated by schemas-to-ts

import { SingleBanner } from '../../banner/interfaces/SingleBanner';
import { SingleBanner_Plain } from '../../banner/interfaces/SingleBanner';
import { SingleBanner_NoRelations } from '../../banner/interfaces/SingleBanner';

export interface TwoSidedProductBanner {
  banner_1?: SingleBanner;
  banner_2?: SingleBanner;
}
export interface TwoSidedProductBanner_Plain {
  banner_1?: SingleBanner_Plain;
  banner_2?: SingleBanner_Plain;
}

export interface TwoSidedProductBanner_NoRelations {
  banner_1?: SingleBanner_NoRelations;
  banner_2?: SingleBanner_NoRelations;
}

