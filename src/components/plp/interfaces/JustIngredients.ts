// Interface automatically generated by schemas-to-ts

import { JustIngredientItems } from './JustIngredientItems';
import { JustIngredientItems_Plain } from './JustIngredientItems';
import { JustIngredientItems_NoRelations } from './JustIngredientItems';

export interface JustIngredients {
  title?: string;
  just_ingredient_items: JustIngredientItems[];
}
export interface JustIngredients_Plain {
  title?: string;
  just_ingredient_items: JustIngredientItems_Plain[];
}

export interface JustIngredients_NoRelations {
  title?: string;
  just_ingredient_items: JustIngredientItems_NoRelations[];
}

