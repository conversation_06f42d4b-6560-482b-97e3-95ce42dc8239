// Interface automatically generated by schemas-to-ts

import { Button } from '../../elements/interfaces/Button';
import { Icon } from '../../media/interfaces/Icon';
import { Button_Plain } from '../../elements/interfaces/Button';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Button_NoRelations } from '../../elements/interfaces/Button';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface ShopChocolate {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  subtitle?: string;
  button?: Button;
  images: Icon[];
}
export interface ShopChocolate_Plain {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  subtitle?: string;
  button?: Button_Plain;
  images: Icon_Plain[];
}

export interface ShopChocolate_NoRelations {
  primary_color?: any;
  bg_color?: any;
  title?: string;
  subtitle?: string;
  button?: Button_NoRelations;
  images: Icon_NoRelations[];
}

