// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface ProcessImages {
  subtitle?: string;
  images: Icon[];
  bg_color?: any;
  title_color?: any;
  subtitle_color?: any;
  title?: any;
}
export interface ProcessImages_Plain {
  subtitle?: string;
  images: Icon_Plain[];
  bg_color?: any;
  title_color?: any;
  subtitle_color?: any;
  title?: any;
}

export interface ProcessImages_NoRelations {
  subtitle?: string;
  images: Icon_NoRelations[];
  bg_color?: any;
  title_color?: any;
  subtitle_color?: any;
  title?: any;
}

