// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface DarkChocolateProductListing {
  title?: string;
  bg_color?: any;
  category?: { data: Category };
}
export interface DarkChocolateProductListing_Plain {
  title?: string;
  bg_color?: any;
  category?: Category_Plain;
}

export interface DarkChocolateProductListing_NoRelations {
  title?: string;
  bg_color?: any;
  category?: number;
}

