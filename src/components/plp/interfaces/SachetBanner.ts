// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Product } from '../../../api/product/content-types/product/product';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { Product_Plain } from '../../../api/product/content-types/product/product';

export interface SachetBanner {
  image?: { data: Media };
  title?: string;
  description?: string;
  bg_color?: any;
  product?: { data: Product };
}
export interface SachetBanner_Plain {
  image?: Media_Plain;
  title?: string;
  description?: string;
  bg_color?: any;
  product?: Product_Plain;
}

export interface SachetBanner_NoRelations {
  image?: number;
  title?: string;
  description?: string;
  bg_color?: any;
  product?: number;
}

