{"collectionName": "components_plp_borderless_product_listings", "info": {"displayName": "Borderless Product Listing", "description": ""}, "options": {}, "attributes": {"category": {"type": "relation", "relation": "oneToOne", "target": "api::category.category"}, "title": {"type": "string"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}}}