{"collectionName": "components_header_header_menus", "info": {"displayName": "Header <PERSON><PERSON>", "description": ""}, "options": {}, "attributes": {"title": {"type": "text", "required": true}, "action_link": {"type": "text"}, "image": {"type": "component", "repeatable": false, "component": "media.image"}, "menu_items": {"displayName": "Menu Items", "type": "component", "repeatable": true, "component": "header.menu-items"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "tag": {"type": "text"}}}