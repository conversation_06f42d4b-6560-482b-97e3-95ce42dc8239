// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { Image_Plain } from '../../media/interfaces/Image';
import { Image_NoRelations } from '../../media/interfaces/Image';

export interface ProteinBanner {
  title?: string;
  subtitle?: string;
  bg_image?: Image;
  image?: Image;
}
export interface ProteinBanner_Plain {
  title?: string;
  subtitle?: string;
  bg_image?: Image_Plain;
  image?: Image_Plain;
}

export interface ProteinBanner_NoRelations {
  title?: string;
  subtitle?: string;
  bg_image?: Image_NoRelations;
  image?: Image_NoRelations;
}

