// Interface automatically generated by schemas-to-ts

import { KeyValue } from '../../elements/interfaces/KeyValue';
import { Image } from '../../media/interfaces/Image';
import { KeyValue_Plain } from '../../elements/interfaces/KeyValue';
import { Image_Plain } from '../../media/interfaces/Image';
import { KeyValue_NoRelations } from '../../elements/interfaces/KeyValue';
import { Image_NoRelations } from '../../media/interfaces/Image';

export interface WhatsInsideDetails {
  title?: string;
  details: KeyValue[];
  image?: Image;
}
export interface WhatsInsideDetails_Plain {
  title?: string;
  details: KeyValue_Plain[];
  image?: Image_Plain;
}

export interface WhatsInsideDetails_NoRelations {
  title?: string;
  details: KeyValue_NoRelations[];
  image?: Image_NoRelations;
}

