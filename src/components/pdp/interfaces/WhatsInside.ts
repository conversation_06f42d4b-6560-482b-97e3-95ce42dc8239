// Interface automatically generated by schemas-to-ts

import { WhatsInsideDetails } from './WhatsInsideDetails';
import { WhatsInsideDetails_Plain } from './WhatsInsideDetails';
import { WhatsInsideDetails_NoRelations } from './WhatsInsideDetails';

export interface WhatsInside {
  title?: string;
  show_component?: boolean;
  whats_inside_details: WhatsInsideDetails[];
}
export interface WhatsInside_Plain {
  title?: string;
  show_component?: boolean;
  whats_inside_details: WhatsInsideDetails_Plain[];
}

export interface WhatsInside_NoRelations {
  title?: string;
  show_component?: boolean;
  whats_inside_details: WhatsInsideDetails_NoRelations[];
}

