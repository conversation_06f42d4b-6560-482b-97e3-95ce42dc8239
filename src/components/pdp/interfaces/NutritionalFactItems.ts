// Interface automatically generated by schemas-to-ts

import { NutritionalFactSubItems } from './NutritionalFactSubItems';
import { NutritionalFactSubItems_Plain } from './NutritionalFactSubItems';
import { NutritionalFactSubItems_NoRelations } from './NutritionalFactSubItems';

export interface NutritionalFactItems {
  key?: string;
  value?: string;
  nutritional_fact_sub_items: NutritionalFactSubItems[];
}
export interface NutritionalFactItems_Plain {
  key?: string;
  value?: string;
  nutritional_fact_sub_items: NutritionalFactSubItems_Plain[];
}

export interface NutritionalFactItems_NoRelations {
  key?: string;
  value?: string;
  nutritional_fact_sub_items: NutritionalFactSubItems_NoRelations[];
}

