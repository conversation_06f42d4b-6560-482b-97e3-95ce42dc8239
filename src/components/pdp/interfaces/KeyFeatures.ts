// Interface automatically generated by schemas-to-ts

import { FlippableCard } from '../../common/interfaces/FlippableCard';
import { FlippableCard_Plain } from '../../common/interfaces/FlippableCard';
import { FlippableCard_NoRelations } from '../../common/interfaces/FlippableCard';

export interface KeyFeatures {
  key_feature_items?: FlippableCard;
  title?: string;
  show_component: boolean;
}
export interface KeyFeatures_Plain {
  key_feature_items?: FlippableCard_Plain;
  title?: string;
  show_component: boolean;
}

export interface KeyFeatures_NoRelations {
  key_feature_items?: FlippableCard_NoRelations;
  title?: string;
  show_component: boolean;
}

