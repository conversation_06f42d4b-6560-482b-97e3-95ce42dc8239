// Interface automatically generated by schemas-to-ts

import { FaqItems } from './FaqItems';
import { FaqItems_Plain } from './FaqItems';
import { FaqItems_NoRelations } from './FaqItems';

export interface Faqs {
  faq_items: FaqItems[];
  title?: string;
  subtitle?: string;
  show_component: boolean;
}
export interface Faqs_Plain {
  faq_items: FaqItems_Plain[];
  title?: string;
  subtitle?: string;
  show_component: boolean;
}

export interface Faqs_NoRelations {
  faq_items: FaqItems_NoRelations[];
  title?: string;
  subtitle?: string;
  show_component: boolean;
}

