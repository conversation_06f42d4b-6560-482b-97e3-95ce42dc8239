import React from "react";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import SeactionHeading from "@/components/Common/Section/Heading";
import { FlipCard } from "@/components/Common/FlipCard.tsx";

interface FlipCardData {
  id: string | number;
  frontImage: string;
  backImage: string;
  frontAlt: string;
  backAlt: string;
}

interface FlipableCardsSectionProps {
  /** Section title */
  title: string;
  /** Color for the section title text */
  titleColor?: string;
  /** Background color for the section */
  backgroundColor?: string;
  /** Optional background image URL */
  backgroundImage?: string;
  /** Array of card data to display */
  cards: FlipCardData[];
  /** Tailwind class for gap between cards */
  gapClassName?: string;
  /** Optional icon to display with the title */
  icon?: React.ReactNode;
  /** Color for the icon */
  iconColor?: string;
  /** Additional classes for the icon */
  iconClassName?: string;
  /** Whether to hide the scrollbar */
  hideScrollbar?: boolean;
  /** Color for the scrollbar */
  scrollbarColor?: string;
  /** Width for each flip card (in pixels) */
  cardWidth?: number;
  /** Height for each flip card (in pixels) */
  cardHeight?: number;
  /** Additional classes to apply to each flip card */
  cardClassName?: string;
}

export default function FlipableCardsSection({
  title,
  titleColor = "#1a181e",
  backgroundColor = "#F9DEA5",
  backgroundImage,
  cards,
  gapClassName = "gap-4.5",
  icon,
  iconColor,
  iconClassName,
  hideScrollbar = false,
  scrollbarColor,
  cardWidth,
  cardHeight,
  cardClassName,
}: FlipableCardsSectionProps) {
  // Prepare the style object based on props
  let containerStyle: React.CSSProperties = {};

  // Set background color if not transparent
  if (backgroundColor !== "transparent") {
    containerStyle.backgroundColor = backgroundColor;
  }

  // Add background image if provided
  if (backgroundImage) {
    containerStyle = {
      ...containerStyle,
      backgroundImage: `url('${backgroundImage}')`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      backgroundRepeat: "no-repeat",
    };
  }

  return (
    <div
      className={`py-8 ${
        backgroundImage ? "bg-cover bg-center bg-no-repeat" : ""
      }`}
      style={containerStyle}
    >
      <div className="max-w-6xl mx-auto px-4">
        <SeactionHeading
          title={title}
          color={titleColor}
          icon={icon}
          iconColor={iconColor}
          iconClassName={iconClassName}
        />
        <div className="md:mt-7.5 mt-5">
          <UniversalCarousel
            useNativeScrollbar={true}
            hideScrollbar={hideScrollbar}
            gapClassName={gapClassName}
            scrollbarColor={scrollbarColor}
          >
            {cards.map((card) => (
              <FlipCard
                key={card.id}
                frontImage={card.frontImage}
                backImage={card.backImage}
                frontAlt={card.frontAlt}
                backAlt={card.backAlt}
                width={cardWidth}
                height={cardHeight}
                className={cardClassName}
              />
            ))}
          </UniversalCarousel>
        </div>
      </div>
    </div>
  );
}
