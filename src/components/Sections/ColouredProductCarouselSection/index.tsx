"use client";
import React from "react";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import SeactionHeading from "@/components/Common/Section/Heading";
import { Product } from "@/components/Common/ProductCard";
import { cn } from "@/libs/utils";
import ColouredProductCard from "@/components/Common/ColouredProductCard";

interface ColouredProductCarouselSectionProps {
  title: string;
  products: Product[];
  backgroundColor?: string;
  titleColor?: string;
  hideScrollbar?: boolean;
  scrollbarColor?: string;
  scrollBarTrackColor?: string;
  className?: string;
  enableProductBg?: boolean;
}

/**
 * A reusable section component that displays products in a carousel
 */
const ColouredProductCarouselSection: React.FC<
  ColouredProductCarouselSectionProps
> = ({
  title,
  products,
  backgroundColor = "#EFA146",
  titleColor = "#FFFFFF",
  hideScrollbar = true,
  scrollbarColor,
  scrollBarTrackColor,
  className,
  enableProductBg = false,
}) => {
  return (
    <div className={cn("py-8", className)} style={{ backgroundColor }}>
      <div className="max-w-[1164px] mx-auto px-4">
        <SeactionHeading title={title} color={titleColor} />
        <div className="md:mt-7.5 mt-5">
          {/* Mobile view: Carousel */}
          <div className="lg:hidden">
            <UniversalCarousel
              useNativeScrollbar={true}
              hideScrollbar={hideScrollbar}
              scrollbarColor={scrollbarColor}
              scrollBarTrackColor={scrollBarTrackColor}
              itemClassName="w-[280px] sm:w-[300px] md:w-[260px] flex-shrink-0 h-full"
              gapClassName="gap-4"
              className="pb-4"
            >
              {products.map((product) => {
                // Create a copy of the product and modify enableProductBg
                const productWithBg = {
                  ...product,
                  enableProductBg: enableProductBg,
                };

                return (
                  <ColouredProductCard
                    key={product.id}
                    product={productWithBg}
                    className="h-full"
                  />
                );
              })}
            </UniversalCarousel>
          </div>

          {/* Desktop view: Grid with adaptive columns based on product count */}
          <div
            className={cn(
              "hidden lg:grid gap-4 auto-rows-fr",
              products.length === 1
                ? "lg:grid-cols-1 max-w-md mx-auto"
                : products.length === 2
                ? "lg:grid-cols-2 max-w-2xl mx-auto"
                : products.length === 3
                ? "lg:grid-cols-3 max-w-4xl mx-auto"
                : "lg:grid-cols-4"
            )}
          >
            {products.map((product) => {
              // Create a copy of the product and modify enableProductBg
              const productWithBg = {
                ...product,
                enableProductBg: enableProductBg,
              };

              return (
                <ColouredProductCard
                  key={product.id}
                  product={productWithBg}
                  className="h-full"
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ColouredProductCarouselSection;
