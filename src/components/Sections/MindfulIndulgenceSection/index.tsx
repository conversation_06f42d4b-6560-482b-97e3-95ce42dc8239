"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";
import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";

export interface MindfulIndulgenceSectionProps {
  /**
   * The main heading text
   */
  heading: string;

  /**
   * The subheading text
   */
  subheading: string;

  /**
   * The URL of the image to display
   */
  imageUrl: string;

  /**
   * The background color of the section
   * @default "#FFECC7"
   */
  bgColor?: string;

  /**
   * The color of the heading text
   * @default "currentColor"
   */
  headingColor?: string;

  /**
   * The color of the subheading text
   * @default "#E78200"
   */
  subheadingColor?: string;

  /**
   * Additional CSS classes to apply to the container
   */
  className?: string;

  /**
   * Alt text for the image
   * @default "Mindful indulgence"
   */
  imageAlt?: string;

  /**
   * Images to display in the FeatureHighlightSection on mobile
   * @default []
   */
  mobileImages?: string[];
}

/**
 * A section component that displays a heading, subheading, and a full-width image.
 * The component is responsive, showing a custom section on desktop (lg breakpoint and above)
 * and a FeatureHighlightSection on mobile (below lg breakpoint).
 */
const MindfulIndulgenceSection: React.FC<MindfulIndulgenceSectionProps> = ({
  heading,
  subheading,
  imageUrl,
  bgColor = "#FFECC7",
  headingColor,
  subheadingColor = "#E78200",
  className,
  imageAlt = "Mindful indulgence",
  mobileImages = [],
}) => {
  return (
    <>
      {/* Desktop version - shown on lg screens and above */}
      <section
        className={cn(
          "hidden lg:flex pt-6 items-center justify-center flex-col",
          className
        )}
        style={{ backgroundColor: bgColor }}
      >
        <div className="space-y-1">
          <p
            className="text-4xl font-narrow font-semibold"
            style={{ color: headingColor }}
          >
            {heading}
          </p>
          <p
            className="text-2xl font-gooddog font-normal"
            style={{ color: subheadingColor }}
          >
            {subheading}
          </p>
        </div>
        <div className="max-w-[1150px]">
          <Image
            src={imageUrl}
            alt={imageAlt}
            width={1150}
            height={500}
            priority
          />
        </div>
      </section>

      {/* Mobile version - shown on screens smaller than lg */}
      <div className="block lg:hidden">
        <FeatureHighlightSection
          title={heading}
          phraseColor={headingColor}
          subtitle={subheading}
          subtitleColor={subheadingColor}
          subtitleClassName="font-gooddog"
          backgroundColor={bgColor}
          images={mobileImages}
        />
      </div>
    </>
  );
};

export default MindfulIndulgenceSection;
