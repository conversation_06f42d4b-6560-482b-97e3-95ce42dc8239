"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { StrikeThrough } from "@/assets/icons/StrikeThrough";
import { getStrapiUrl } from "@/utils/strapiUrl";

export interface ProductImageType {
  mweb_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  web_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
}

export interface ProductImageType {
  mweb_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  web_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  /** Optional action URL for the product */
  actionUrl?: string;
}

export interface ChooseProductSectionProps {
  /** The phrase that appears before the erased text (default: "Pick your") */
  phrase?: string;
  /** The text that is erased/strikethrough (default: "poison") */
  erased?: string;
  /** The text that appears visible above the strikethrough (default: "protein") */
  visible?: string;
  /** Background color for the section (default: #772D4A) */
  bgColor?: string;
  /** First product image data */
  firstProduct: ProductImageType;
  /** Second product image data */
  secondProduct: ProductImageType;
  /** Optional action URL for the entire section */
  actionUrl?: string;
  /** Additional class name for the section */
  className?: string;
}

const ChooseProductSection: React.FC<ChooseProductSectionProps> = ({
  phrase = "Pick your",
  erased = "poison",
  visible = "protein",
  bgColor = "#772D4A",
  firstProduct,
  secondProduct,
  actionUrl,
  className,
}) => {
  // Helper function to render product image with optional link
  const renderProduct = (product: ProductImageType) => {
    const ProductContent = () => (
      <>
        <div className="aspect-video relative hidden lg:block">
          <Image
            src={
              product.web_image.url.startsWith("/uploads")
                ? getStrapiUrl(product.web_image.url)
                : product.web_image.url
            }
            alt={product.web_image.alternativeText || "Product desktop image"}
            fill
            style={{ objectFit: "contain" }}
            priority
          />
        </div>
        <div className="aspect-[5/4] relative block lg:hidden">
          <Image
            src={
              product.mweb_image.url.startsWith("/uploads")
                ? getStrapiUrl(product.mweb_image.url)
                : product.mweb_image.url
            }
            alt={product.mweb_image.alternativeText || "Product mobile image"}
            fill
            style={{ objectFit: "contain" }}
            priority
          />
        </div>
      </>
    );

    return product.actionUrl ? (
      <Link href={product.actionUrl}>
        <ProductContent />
      </Link>
    ) : (
      <ProductContent />
    );
  };

  const SectionContent = () => (
    <div
      className={cn("md:py-12 py-6 text-center px-8", className)}
      style={{ backgroundColor: bgColor }}
    >
      <h2 className="text-[32px] lg:text-[38px] font-narrow font-semibold text-white mb-6">
        {phrase}{" "}
        <span className="relative inline-block mx-1">
          <span className="relative z-10 text-[32px] lg:text-[38px] font-narrow font-semibold text-white">
            {erased}
          </span>
          <span className="absolute top-[-30px] lg:top-[-24px] right-[-30px] rotate-[-10deg] text-[28px] font-gooddog font-normal text-white">
            {visible}
          </span>
          <StrikeThrough className="absolute top-5 left-0 right-0" />
        </span>
      </h2>

      {/* Product images section */}
      <div className="max-w-[1118px] mx-auto grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* First product */}
        <div>{renderProduct(firstProduct)}</div>

        {/* Second product */}
        <div>{renderProduct(secondProduct)}</div>
      </div>
    </div>
  );

  return actionUrl ? (
    <Link href={actionUrl} className="block w-full">
      <SectionContent />
    </Link>
  ) : (
    <SectionContent />
  );
};

export default ChooseProductSection;
