# ChooseProductSection Component

A reusable section component that displays a "Pick your X" heading with a strikethrough effect and two product images that are responsive for mobile and desktop viewports.

## Features

- Customizable phrase that appears before the strikethrough text
- Customizable erased text with strikethrough effect
- Customizable visible text that appears above the strikethrough
- Responsive design with separate images for mobile and desktop
- Optional links for each product image
- Customizable background color
- Maintains consistent styling with the rest of the application

## Props

| Prop            | Type               | Default       | Description                                                            |
| --------------- | ------------------ | ------------- | ---------------------------------------------------------------------- |
| `phrase`        | `string`           | `"Pick your"` | The phrase that appears before the erased text                         |
| `erased`        | `string`           | `"poison"`    | The text that is erased/strikethrough                                  |
| `visible`       | `string`           | `"protein"`   | The text that appears visible above the strikethrough                  |
| `bgColor`       | `string`           | `"#772D4A"`   | Background color for the section                                       |
| `firstProduct`  | `ProductImageType` | Required      | Object containing mobile and desktop image data for the first product  |
| `secondProduct` | `ProductImageType` | Required      | Object containing mobile and desktop image data for the second product |
| `actionUrl`     | `string`           | `undefined`   | Optional URL to make the entire section clickable                      |
| `className`     | `string`           | `undefined`   | Additional CSS classes to apply to the container                       |

## ProductImageType Structure

```typescript
{
  mweb_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  web_image: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  actionUrl?: string; // Optional URL to make the product image clickable
}
```

## Usage Examples

### Basic Usage

```tsx
import ChooseProductSection from "@/components/Sections/ChooseProductSection";

<ChooseProductSection
  phrase="Pick your"
  erased="poison"
  visible="protein"
  firstProduct={{
    mweb_image: {
      alternativeText: "Product 1 mobile",
      url: "/images/banners/product_mobile_1.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: "Product 1 desktop",
      url: "/images/banners/product_desktop_1.png",
      mime: "image/png",
    },
  }}
  secondProduct={{
    mweb_image: {
      alternativeText: "Product 2 mobile",
      url: "/images/banners/product_mobile_2.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: "Product 2 desktop",
      url: "/images/banners/product_desktop_2.png",
      mime: "image/png",
    },
  }}
/>;
```

### With Product-Specific Action URLs

```tsx
<ChooseProductSection
  phrase="Choose your"
  erased="flavor"
  visible="taste"
  bgColor="#3A1D77"
  firstProduct={{
    mweb_image: {
      url: "/images/banners/flavor_mobile_1.png",
    },
    web_image: {
      url: "/images/banners/flavor_desktop_1.png",
    },
    actionUrl: "/products/flavor-1",
  }}
  secondProduct={{
    mweb_image: {
      url: "/images/banners/flavor_mobile_2.png",
    },
    web_image: {
      url: "/images/banners/flavor_desktop_2.png",
    },
    actionUrl: "/products/flavor-2",
  }}
/>
```

### With Action URL for the Entire Section

```tsx
<ChooseProductSection
  phrase="Pick your"
  erased="poison"
  visible="protein"
  bgColor="#772D4A"
  firstProduct={{
    mweb_image: {
      url: "/images/banners/product_mobile_1.png",
    },
    web_image: {
      url: "/images/banners/product_desktop_1.png",
    },
    actionUrl: "/products/protein-powder-15g",
  }}
  secondProduct={{
    mweb_image: {
      url: "/images/banners/product_mobile_2.png",
    },
    web_image: {
      url: "/images/banners/product_desktop_2.png",
    },
    actionUrl: "/products/protein-powder-24g",
  }}
  actionUrl="/products/protein-powder"
/>
```

## Notes

- The component automatically handles Strapi URLs by checking if the URL starts with "/uploads" and applying the `getStrapiUrl` utility function.
- Mobile images are displayed on screens smaller than the `lg` breakpoint, desktop images are displayed on `lg` and larger screens.
- The aspect ratio for desktop images is fixed at 16:9 (aspect-video) and mobile aspect ratio is fixed at 5:4.
- The StrikeThrough icon maintains the same styling and positioning as in the original design.
- Each product can have its own `actionUrl` property to make the product image clickable.
- If the section-level `actionUrl` is provided, the entire section becomes clickable and will navigate to the specified URL.
- Individual product action URLs will still work even when the section-level `actionUrl` is provided, as they are nested links.
