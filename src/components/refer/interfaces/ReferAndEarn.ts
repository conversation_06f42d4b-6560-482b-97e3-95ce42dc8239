// Interface automatically generated by schemas-to-ts

import { ReferAndEarnForm } from './ReferAndEarnForm';
import { ReferAndEarnThankYouPage } from './ReferAndEarnThankYouPage';
import { ReferAndEarnForm_Plain } from './ReferAndEarnForm';
import { ReferAndEarnThankYouPage_Plain } from './ReferAndEarnThankYouPage';
import { ReferAndEarnForm_NoRelations } from './ReferAndEarnForm';
import { ReferAndEarnThankYouPage_NoRelations } from './ReferAndEarnThankYouPage';

export interface ReferAndEarn {
  form?: ReferAndEarnForm;
  on_submit?: ReferAndEarnThankYouPage;
}
export interface ReferAndEarn_Plain {
  form?: ReferAndEarnForm_Plain;
  on_submit?: ReferAndEarnThankYouPage_Plain;
}

export interface ReferAndEarn_NoRelations {
  form?: ReferAndEarnForm_NoRelations;
  on_submit?: ReferAndEarnThankYouPage_NoRelations;
}

