// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ReferAndEarnThankYouPage {
  image?: { data: Media };
  description?: any;
  link?: string;
}
export interface ReferAndEarnThankYouPage_Plain {
  image?: Media_Plain;
  description?: any;
  link?: string;
}

export interface ReferAndEarnThankYouPage_NoRelations {
  image?: number;
  description?: any;
  link?: string;
}

