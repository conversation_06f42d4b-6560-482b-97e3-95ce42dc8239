// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ReferAndEarnForm {
  image?: { data: Media };
  title?: any;
}
export interface ReferAndEarnForm_Plain {
  image?: Media_Plain;
  title?: any;
}

export interface ReferAndEarnForm_NoRelations {
  image?: number;
  title?: any;
}

