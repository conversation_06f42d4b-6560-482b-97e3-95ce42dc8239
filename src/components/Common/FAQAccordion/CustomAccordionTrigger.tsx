"use client";

import * as React from "react";
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { ChevronDown } from "@/assets/icons/ChevronDown";
import { ChevronUp } from "@/assets/icons/ChevronUp";
import { cn } from "@/libs/utils";

interface CustomAccordionTriggerProps
  extends React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger> {
  iconColor?: string;
}

export const CustomAccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  CustomAccordionTriggerProps
>(({ className, children, iconColor = "#BE259A", ...props }, ref) => {
  const [isOpen, setIsOpen] = React.useState(false);

  // Create a ref to track the actual open state
  const triggerRef = React.useRef<HTMLButtonElement | null>(null);

  // Combine the refs
  const combinedRef = (node: HTMLButtonElement) => {
    // Apply the forwarded ref
    if (typeof ref === "function") {
      ref(node);
    } else if (ref) {
      ref.current = node;
    }

    // Apply our local ref
    triggerRef.current = node;
  };

  // Use an effect to check the actual state
  React.useEffect(() => {
    if (!triggerRef.current) return;

    // Create a mutation observer to watch for state changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (
          mutation.type === "attributes" &&
          mutation.attributeName === "data-state"
        ) {
          const newState = triggerRef.current?.getAttribute("data-state");
          setIsOpen(newState === "open");
        }
      });
    });

    // Start observing
    observer.observe(triggerRef.current, { attributes: true });

    // Initial state check
    const initialState = triggerRef.current.getAttribute("data-state");
    setIsOpen(initialState === "open");

    // Cleanup
    return () => observer.disconnect();
  }, []);

  return (
    <AccordionPrimitive.Header className="flex">
      <AccordionPrimitive.Trigger
        ref={combinedRef}
        className={cn(
          "flex flex-1 items-start justify-between gap-4 py-4 text-left transition-all outline-none",
          className
        )}
        {...props}
      >
        {children}
        <div className="flex items-center justify-center">
          {isOpen ? (
            <ChevronUp color={iconColor} className="shrink-0" />
          ) : (
            <ChevronDown color={iconColor} className="shrink-0" />
          )}
        </div>
      </AccordionPrimitive.Trigger>
    </AccordionPrimitive.Header>
  );
});

CustomAccordionTrigger.displayName = "CustomAccordionTrigger";

export default CustomAccordionTrigger;
