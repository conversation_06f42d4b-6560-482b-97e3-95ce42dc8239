import React from "react";
import Link from "next/link";
import StarRating from "@/components/Common/StarRating";
import { cn } from "@/libs/utils";

interface CompactReviewCardProps {
  name: string;
  review: string;
  rating: number;
  avatar?: string;
  className?: string;
  emoji?: string;
  productLinks?: Array<{
    text: string;
    href: string;
  }>;
}

const CompactReviewCard: React.FC<CompactReviewCardProps> = ({
  name,
  review,
  rating,
  className,
  emoji,
  productLinks = [],
}) => {
  // Function to replace product links and emoji in the review text
  const renderReviewWithLinks = () => {
    // If no product links, just handle emoji if present
    if (productLinks.length === 0) {
      return emoji ? (
        <>
          {review.split(emoji).map((part, i, arr) =>
            i < arr.length - 1 ? (
              <React.Fragment key={i}>
                {part}
                <span className="inline-block">{emoji}</span>
              </React.Fragment>
            ) : (
              part
            )
          )}
        </>
      ) : (
        review
      );
    }

    // Process both links and emoji
    const processedReview = review;
    const segments: React.ReactNode[] = [];
    let lastIndex = 0;

    // Sort links by their position in the text to process them in order
    const sortedLinks = [...productLinks].sort((a, b) => {
      return processedReview.indexOf(a.text) - processedReview.indexOf(b.text);
    });

    // Process each link
    sortedLinks.forEach((link) => {
      const linkIndex = processedReview.indexOf(link.text, lastIndex);
      if (linkIndex !== -1) {
        // Add text before the link, checking for emoji
        if (linkIndex > lastIndex) {
          const textBefore = processedReview.substring(lastIndex, linkIndex);
          if (emoji && textBefore.includes(emoji)) {
            const parts = textBefore.split(emoji);
            parts.forEach((part, i) => {
              if (i > 0) {
                segments.push(
                  <span
                    key={`emoji-${lastIndex}-${i}`}
                    className="inline-block"
                  >
                    {emoji}
                  </span>
                );
              }
              if (part) segments.push(part);
            });
          } else {
            segments.push(textBefore);
          }
        }

        // Add the link
        segments.push(
          <Link
            key={link.text}
            href={link.href}
            className="font-bold underline hover:text-gray-700 transition-colors"
          >
            {link.text}
          </Link>
        );

        lastIndex = linkIndex + link.text.length;
      }
    });

    // Add any remaining text, checking for emoji
    if (lastIndex < processedReview.length) {
      const textAfter = processedReview.substring(lastIndex);
      if (emoji && textAfter.includes(emoji)) {
        const parts = textAfter.split(emoji);
        parts.forEach((part, i) => {
          if (i > 0) {
            segments.push(
              <span key={`emoji-end-${i}`} className="inline-block">
                {emoji}
              </span>
            );
          }
          if (part) segments.push(part);
        });
      } else {
        segments.push(textAfter);
      }
    }

    return segments;
  };

  return (
    <div
      className={cn(
        "bg-white rounded-2xl shadow-lg p-4 max-w-md h-full flex flex-col",
        className
      )}
      style={{ boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)" }}
    >
      <div className="flex flex-col justify-between h-full">
        <div className="min-h-[80px] flex flex-col justify-between">
          <p className="text-xs font-medium mb-auto font-poppins">
            {renderReviewWithLinks()}
          </p>
          <div className="h-px w-full bg-[#A6A6A6] my-2"></div>
        </div>
        <div className="flex items-center mt-auto">
          {/* <div className="relative w-12 h-12 mr-3 overflow-hidden rounded-full border-2 border-gray-200">
            <Image src={avatar} alt={name} fill className="object-cover" />
          </div> */}
          <div>
            <p className="font-medium text-xs font-obviously">{name}</p>
            <div className="mt-1">
              <StarRating rating={rating} size="sm" className="gap-1" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompactReviewCard;
