"use client";

import { toast } from "sonner";
import { X } from "lucide-react";

interface CopyToastProps {
  message: string;
  actionText?: string;
  onAction?: () => void;
}

export function CopyToast({ message, actionText, onAction }: CopyToastProps) {
  // Dismiss all existing toasts first to ensure only one shows
  toast.dismiss();

  return toast.custom(
    (t) => (
      <div className="flex items-center justify-between bg-[#353535] text-white px-4 py-3 rounded-lg shadow-lg min-w-[300px] max-w-[500px]">
        <div className="flex items-center gap-3 flex-1">
          <span className="text-sm font-medium">{message}</span>
          {actionText && onAction && (
            <button
              onClick={() => {
                onAction();
                toast.dismiss(t);
              }}
              className="cursor-pointer text-orange-400 hover:text-orange-300 text-sm font-medium underline transition-colors"
            >
              {actionText}
            </button>
          )}
        </div>
        <button
          onClick={() => toast.dismiss(t)}
          className="text-white hover:text-gray-300 transition-colors ml-3"
        >
          <X size={16} className="cursor-pointer" />
        </button>
      </div>
    ),
    {
      duration: 5000,
      position: "bottom-center",
      className: "mb-12",
    }
  );
}
