"use client";

import { useState, useRef, useCallback } from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";
import { getStrapiUrl } from "@/utils/strapiUrl";

export interface FlipCardProps {
  frontImage: string;
  backImage: string;
  frontAlt?: string;
  backAlt?: string;
  width?: number;
  height?: number;
  className?: string;
  flipOnClick?: boolean;
}

export function FlipCard({
  frontImage,
  backImage,
  frontAlt = "Front image",
  backAlt = "Back image",
  width = 250,
  height = 368,
  className,
  flipOnClick = false,
}: FlipCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const handleFlip = () => {
    if (flipOnClick) {
      setIsFlipped(!isFlipped);
    }
  };

  // Robust hover detection using mouse position relative to card boundaries
  const handleMouseEnter = useCallback(() => {
    if (!flipOnClick) {
      // Clear any existing timeout
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
      setIsFlipped(true);
    }
  }, [flipOnClick]);

  const handleMouseLeave = useCallback(
    (e: React.MouseEvent) => {
      if (!flipOnClick && cardRef.current) {
        const rect = cardRef.current.getBoundingClientRect();
        const { clientX, clientY } = e;

        // Check if mouse is actually outside the card boundaries
        const isOutside =
          clientX < rect.left ||
          clientX > rect.right ||
          clientY < rect.top ||
          clientY > rect.bottom;

        if (isOutside) {
          // Add a small delay to prevent flickering on edge cases
          hoverTimeoutRef.current = setTimeout(() => {
            setIsFlipped(false);
          }, 50);
        }
      }
    },
    [flipOnClick]
  );

  // Additional mouse move handler to ensure we stay flipped when inside
  const handleMouseMove = useCallback(
    (e: React.MouseEvent) => {
      if (!flipOnClick && cardRef.current) {
        const rect = cardRef.current.getBoundingClientRect();
        const { clientX, clientY } = e;

        // Check if mouse is inside the card boundaries
        const isInside =
          clientX >= rect.left &&
          clientX <= rect.right &&
          clientY >= rect.top &&
          clientY <= rect.bottom;

        if (isInside) {
          // Clear any pending timeout and ensure card stays flipped
          if (hoverTimeoutRef.current) {
            clearTimeout(hoverTimeoutRef.current);
            hoverTimeoutRef.current = null;
          }
          if (!isFlipped) {
            setIsFlipped(true);
          }
        }
      }
    },
    [flipOnClick, isFlipped]
  );

  return (
    <div
      ref={cardRef}
      className={cn("relative w-[250px] h-[368px]", className)}
      style={{
        perspective: "1000px",
      }}
      onClick={handleFlip}
      onKeyDown={(e) => {
        if (flipOnClick && (e.key === "Enter" || e.key === " ")) {
          setIsFlipped(!isFlipped);
        }
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      tabIndex={flipOnClick ? 0 : -1}
      role={flipOnClick ? "button" : undefined}
      aria-pressed={flipOnClick ? isFlipped : undefined}
    >
      {/* Card container with 3D effect */}
      <div
        className="relative w-full h-full transition-transform duration-700"
        style={{
          transformStyle: "preserve-3d",
          transform: isFlipped ? "rotateY(180deg)" : "rotateY(0deg)",
        }}
      >
        {/* Front side */}
        <div className="absolute w-full h-full rounded-lg overflow-hidden backface-hidden">
          <Image
            src={
              getStrapiUrl(frontImage) ||
              frontImage ||
              "/placeholder.svg?height=400&width=300&text=Front"
            }
            alt={frontAlt}
            width={width}
            height={height}
            className="w-full h-full object-contain"
            priority
          />
        </div>

        {/* Back side */}
        <div
          className="absolute w-full h-full rounded-lg overflow-hidden backface-hidden"
          style={{
            transform: "rotateY(180deg)",
          }}
        >
          <Image
            src={
              getStrapiUrl(backImage) ||
              backImage ||
              "/placeholder.svg?height=400&width=300&text=Back"
            }
            alt={backAlt}
            width={width}
            height={height}
            className="w-full h-full object-contain"
          />
        </div>
      </div>
    </div>
  );
}
