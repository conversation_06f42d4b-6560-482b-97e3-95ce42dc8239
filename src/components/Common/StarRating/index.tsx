import React from "react";
import { Star } from "@/assets/icons/Star";
import { cn } from "@/libs/utils";

export interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: "sm" | "md" | "lg";
  className?: string;
  starClassName?: string;
}

const StarRating: React.FC<StarRatingProps> = ({
  rating,
  maxRating = 5,
  size = "md",
  className,
  starClassName,
}) => {
  // Calculate fill percentage for each star
  const getStarFillPercentage = (starPosition: number) => {
    const difference = rating - (starPosition - 1);

    if (difference >= 1) return "100%";
    if (difference <= 0) return "0%";

    return `${Math.round(difference * 100)}%`;
  };

  // Determine star size based on the size prop
  const getStarSize = () => {
    switch (size) {
      case "sm":
        return "h-3 w-3";
      case "lg":
        return "h-5 w-5";
      case "md":
      default:
        return "h-4.5 w-4.5";
    }
  };

  const starSize = getStarSize();

  return (
    <div className={cn("flex gap-[2.5px]", className)}>
      {Array.from({ length: maxRating }, (_, index) => index + 1).map(
        (star) => (
          <span
            key={star}
            className={cn("relative inline-block", starSize, starClassName)}
          >
            {/* Background star (gray/faded) */}
            <Star className={starSize} style={{ opacity: 0.3 }} />

            {/* Filled star overlay with dynamic width based on rating */}
            <span
              className="absolute inset-0 overflow-hidden"
              style={{
                width: getStarFillPercentage(star),
              }}
            >
              <Star className={starSize} />
            </span>
          </span>
        )
      )}
    </div>
  );
};

export default StarRating;
