import { getFooterDetails } from "@/libs/middlewareAPIs";
import FooterClient from "./FooterClient";

const Footer = async () => {
  try {
    const response = await getFooterDetails();
    // The response is already the FooterType object, use it directly
    const footerData = response;

    if (!footerData) return null;

    return <FooterClient footerData={footerData} />;
  } catch (error) {
    console.error("Error fetching footer data:", error);
    // Return the FooterClient with null data to show fallback content
    return <FooterClient footerData={null} />;
  }
};

export default Footer;
