"use client";

import Img from "@/components/Elements/img";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import React from "react";
import { FooterType } from "@/types/Collections/Footer";
import { getStrapiUrl } from "@/utils/strapiUrl";
import Image from "next/image";

interface FooterClientProps {
  footerData?: FooterType | null;
}

const FooterClient: React.FC<FooterClientProps> = ({ footerData }) => {
  return (
    <div className="bg-[#231F20] w-full text-white">
      <div className="container mx-auto py-12 px-4 md:px-6">
        <div className="max-w-6xl mx-auto flex flex-col lg:flex-row items-center lg:items-start justify-center gap-8">
          {/* Left Column Wrapper */}
          <div className="flex flex-col md:flex-row items-center gap-8 md:gap-6 md:flex-1">
            {/* Logo */}
            <div className="flex-shrink-0 mx-auto md:mx-0">
              <Img
                src={getStrapiUrl(footerData?.logo?.url) || ""}
                alt={footerData?.logo?.alternativeText || "Footer Logo"}
                width={134}
                height={134}
                className="w-[134px] h-[134px] object-contain"
              />
            </div>
            {/* Newsletter */}
            <div className="max-w-md flex flex-col mx-auto md:mx-0">
              <div>
                <div className="flex items-center gap-2 justify-center md:justify-start mb-1">
                  {footerData?.newsletter_icon?.url && (
                    <Image
                      src={getStrapiUrl(footerData.newsletter_icon.url)}
                      alt={
                        footerData.newsletter_icon.alternativeText ||
                        "Newsletter Icon"
                      }
                      width={24}
                      height={24}
                      className="w-6 h-6"
                      style={{ filter: "brightness(0) invert(1)" }} // Make SVG white
                    />
                  )}
                  <p className="font-obviously text-2xl md:text-sm font-bold text-center md:text-left">
                    {footerData?.newsletter_title || "Truth-seekers, unite!"}
                  </p>
                </div>
                <p className="text-sm max-w-2xl text-center md:text-left mt-2">
                  {footerData?.newsletter_description ||
                    "One, BS free, in-depth article about food & fitness. Delivered every saturday. Free!"}
                </p>
                <div className="relative mt-6 w-full max-w-md">
                  <input
                    className="bg-white text-black rounded-md py-3 px-4 w-full pr-[120px]"
                    placeholder="<EMAIL>"
                  />
                  <Button
                    className="absolute right-0 top-0 h-full rounded-l-none font-bold"
                    style={{ backgroundColor: "#CEFD7B", color: "#231F20" }}
                  >
                    COUNT ME IN
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - On mobile and tablet, this appears below with 2 columns */}
          <div className="mx-auto lg:mx-0 w-full lg:w-auto flex flex-col gap-8">
            {/* Social Icons - First on mobile/tablet, second on desktop */}
            <div className="flex justify-between items-center w-full max-w-xs mx-auto lg:mx-0 order-1 lg:order-2 mb-4 lg:mb-0">
              {/* Only render social media links from Strapi */}
              {footerData?.social_media?.map((social, index) => (
                <a
                  key={index}
                  href={social.image?.url ? getStrapiUrl(social.image.url) : ""}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={
                    social.image?.alternativeText || `Social Media ${index + 1}`
                  }
                >
                  <Img
                    src={
                      social.image?.url ? getStrapiUrl(social.image.url) : ""
                    }
                    alt={
                      social.image?.alternativeText ||
                      `Social Media ${index + 1}`
                    }
                    width={24}
                    height={24}
                    className="w-6 h-6 hover:text-[#CEFD7B] transition-colors"
                  />
                </a>
              ))}
            </div>

            {/* Links - Second on mobile/tablet, first on desktop */}
            <div className="font-obviously text-xs grid grid-cols-2 gap-8 lg:gap-10 text-center lg:text-left order-2 lg:order-1">
              {/* Map through each footer_links section */}
              {footerData?.footer_links?.map((section, sectionIndex) => (
                <div key={sectionIndex} className="flex flex-col gap-3">
                  {section.items && section.items.length > 0 ? (
                    section.items.map((link, index) => (
                      <Link
                        key={index}
                        href={link.action_link || "#"}
                        className="font-bold"
                      >
                        {link.text || `Link ${index + 1}`}
                      </Link>
                    ))
                  ) : (
                    <div>No links available</div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8 border-t border-white">
        <div className="flex items-center justify-center w-full">
          <p className="text-sm text-gray-400 text-center">
            © {new Date().getFullYear()} Fitshit Health Solutions. All rights
            reserved.
          </p>
        </div>
      </div>
    </div>
  );
};

export default FooterClient;
