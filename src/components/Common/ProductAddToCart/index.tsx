"use client";

import * as React from "react";
import { ChevronDownIcon, MinusIcon, PlusIcon } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/libs/utils";
import { useRouter } from "next/navigation";

type ProductAddToCartProps = {
  sizes?: string[];
  selectedSize?: string;
  onSizeChange?: (size: string) => void;
  onAddToCart: () => void;
  variant?: React.ComponentProps<typeof Button>["variant"];
  enableProductBg?: boolean;
  primaryColor?: string;
  buttonAction?: {
    type: string;
    redirectSlug?: string;
  };
};

export function ProductAddToCart({
  sizes,
  selectedSize: propSelectedSize,
  onSizeChange,
  onAddToCart,
  variant = "default",
  enableProductBg = false,
  primaryColor = "#93385D",
  buttonAction = { type: "addToCart" },
}: ProductAddToCartProps) {
  const router = useRouter();
  const hasSizes = sizes && sizes.length > 0;

  // Use a map to track counts and added state for each variant
  const [variantCounts, setVariantCounts] = React.useState<
    Record<string, number>
  >({});
  const [variantAdded, setVariantAdded] = React.useState<
    Record<string, boolean>
  >({});

  const [selectedSize, setSelectedSize] = React.useState(
    propSelectedSize || (sizes && sizes.length > 0 ? sizes[0] : "")
  );

  // Get current count and added state for the selected size or for non-variant products
  const variantKey = selectedSize || "default";
  const currentCount = variantCounts[variantKey] || 1;
  const isAdded = variantAdded[variantKey] || false;

  // Update local state when prop changes
  React.useEffect(() => {
    if (propSelectedSize) {
      // If the selected size is changing, just update the selected size
      if (selectedSize !== propSelectedSize) {
        setSelectedSize(propSelectedSize);
        console.log(`Selected size changed to: ${propSelectedSize}`);
      }
    }
  }, [propSelectedSize, selectedSize]);

  // Log when selected size changes
  React.useEffect(() => {
    if (selectedSize) {
      console.log(`Selected size changed to: ${selectedSize}`);
      console.log(`Current count: ${currentCount}, isAdded: ${isAdded}`);
    }
  }, [selectedSize, currentCount, isAdded]);

  const handleAddToCart = () => {
    if (buttonAction.type === "buildYourOwnBox" && buttonAction.redirectSlug) {
      // Redirect to the specified slug
      router.push(buttonAction.redirectSlug);
    } else {
      // Default "Add to Cart" behavior
      setVariantAdded((prev) => ({
        ...prev,
        [variantKey]: true,
      }));
      onAddToCart();
    }
  };

  const handleIncrement = () => {
    // Increment the count for the current variant or default
    setVariantCounts((prev) => ({
      ...prev,
      [variantKey]: (prev[variantKey] || 1) + 1,
    }));
  };

  const handleDecrement = () => {
    const currentVariantCount = variantCounts[variantKey] || 1;

    if (currentVariantCount > 1) {
      // Decrement the count for the current variant or default
      setVariantCounts((prev) => ({
        ...prev,
        [variantKey]: currentVariantCount - 1,
      }));
    } else {
      // Reset the added state for the current variant or default
      setVariantAdded((prev) => ({
        ...prev,
        [variantKey]: false,
      }));
    }
  };

  return (
    <div className="flex w-full ">
      {hasSizes && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                buttonVariants({ size: "default" }),
                "rounded-r-none"
              )}
              style={
                enableProductBg
                  ? {
                      borderColor: "white",
                      color: "white",
                      backgroundColor: "transparent",
                    }
                  : {
                      borderColor: primaryColor,
                      color: primaryColor,
                      backgroundColor: "white",
                    }
              }
            >
              {selectedSize}
              <ChevronDownIcon
                className="-me-1 opacity-60"
                size={16}
                aria-hidden="true"
              />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="center">
            {sizes.map((size) => (
              <DropdownMenuItem
                key={size}
                onSelect={() => {
                  // Update the selected size
                  setSelectedSize(size);

                  // Call the parent's onSizeChange callback
                  if (onSizeChange) {
                    onSizeChange(size);
                  }

                  // Initialize the count for this variant if it doesn't exist
                  if (!(size in variantCounts)) {
                    setVariantCounts((prev) => ({
                      ...prev,
                      [size]: 1,
                    }));
                  }

                  // Initialize the added state for this variant if it doesn't exist
                  if (!(size in variantAdded)) {
                    setVariantAdded((prev) => ({
                      ...prev,
                      [size]: false,
                    }));
                  }

                  console.log(
                    `Switched to variant: ${size}, count: ${
                      variantCounts[size] || 1
                    }, isAdded: ${variantAdded[size] || false}`
                  );
                }}
                style={{ color: primaryColor }}
                className="hover:bg-gray-50"
              >
                {size}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
      {!isAdded ? (
        <Button
          onClick={handleAddToCart}
          variant={variant}
          className={cn(
            "font-obviously ",
            hasSizes ? "rounded-l-none " : "w-full"
          )}
        >
          {buttonAction.type === "buildYourOwnBox"
            ? "BUILD YOUR OWN BOX"
            : "ADD TO CART"}
        </Button>
      ) : (
        <div
          className={cn(
            "flex items-center h-9 rounded-md w-full",
            hasSizes ? "rounded-l-none" : ""
          )}
          style={
            enableProductBg
              ? {
                  backgroundColor: "white",
                  border: `1px solid ${primaryColor}`,
                }
              : { backgroundColor: primaryColor }
          }
        >
          <Button
            variant={variant}
            size="icon"
            className="h-full rounded-none px-3 flex-1"
            style={
              enableProductBg
                ? { color: primaryColor, backgroundColor: "transparent" }
                : {}
            }
            onClick={handleDecrement}
          >
            <MinusIcon size={16} />
          </Button>
          <div
            className="flex items-center justify-center h-full px-3 font-medium flex-1"
            style={{
              color: "white",
              backgroundColor: enableProductBg ? primaryColor : "transparent",
              border: enableProductBg ? "1px solid white" : "none",
              margin: enableProductBg ? "0 2px" : "0",
              borderRadius: enableProductBg ? "4px" : "0",
            }}
          >
            {currentCount}
          </div>
          <Button
            variant={variant}
            size="icon"
            className="h-full rounded-none px-3 flex-1"
            style={
              enableProductBg
                ? { color: primaryColor, backgroundColor: "transparent" }
                : {}
            }
            onClick={handleIncrement}
          >
            <PlusIcon size={16} />
          </Button>
        </div>
      )}
    </div>
  );
}

export default ProductAddToCart;
