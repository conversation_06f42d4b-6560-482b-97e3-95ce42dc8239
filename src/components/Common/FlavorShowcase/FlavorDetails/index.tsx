"use client";

import React, { useState } from "react";
import { cn } from "@/libs/utils";
import { PlusIcon } from "lucide-react";

interface FlavorDetailsProps {
  name: string;
  protein: string;
  description: string;
  ingredients: string;
  usageDescription: string;
  className?: string;
}

const FlavorDetails: React.FC<FlavorDetailsProps> = ({
  name,
  protein,
  description,
  ingredients,
  usageDescription,
  className,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div
      className={cn(
        "flex flex-col w-full px-6 py-[11.5px] text-black bg-white",
        "md:w-1/2",
        className
      )}
    >
      {/* Header - Always visible on both mobile and desktop */}
      <div className={`md:mb-4 flex justify-between items-center`}>
        <h3 className="text-sm md:text-base font-obviously font-semibold">
          {name} <span className="font-normal font-obviously">({protein})</span>
        </h3>
        {/* Plus icon only visible on mobile */}
        <button
          onClick={toggleExpand}
          className="md:hidden flex items-center justify-center rounded-full bg-[#c47c5a] w-8 h-8 transition-transform duration-300"
          aria-label={isExpanded ? "Collapse details" : "Expand details"}
        >
          <PlusIcon
            size={20}
            color="white"
            className={`transition-transform duration-300 ${
              isExpanded ? "rotate-45" : ""
            }`}
          />
        </button>
      </div>

      {/* Desktop view - always visible on md and above */}
      <div className="hidden md:block">
        <p className="text-sm font-obviously">{description}</p>
        <br />
        <p className="text-sm font-obviously">{ingredients}</p>
        <br />
        <p className="italic text-sm font-obviously">{usageDescription}</p>
      </div>

      {/* Mobile view - accordion content */}
      <div
        className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out`}
        style={{
          maxHeight: isExpanded ? "1000px" : "0",
          opacity: isExpanded ? 1 : 0,
          marginTop: isExpanded ? "1rem" : "0",
        }}
      >
        <div>
          <p className="text-sm font-obviously">{description}</p>
          <br />
          <p className="text-sm font-obviously">{ingredients}</p>
          <br />
          <p className="italic text-sm font-obviously">{usageDescription}</p>
        </div>
      </div>
    </div>
  );
};

export default FlavorDetails;
