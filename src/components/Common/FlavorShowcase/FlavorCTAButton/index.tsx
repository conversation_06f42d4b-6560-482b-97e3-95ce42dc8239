import React from "react";
import Link from "next/link";
import { cn } from "@/libs/utils";

interface FlavorCTAButtonProps {
  text: string;
  link: string;
  className?: string;
}

const FlavorCTAButton: React.FC<FlavorCTAButtonProps> = ({
  text,
  link,
  className,
}) => {
  return (
    <div className={cn("flex justify-center mb-6.5", className)}>
      <Link
        href={link}
        className={cn(
          "bg-white text-black font-bold text-xs uppercase tracking-wide",
          "px-4 py-2 rounded-lg border-2 border-black",
          "shadow-[-3px_3px_0px_0px_rgba(0,0,0,1)]",
          "hover:shadow-[-1px_1px_0px_0px_rgba(0,0,0,1)] hover:translate-x-[-1px] hover:translate-y-[1px]",
          "transition-all duration-200 ease-in-out"
        )}
      >
        {text}
      </Link>
    </div>
  );
};

export default FlavorCTAButton;
