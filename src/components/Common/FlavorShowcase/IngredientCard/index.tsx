import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";

interface IngredientCardProps {
  type: string;
  percentage: number;
  image?: string;
  className?: string;
}

const IngredientCard: React.FC<IngredientCardProps> = ({
  type,
  percentage,
  image,
  className,
}) => {
  return (
    <div
      className={cn(
        "h-[173px] border border-black bg-white rounded-lg px-[15.5px] py-1.5 flex flex-col items-start text-black min-w-[200px] shadow-[-4.22px_5.06px_0px_0px_rgba(0,0,0,0.4)]",
        className
      )}
    >
      <div className="w-full">
        <div className="text-base font-semibold font-obviously flex justify-between items-center mb-2.5">
          <h4>{type}</h4>
          <span>{percentage}%</span>
        </div>
        <div className="ingredients-divider bg-gradient-to-r from-[rgb(196,124,90)] to-transparent h-[2.5px] w-full rounded-full mb-3.5" />
      </div>
      {image && (
        <div className="w-full flex justify-center">
          <div className="relative w-full h-[110px]">
            <Image src={image} alt={type} fill className="object-contain" />
          </div>
        </div>
      )}
    </div>
  );
};

export default IngredientCard;
