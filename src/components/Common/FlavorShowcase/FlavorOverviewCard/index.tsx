import React from "react";
import { cn } from "@/libs/utils";
import { FlavorType } from "../../FlavorShowcase";
import ProductVisual from "../ProductVisual";
import FlavorDetails from "../FlavorDetails";

interface FlavorOverviewCardProps {
  flavor: FlavorType;
  className?: string;
}

const FlavorOverviewCard: React.FC<FlavorOverviewCardProps> = ({
  flavor,
  className,
}) => {
  return (
    <div
      className={cn(
        "border border-white mb-8 flex flex-col md:flex-row max-w-[800px] w-full mx-auto rounded-[21px] overflow-hidden",
        className
      )}
    >
      <ProductVisual productImage={flavor.images.product} />
      <FlavorDetails
        name={flavor.name}
        protein={flavor.protein}
        description={flavor.description}
        ingredients={flavor.ingredients}
        usageDescription={flavor.usageDescription}
      />
    </div>
  );
};

export default FlavorOverviewCard;
