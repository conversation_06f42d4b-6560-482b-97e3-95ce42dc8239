import React from "react";
import { cn } from "@/libs/utils";

interface IngredientsHeaderProps {
  className?: string;
  heading?: string;
  subheading?: string;
}

const IngredientsHeader: React.FC<IngredientsHeaderProps> = ({
  className,
  heading = "Just 5 ingredients",
  subheading = "all add upto 100%",
}) => {
  return (
    <div className={cn("text-center mb-6 space-y-3", className)}>
      <h1 className="font-narrow text-center text-4xl font-semibold">
        {heading}
      </h1>
      <p className="font-gooddog text-center text-[22px] mb-5 font-gooddog">
        {subheading}
      </p>
    </div>
  );
};

export default IngredientsHeader;
