"use client";

import React, { useState } from "react";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/libs/utils";
import { TabLine } from "@/assets/icons/TabLine";

interface TabItem {
  value: string;
  label: string;
  content: React.ReactNode;
  primaryColor?: string; // Each tab can have its own primary color
  bgColor?: string; // Background color for the tab content section
}

interface ScrollableTabsProps {
  tabs: TabItem[];
  defaultValue?: string;
  className?: string;
  tabsListClassName?: string;
  tabsTriggerClassName?: string;
  tabsContentClassName?: string;
  activeTabBgColor?: string; // Fallback color if tab doesn't specify primaryColor
  onTabChange?: (color: string) => void; // Callback when tab changes to pass the color
}

export function ScrollableTabs({
  tabs,
  defaultValue,
  className,
  tabsListClassName,
  tabsTriggerClassName,
  tabsContentClassName,
  activeTabBgColor = "#FFFFFF", // Default to white if not specified
  onTabChange,
}: ScrollableTabsProps) {
  // Use the first tab as default if not specified
  const defaultTab = defaultValue || tabs[0]?.value;

  // Track the currently active tab color
  const [activeColor, setActiveColor] = useState<string>(
    tabs.find((tab) => tab.value === defaultTab)?.primaryColor ||
      activeTabBgColor
  );

  // Handle tab change to update the active color
  const handleTabChange = (value: string) => {
    const selectedTab = tabs.find((tab) => tab.value === value);
    const newColor = selectedTab?.primaryColor || activeTabBgColor;

    setActiveColor(newColor);

    // Call the onTabChange callback if provided
    if (onTabChange) {
      // Use bgColor if available, otherwise use primaryColor
      const bgColor =
        selectedTab?.bgColor || selectedTab?.primaryColor || activeTabBgColor;
      onTabChange(bgColor);
    }
  };

  return (
    <Tabs
      defaultValue={defaultTab}
      className={cn("w-full", className)}
      onValueChange={handleTabChange}
    >
      {/* Scrollable tabs in a carousel */}
      <div className="mb-4">
        <UniversalCarousel useNativeScrollbar={true} className="pb-2">
          <TabsList
            className={cn(
              "flex h-[40px] items-center justify-start bg-transparent p-1",
              tabsListClassName
            )}
            style={{ "--active-tab-color": activeColor } as React.CSSProperties}
          >
            {tabs.map((tab, index) => (
              <React.Fragment key={tab.value}>
                {index > 0 && (
                  <div className="flex items-center justify-center h-[calc(100%-2px)] mx-2">
                    <TabLine color={activeColor} className="h-full" />
                  </div>
                )}
                <TabsTrigger
                  value={tab.value}
                  className={cn(
                    "font-narrow font-semibold px-[48px] py-1.5 h-[36px] whitespace-nowrap text-base uppercase border rounded-md flex items-center justify-center",
                    "data-[state=active]:bg-[var(--active-tab-color,#ffffff)] data-[state=active]:text-white data-[state=active]:border-transparent data-[state=active]:shadow-md",
                    "data-[state=inactive]:text-[var(--active-tab-color,#ffffff)] data-[state=inactive]:border-[var(--active-tab-color,#ffffff)] data-[state=inactive]:border-2 data-[state=inactive]:bg-transparent",
                    "hover:data-[state=inactive]:bg-white/10",
                    tabsTriggerClassName
                  )}
                >
                  {tab.label}
                </TabsTrigger>
              </React.Fragment>
            ))}
          </TabsList>
        </UniversalCarousel>
      </div>

      {/* Tab content displayed normally below */}
      <div className={cn("mt-2", tabsContentClassName)}>
        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
      </div>
    </Tabs>
  );
}

export default ScrollableTabs;
