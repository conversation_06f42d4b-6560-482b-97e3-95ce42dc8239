"use client";

import React from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { logoutUser } from "@/redux/actions/auth.actions";
import { UserIcon } from "@/assets/icons/User";
import AuthDialog from "@/components/Auth/AuthDialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const UserMenu: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);

  const handleLogout = () => {
    dispatch(logoutUser());
  };

  if (!isAuthenticated) {
    return <AuthDialog />;
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="flex items-center justify-center">
        <UserIcon />
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="p-2 border-b">
          <p className="font-medium">Hello, {user?.name || "User"}</p>
          <p className="text-sm text-gray-500">{user?.phone}</p>
        </div>
        <DropdownMenuItem asChild>
          <a href="/account" className="cursor-pointer">
            My Account
          </a>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <a href="/orders" className="cursor-pointer">
            My Orders
          </a>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserMenu;
