'use client';

import { MegaMenu, MegaMenuList } from '@/components/Features/MegaMenu';
import { useEffect, useState } from 'react';
import MenuItem from './MenuItem';
import { MenuType } from '@/types/Components/Common/Menu';

const MegaMenuComponent: React.FC<{ menuData: MenuType[] }> = ({ menuData }) => {
  const [activeSubMenus, setActiveSubMenus] = useState<{
    [key: string]: string | null;
  }>({});

  useEffect(() => {
    const initialActiveSubMenus: { [key: string]: string | null } = {};
    menuData?.forEach((item) => {
      if (item?.sub_menu && Array.isArray(item.sub_menu) && item.sub_menu.length > 0) {
        initialActiveSubMenus[item.title ?? ''] = item.sub_menu[0]?.title ?? null;
      }
    });
    setActiveSubMenus(initialActiveSubMenus);
  }, [menuData]);

  const handleSubMenuEnter = (menuId: string | null, subMenuId: string | null) => {
    if (menuId) {
      setActiveSubMenus((prev) => ({ ...prev, [menuId]: subMenuId ?? menuId }));
    }
  };

  return (
    <MegaMenu viewportContainerClassName="left-[50%] mt-6 w-screen -translate-x-1/2 bg-white">
      <MegaMenuList className="hidden gap-4 md:flex">
        {menuData?.map((item, index) => {
          if (!item.show) return null;
          return (
            <MenuItem
              key={index}
              data={item}
              index={index}
              activeSubMenus={activeSubMenus}
              handleSubMenuEnter={handleSubMenuEnter}
            />
          );
        })}
      </MegaMenuList>
    </MegaMenu>
  );
};

export default MegaMenuComponent;
