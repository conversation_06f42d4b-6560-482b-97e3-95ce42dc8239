// components/ProductInfoHeader.tsx
"use client";

import React from "react";

type ProductInfoHeaderProps = {
  title: string;
  hashtag: string;
  description: string;
};

const ProductInfoHeader: React.FC<ProductInfoHeaderProps> = ({
  title,
  hashtag,
  description,
}) => {
  return (
    <div className="px-4 max-w-3xl">
      <div className="space-y-2">
        <p className="text-[28px] md:text-4xl font-bold font-narrow ">
          {title}
        </p>
        <p className="text-2xl font-normal text-[#000000] font-gooddog">
          {hashtag}
        </p>
        <p className="text-base font-normal font-obviously text-gray-800">{`"${description}"`}</p>
      </div>
    </div>
  );
};

export default ProductInfoHeader;
