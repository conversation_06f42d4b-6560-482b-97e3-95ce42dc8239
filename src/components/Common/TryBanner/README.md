# TryBanner Component

A reusable banner component that displays different images for mobile and desktop viewports, specifically designed for "Try" promotional banners.

## Features

- Responsive design with separate images for mobile and desktop
- Optional link functionality
- Automatic handling of Strapi image URLs
- Predefined aspect ratios optimized for promotional content

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `image` | `object` | Required | Object containing mobile and desktop image data |
| `href` | `string` | `undefined` | Optional URL to make the banner clickable |
| `className` | `string` | `undefined` | Additional CSS classes to apply to the container |

## Image Object Structure

```typescript
{
  id?: string;
  mweb_image?: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  web_image?: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
}
```

## Usage Examples

### Basic Usage

```tsx
import TryBanner from "@/components/Common/TryBanner";

const bannerImage = {
  id: "16",
  mweb_image: {
    alternativeText: null,
    url: "/uploads/mango_mobile_db1d6ca1fa.png",
    mime: "image/png"
  },
  web_image: {
    alternativeText: null,
    url: "/uploads/mango_desktop_ef4a940479.jpg",
    mime: "image/jpeg"
  }
};

<TryBanner image={bannerImage} />
```

### With Link

```tsx
<TryBanner 
  image={bannerImage} 
  href="/products/protein-bars" 
/>
```

### With Custom Class

```tsx
<TryBanner 
  image={bannerImage} 
  className="my-8"
/>
```

## Notes

- The component automatically handles Strapi URLs by checking if the URL starts with "/uploads" and applying the `getStrapiUrl` utility function.
- Mobile image is displayed on screens smaller than the `lg` breakpoint, desktop image is displayed on `lg` and larger screens.
- If a URL is provided via the `href` prop, the entire banner becomes clickable.
- Desktop aspect ratio is fixed at 5.5:1 and mobile aspect ratio is fixed at 21:8.
