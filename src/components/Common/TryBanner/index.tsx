"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { getStrapiUrl } from "@/utils/strapiUrl";

export interface TryBannerProps {
  image: {
    id?: string;
    mweb_image?: {
      alternativeText?: string | null;
      url: string;
      mime?: string;
    };
    web_image?: {
      alternativeText?: string | null;
      url: string;
      mime?: string;
    };
  };
  href?: string;
  className?: string;
}

const TryBanner: React.FC<TryBannerProps> = ({ image, href, className }) => {
  const BannerContent = () => (
    <>
      {/* Desktop image - hidden on small screens, visible on larger screens */}
      <div className="aspect-[5.5/1] relative w-full hidden lg:block">
        {image.web_image?.url && (
          <Image
            src={
              image.web_image.url.startsWith("/uploads")
                ? getStrapiUrl(image.web_image.url)
                : image.web_image.url
            }
            alt={image.web_image.alternativeText || "Banner image desktop"}
            fill
            style={{ objectFit: "cover" }}
            priority
          />
        )}
      </div>

      {/* Mobile image - visible on small screens, hidden on larger screens */}
      <div className="aspect-[21/8] relative w-full block lg:hidden">
        {image.mweb_image?.url && (
          <Image
            src={
              image.mweb_image.url.startsWith("/uploads")
                ? getStrapiUrl(image.mweb_image.url)
                : image.mweb_image.url
            }
            alt={image.mweb_image.alternativeText || "Banner image mobile"}
            fill
            style={{ objectFit: "cover" }}
            priority
          />
        )}
      </div>
    </>
  );

  return (
    <div className={cn("w-full", className)}>
      {href ? (
        <Link href={href} className="block w-full">
          <BannerContent />
        </Link>
      ) : (
        <BannerContent />
      )}
    </div>
  );
};

export default TryBanner;
