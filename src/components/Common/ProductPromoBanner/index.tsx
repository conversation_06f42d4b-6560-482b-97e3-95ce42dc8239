"use client";

import React from "react";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface ProductPromoBannerProps {
  imageSrc: string;
  imageAlt?: string;
  title: string;
  subtitle: string;
  actionUrl: string;
  buttonText?: string;
}

const ProductPromoBanner: React.FC<ProductPromoBannerProps> = ({
  imageSrc,
  imageAlt = "Product Image",
  title,
  subtitle,
  actionUrl,
  buttonText = "Shop Now",
}) => {
  const router = useRouter();

  const handleButtonClick = () => {
    router.push(actionUrl);
  };

  return (
    <div className="py-5 px-12 flex items-center justify-between bg-white my-6 shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.12)] rounded-lg">
      {/* left section product info */}
      <div className="flex gap-4 items-center lg:justify-normal justify-between flex-grow">
        <div className="relative aspect-square w-full max-w-[420px] lg:w-[100px]">
          <Image src={imageSrc} alt={imageAlt} fill className="object-cover" />
        </div>
        <div>
          <h3 className="font-narrow text-xs lg:text-[32px] font-semibold mb-2">
            {title}
          </h3>
          <p className="text-xs lg:text-base text-[#535353] font-obviously font-normal mb-1">
            {subtitle}
          </p>
          <div className="block lg:hidden mt-4">
            <Link
              href={actionUrl}
              className="inline-block font-obviously font-semibold text-[10px] leading-4 text-[#535353] border-b border-[#535353] uppercase  rounded-none"
            >
              {buttonText}
            </Link>
          </div>
        </div>
      </div>
      {/* right section button */}
      <div className="hidden lg:block">
        <Button
          onClick={handleButtonClick}
          className=" bg-white border hover:bg-white cursor-pointer border-black shadow-[-5px_5px_0_0_rgba(0,0,0,0.5)] font-obviously font-semibold text-xs leading-8 flex items-center justify-center uppercase text-black rounded-[10px] w-[181px] h-[44px]"
        >
          {buttonText}
        </Button>
      </div>
    </div>
  );
};

export default ProductPromoBanner;
