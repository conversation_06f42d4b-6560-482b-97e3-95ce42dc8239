# SingleBanner Component

A reusable banner component that displays different images for mobile and desktop viewports.

## Features

- Responsive design with separate images for mobile and desktop
- Optional link functionality
- Customizable aspect ratio
- Configurable object-fit property
- Automatic handling of Strapi image URLs

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `image` | `object` | Required | Object containing mobile and desktop image data |
| `href` | `string` | `undefined` | Optional URL to make the banner clickable |
| `className` | `string` | `undefined` | Additional CSS classes to apply to the container |
| `aspectRatio` | `string` | `"aspect-square lg:aspect-[3/1]"` | Tailwind CSS classes for aspect ratio |
| `objectFit` | `"fill" \| "cover" \| "contain"` | `"fill"` | CSS object-fit property for the images |

## Image Object Structure

```typescript
{
  id?: string;
  mweb_image?: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
  web_image?: {
    alternativeText?: string | null;
    url: string;
    mime?: string;
  };
}
```

## Usage Examples

### Basic Usage

```tsx
import SingleBanner from "@/components/Common/SingleBanner";

const bannerImage = {
  id: "16",
  mweb_image: {
    alternativeText: null,
    url: "/uploads/mango_mobile_db1d6ca1fa.png",
    mime: "image/png"
  },
  web_image: {
    alternativeText: null,
    url: "/uploads/mango_desktop_ef4a940479.jpg",
    mime: "image/jpeg"
  }
};

<SingleBanner image={bannerImage} />
```

### With Link

```tsx
<SingleBanner 
  image={bannerImage} 
  href="/products/protein-bars" 
/>
```

### Custom Aspect Ratio

```tsx
<SingleBanner 
  image={bannerImage} 
  aspectRatio="aspect-[4/3] lg:aspect-[16/9]" 
/>
```

### Custom Object Fit

```tsx
<SingleBanner 
  image={bannerImage} 
  objectFit="contain"
  className="bg-gray-100" 
/>
```

## Notes

- The component automatically handles Strapi URLs by checking if the URL starts with "/uploads" and applying the `getStrapiUrl` utility function.
- Mobile image is displayed on screens smaller than the `lg` breakpoint, desktop image is displayed on `lg` and larger screens.
- If a URL is provided via the `href` prop, the entire banner becomes clickable.
