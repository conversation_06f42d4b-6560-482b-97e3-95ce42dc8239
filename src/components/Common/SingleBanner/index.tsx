"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { getStrapiUrl } from "@/utils/strapiUrl";

export interface SingleBannerProps {
  image: {
    id?: string;
    mweb_image?: {
      alternativeText?: string | null;
      url: string;
      mime?: string;
    };
    web_image?: {
      alternativeText?: string | null;
      url: string;
      mime?: string;
    };
  };
  href?: string;
  className?: string;
  aspectRatio?: string;
  objectFit?: "fill" | "cover" | "contain";
}

const SingleBanner: React.FC<SingleBannerProps> = ({
  image,
  href,
  className,
  aspectRatio = "aspect-square lg:aspect-[3/1]",
  objectFit = "fill",
}) => {
  const BannerContent = () => (
    <div className={cn(aspectRatio, "w-full relative")}>
      {image.web_image?.url && (
        <Image
          src={
            image.web_image.url.startsWith("/uploads")
              ? getStrapiUrl(image.web_image.url)
              : image.web_image.url
          }
          alt={image.web_image.alternativeText || "Banner image desktop"}
          fill
          style={{ objectFit }}
          priority
          className="hidden lg:block"
        />
      )}
      {image.mweb_image?.url && (
        <Image
          src={
            image.mweb_image.url.startsWith("/uploads")
              ? getStrapiUrl(image.mweb_image.url)
              : image.mweb_image.url
          }
          alt={image.mweb_image.alternativeText || "Banner image mobile"}
          fill
          style={{ objectFit }}
          priority
          className="block lg:hidden"
        />
      )}
    </div>
  );

  return (
    <div className={cn("w-full relative", className)}>
      {href ? (
        <Link href={href} className="block w-full">
          <BannerContent />
        </Link>
      ) : (
        <BannerContent />
      )}
    </div>
  );
};

export default SingleBanner;
