import React from "react";
import { cn } from "@/libs/utils";

interface SectionHeadingProps {
  title?: string;
  color?: string;
  icon?: React.ReactNode;
  iconColor?: string;
  iconPosition?: "left" | "right" | "top";
  className?: string;
  iconClassName?: string;
}

const SeactionHeading: React.FC<SectionHeadingProps> = ({
  title = "Protein Powders",
  color = "#1a181e",
  icon,
  iconColor,
  iconPosition = "right",
  className,
  iconClassName,
}) => {
  // Function to render the icon with the appropriate color and size
  const renderIcon = () => {
    if (!icon) return null;

    // If it's a React element (like an SVG), clone it and apply the className and color
    if (React.isValidElement(icon)) {
      // Clone the element with the new props
      return React.cloneElement(icon, {
        // We need to use type assertion to avoid TypeScript errors
        className: iconClassName,
        color: iconColor || color,
        fill: iconColor || color,
      } as React.Attributes);
    }

    // If it's not a React element, wrap it in a div
    return (
      <div
        className={cn("inline-flex items-center justify-center", iconClassName)}
        style={{ color: iconColor || color }}
      >
        {icon}
      </div>
    );
  };

  // Determine the layout based on icon position
  const getLayout = () => {
    switch (iconPosition) {
      case "left":
        return "flex flex-row items-center justify-center gap-2";
      case "right":
        return "flex flex-row items-center justify-center gap-2";
      case "top":
        return "flex flex-col items-center justify-center gap-2";
      default:
        return "flex flex-row items-center justify-center gap-2";
    }
  };

  return (
    <div className={cn("text-center", className)}>
      <div
        className={cn("lg:text-4xl text-[28px] font-semibold", getLayout())}
        style={{ color: color }}
      >
        {iconPosition === "left" && renderIcon()}
        {iconPosition === "top" && renderIcon()}
        <span>{title}</span>
        {iconPosition === "right" && renderIcon()}
      </div>
    </div>
  );
};

export default SeactionHeading;
