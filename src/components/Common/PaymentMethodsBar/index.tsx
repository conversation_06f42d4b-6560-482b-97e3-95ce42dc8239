import React from "react";
import Img from "@/components/Elements/img";

export interface PaymentMethod {
  id: string;
  name: string;
  imageSrc: string;
}

export interface PaymentMethodsBarProps {
  /** Prefix text before payment methods */
  prefixText?: string;
  /** Suffix text after payment methods */
  suffixText?: string;
  /** Array of payment methods to display */
  paymentMethods?: PaymentMethod[];
}

/**
 * PaymentMethodsBar - A component to display available payment methods
 */
const PaymentMethodsBar: React.FC<PaymentMethodsBarProps> = ({
  prefixText = "Securely pay using:",
  suffixText = "Oh! and Cash on delivery too :)",
  paymentMethods = defaultPaymentMethods,
}) => {
  return (
    <div className="py-3.5 flex justify-center items-center bg-white shadow-[0_-4px_6px_-1px_rgba(0,0,0,0.1)]">
      <div className="flex flex-col md:flex-row flex-wrap items-center gap-4">
        {prefixText && (
          <p className="text-sm text-black font-bold text-center">
            {prefixText}
          </p>
        )}

        <div className="flex justify-center items-center gap-5">
          {paymentMethods.map((method) => (
            <Img
              key={method.id}
              src={method.imageSrc}
              alt={method.name}
              width={64}
              height={32}
              className="h-8 w-auto object-contain"
            />
          ))}
        </div>

        {suffixText && (
          <p className="text-base font-bold text-center text-[#93385D]">
            {suffixText}
          </p>
        )}
      </div>
    </div>
  );
};

// Default payment methods
const defaultPaymentMethods: PaymentMethod[] = [
  { id: "paytm", name: "Paytm", imageSrc: "/paytm.png" },
  { id: "visa", name: "Visa", imageSrc: "/visa.png" },
  {
    id: "mastercard",
    name: "Mastercard",
    imageSrc: "/mastercard.png",
  },
  { id: "maestro", name: "Maestro", imageSrc: "/maestro.png" },
  {
    id: "american-express",
    name: "American Express",
    imageSrc: "/american-express.png",
  },
];

export default PaymentMethodsBar;
