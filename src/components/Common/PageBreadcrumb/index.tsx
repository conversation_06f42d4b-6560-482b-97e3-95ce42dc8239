import {
  B<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  B<PERSON><PERSON>rumbItem,
  Bread<PERSON>rumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { cn } from "@/libs/utils";
import Link from "next/link";
import React, { FC } from "react";

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface PageBreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

const PageBreadcrumb: FC<PageBreadcrumbProps> = ({ items, className }) => {
  return (
    <Breadcrumb className={cn("mb-2.5 px-4 pt-2.5 container-box", className)}>
      <BreadcrumbList className="gap-0 sm:gap-0 text-base md:text-lg">
        {items.map((item, index) => (
          <React.Fragment key={index}>
            <BreadcrumbItem className="ml-1 mt-1">
              <BreadcrumbLink
                className="cursor-pointer font-bold text-black hover:underline"
                asChild
              >
                <Link href={item.href || "#"}>{item.label}</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="mt-1 ml-1 font-bold text-black ">
              /
            </BreadcrumbSeparator>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default PageBreadcrumb;
