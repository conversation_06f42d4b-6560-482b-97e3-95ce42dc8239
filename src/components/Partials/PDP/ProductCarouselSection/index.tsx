"use client";

import React from "react";
import ProductCarousel from "@/components/Partials/PDP/ProductCarousel";

/**
 * ProductCarouselSection Component
 *
 * Wrapper component for the product image carousel with proper styling
 * and layout structure.
 *
 * @param images - Array of image URLs for the carousel
 * @param title - Product title for alt text
 * @param currentSlide - Currently active slide index
 * @param onSlideChange - Callback function when slide changes
 * @param primaryColor - Primary color for carousel navigation
 */
interface ProductCarouselSectionProps {
  images: string[];
  title: string;
  currentSlide: number;
  onSlideChange: (index: number) => void;
  primaryColor?: string;
}

const ProductCarouselSection: React.FC<ProductCarouselSectionProps> = ({
  images,
  title,
  currentSlide,
  onSlideChange,
  primaryColor,
}) => {
  return (
    <div className="pl-12 hidden lg:block">
      <div className="mb-10">
        <ProductCarousel
          images={images}
          title={title}
          currentSlide={currentSlide}
          onSlideChange={onSlideChange}
          primaryColor={primaryColor}
        />
      </div>
    </div>
  );
};

export default ProductCarouselSection;
