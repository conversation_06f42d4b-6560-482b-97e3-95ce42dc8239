import React from "react";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { FlipCard } from "@/components/Common/FlipCard.tsx";

const KeyFeatures = ({ productData }: { productData: ProductDetailsType }) => {
  const title = productData.key_features?.title;

  return (
    <section className="flex flex-col gap-4 max-w-[1020px] mx-auto">
      <h2 className="text-[32px] font-semibold font-obviously text-black text-center">
        {title}
      </h2>
      <UniversalCarousel
        useNativeScrollbar={false}
        hideScrollbar={false}
        gapClassName="gap-4"
        scrollbarColor="#EFA146"
      >
        {productData.key_features?.key_feature_items?.flippable_card_items.map(
          (card, i) => (
            <FlipCard
              key={i}
              frontImage={card.front?.web?.url || ""}
              backImage={card.back?.web?.url || ""}
              frontAlt={card.front?.web?.alternativeText || "key-feature-front"}
              backAlt={card.back?.web?.alternativeText || "key-feature-back"}
              width={200}
              height={368}
              className="w-[200px] h-[368px]"
            />
          )
        )}
      </UniversalCarousel>
    </section>
  );
};

export default KeyFeatures;
