import { ExtendedVariant } from "@/types/Medusa/Product";

/**
 * Utility functions for PDP components
 */

// ============================================================================
// PRICING UTILITIES
// ============================================================================

/**
 * Calculate discount percentage between two prices
 */
export const calculateDiscountPercentage = (
  currentPrice: number,
  originalPrice: number
): number => {
  if (originalPrice <= 0 || currentPrice >= originalPrice) return 0;
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
};

/**
 * Format price for display
 */
export const formatPrice = (price: number): string => {
  return `₹${price.toLocaleString()}`;
};

/**
 * Calculate loyalty points (typically 2% of price)
 */
export const calculateLoyaltyPoints = (
  price: number,
  percentage: number = 2
): number => {
  return Math.floor(price * (percentage / 100));
};

// ============================================================================
// VARIANT UTILITIES
// ============================================================================

/**
 * Get variant price information
 */
export const getVariantPricing = (variant: ExtendedVariant | null) => {
  if (!variant) {
    return {
      currentPrice: 0,
      originalPrice: 0,
      hasDiscount: false,
      discountPercentage: 0,
    };
  }

  const currentPrice =
    variant.extended_product_variants?.compare_at_price ||
    variant.calculated_price?.calculated_amount ||
    0;
  const originalPrice = variant.calculated_price?.calculated_amount || 0;
  const hasDiscount = originalPrice > currentPrice;
  const discountPercentage = calculateDiscountPercentage(
    currentPrice,
    originalPrice
  );

  return {
    currentPrice,
    originalPrice,
    hasDiscount,
    discountPercentage,
  };
};

/**
 * Check if variant is available
 */
export const isVariantAvailable = (
  variant: ExtendedVariant | null
): boolean => {
  return !!(
    variant &&
    variant.inventory_quantity &&
    variant.inventory_quantity > 0
  );
};

/**
 * Get variant display title
 */
export const getVariantTitle = (variant: ExtendedVariant | null): string => {
  return variant?.title || "";
};

// ============================================================================
// IMAGE UTILITIES
// ============================================================================

/**
 * Extract image URLs from variant images
 */
export const extractVariantImageUrls = (
  variant: ExtendedVariant | null
): string[] => {
  if (!variant?.variant_image || !Array.isArray(variant.variant_image)) {
    return [];
  }

  return variant.variant_image
    .sort((a, b) => a.rank - b.rank)
    .map((img) => img.url);
};

/**
 * Extract global product image URLs
 */
export const extractProductImageUrls = (
  productImages: any[] | null
): string[] => {
  if (!productImages || !Array.isArray(productImages)) {
    return [];
  }

  return productImages.map((img) => img.url || img);
};

/**
 * Get fallback images when no images are available
 */
export const getFallbackImages = (): string[] => {
  return [
    "/images/products/pdp/image.png",
    "/images/products/badaam/highlight/2.webp",
    "/images/products/badaam/highlight/3.webp",
    "/images/products/badaam/highlight/4.webp",
    "/images/products/badaam/highlight/5.webp",
  ];
};

// ============================================================================
// BREADCRUMB UTILITIES
// ============================================================================

/**
 * Generate breadcrumb items for product
 */
export const generateProductBreadcrumb = (productTitle?: string) => {
  return [
    { label: "Home", href: "/" },
    { label: "Products", href: "/products" },
    { label: productTitle || "Product" },
  ];
};

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate quantity constraints
 */
export const validateQuantity = (
  quantity: number,
  maxQuantity: number
): {
  isValid: boolean;
  error?: string;
} => {
  if (quantity < 1) {
    return { isValid: false, error: "Quantity must be at least 1" };
  }

  if (quantity > maxQuantity) {
    return { isValid: false, error: `Maximum quantity is ${maxQuantity}` };
  }

  return { isValid: true };
};

/**
 * Check if product data is complete
 */
export const isProductDataComplete = (
  strapiProduct: any,
  medusaProduct: any
): boolean => {
  return !!(strapiProduct || medusaProduct);
};

// ============================================================================
// ERROR HANDLING UTILITIES
// ============================================================================

/**
 * Create standardized error object
 */
export const createProductError = (
  code: string,
  message: string,
  details?: any
) => {
  return {
    code,
    message,
    details,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Handle async errors with fallback
 */
export const handleAsyncError = async <T>(
  asyncFn: () => Promise<T>,
  fallback: T,
  errorHandler?: (error: Error) => void
): Promise<T> => {
  try {
    return await asyncFn();
  } catch (error) {
    if (errorHandler && error instanceof Error) {
      errorHandler(error);
    }
    return fallback;
  }
};

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

/**
 * Format date for display
 */
export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

/**
 * Truncate text with ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + "...";
};

/**
 * Generate unique ID for components
 */
export const generateId = (prefix: string = "pdp"): string => {
  return `${prefix}-${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 11)}`;
};
