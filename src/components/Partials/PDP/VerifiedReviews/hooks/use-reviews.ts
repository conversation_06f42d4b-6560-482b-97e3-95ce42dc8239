"use client";

import { useQuery } from "@tanstack/react-query";
import { useState, useMemo } from "react";
import { listProductReviews } from "@/services/medusa/reviews";
import type {
  UseReviewsOptions,
  UseReviewsReturn,
  ProductReview,
} from "./types";

export const useReviews = ({
  productId,
  limit = 5,
  enabled = true,
}: UseReviewsOptions): UseReviewsReturn => {
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate offset based on current page
  const offset = useMemo(() => {
    return (currentPage - 1) * limit;
  }, [currentPage, limit]);

  const {
    data: apiResponse,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["product-reviews", productId, currentPage, limit],
    queryFn: () =>
      listProductReviews({
        limit,
        offset,
        queryParams: {
          product_id: productId,
        },
      }),
    enabled: enabled && !!productId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Extract data from API response
  const data = useMemo(() => {
    if (!apiResponse?.data?.data) return [];
    return apiResponse.data.data as ProductReview[];
  }, [apiResponse]);

  const totalCount = useMemo(() => {
    return apiResponse?.data?.count || 0;
  }, [apiResponse]);

  const totalPages = useMemo(() => {
    return Math.ceil(totalCount / limit);
  }, [totalCount, limit]);

  const hasNextPage = useMemo(() => {
    return currentPage < totalPages;
  }, [currentPage, totalPages]);

  const hasPreviousPage = useMemo(() => {
    return currentPage > 1;
  }, [currentPage]);

  // Navigation functions
  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const nextPage = () => {
    if (hasNextPage) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const previousPage = () => {
    if (hasPreviousPage) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  return {
    data,
    totalCount,
    currentPage,
    totalPages,
    isLoading,
    isError,
    error: error as Error | null,
    goToPage,
    nextPage,
    previousPage,
    hasNextPage,
    hasPreviousPage,
  };
};
