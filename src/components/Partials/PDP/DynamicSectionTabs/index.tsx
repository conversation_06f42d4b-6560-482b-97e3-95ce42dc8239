"use client";

import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useCallback, useEffect, useState } from "react";

interface Section {
  id: string;
  label: string;
}

interface DynamicSectionTabsProps {
  sections: Section[];
}

export function DynamicSectionTabs({ sections }: DynamicSectionTabsProps) {
  const [activeSection, setActiveSection] = useState<string>("");

  const handleScroll = useCallback((id: string) => {
    const el = document.getElementById(id);
    if (el) {
      // Set active section immediately for instant UI feedback
      setActiveSection(id);

      // Calculate the exact position to scroll to
      const elementTop = el.getBoundingClientRect().top + window.pageYOffset;
      const offsetTop = 100; // Adjust this value based on your header/navbar height

      // Scroll to the exact position
      window.scrollTo({
        top: elementTop - offsetTop,
        behavior: "smooth",
      });
    }
  }, []);

  // Handle tab click
  const handleTabChange = useCallback(
    (value: string) => {
      handleScroll(value);
    },
    [handleScroll]
  );

  // Update active section based on scroll position
  useEffect(() => {
    if (!sections || sections.length === 0) return;

    const handleScrollEvent = () => {
      // Find the section that is currently in view
      for (const section of sections) {
        const element = document.getElementById(section.id);
        if (!element) continue;

        const rect = element.getBoundingClientRect();
        const offsetTop = 150; // Same offset as used in handleScroll

        // Check if the element is in the viewport with proper offset consideration
        if (rect.top <= offsetTop && rect.bottom >= offsetTop) {
          setActiveSection(section.id);
          break;
        }
      }
    };

    // Set initial active section
    if (!activeSection && sections.length > 0) {
      handleScrollEvent();
    }

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledScrollHandler = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScrollEvent();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener("scroll", throttledScrollHandler, {
      passive: true,
    });
    return () => window.removeEventListener("scroll", throttledScrollHandler);
  }, [sections, activeSection]);

  // Set initial active section when sections first load
  useEffect(() => {
    if (sections && sections.length > 0 && !activeSection) {
      setActiveSection(sections[0].id);
    }
  }, [sections, activeSection]);

  if (!sections || sections.length === 0) return null;

  return (
    <Tabs value={activeSection} onValueChange={handleTabChange}>
      <TabsList className="flex flex-wrap gap-2 mb-6">
        {sections.map((section) => (
          <TabsTrigger
            key={section.id}
            value={section.id}
            className={`rounded-full px-4 py-1 text-sm font-medium transition-colors ${
              activeSection === section.id
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : "bg-muted text-foreground hover:bg-muted/80"
            }`}
          >
            {section.label}
          </TabsTrigger>
        ))}
      </TabsList>
    </Tabs>
  );
}
