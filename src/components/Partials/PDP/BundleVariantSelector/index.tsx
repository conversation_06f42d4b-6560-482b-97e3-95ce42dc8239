"use client";

import React, { useCallback, useMemo } from "react";
import { BundleVariantSelectorProps, BundleQuantityValidation } from "../types";
import {
  calculateTotalQuantity,
  getRequiredPackSize,
  validateBundleConfiguration,
  getVariantMaxQuantity,
} from "./BundleValidationUtils";

/**
 * BundleVariantSelector Component
 *
 * Displays bundle variants with quantity selectors for BYOB products.
 * Only renders when productType === "BYOB".
 */
const BundleVariantSelector: React.FC<BundleVariantSelectorProps> = ({
  strapiProduct,
  bundleVariants,
  productType,
  onQuantityChange,
  selectedQuantities,
}) => {
  // Calculate total selected quantity using utility function
  const totalSelectedQuantity = useMemo(() => {
    return calculateTotalQuantity(selectedQuantities);
  }, [selectedQuantities]);

  // Get required pack size using utility function
  const requiredPackSize = useMemo(() => {
    return getRequiredPackSize(strapiProduct);
  }, [strapiProduct]);

  // Bundle quantity validation using utility function
  const validation: BundleQuantityValidation = useMemo(() => {
    return validateBundleConfiguration(selectedQuantities, requiredPackSize);
  }, [selectedQuantities, requiredPackSize]);
  console.log("validation: ", validation);

  // Check if plus button should be disabled for a specific variant
  const isPlusButtonDisabled = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      const variant = bundleVariants.find((v) => v.systemId === variantId);
      const maxQuantity = variant ? getVariantMaxQuantity(variant) : undefined;

      // Disable if total bundle limit reached
      if (totalSelectedQuantity >= requiredPackSize) {
        return true;
      }

      // Disable if individual variant max quantity reached
      if (maxQuantity !== undefined && currentQuantity >= maxQuantity) {
        return true;
      }

      return false;
    },
    [
      selectedQuantities,
      bundleVariants,
      totalSelectedQuantity,
      requiredPackSize,
    ]
  );

  // Check if minus button should be disabled for a specific variant
  const isMinusButtonDisabled = useCallback(
    (variantId: string) => {
      const currentQuantity = selectedQuantities[variantId] || 0;
      return currentQuantity <= 0;
    },
    [selectedQuantities]
  );

  const handleQuantityChange = useCallback(
    (variantId: string, newQuantity: number) => {
      // Get the variant to check for max quantity
      const variant = bundleVariants.find((v) => v.systemId === variantId);
      const maxQuantity = variant ? getVariantMaxQuantity(variant) : undefined;

      // Ensure quantity is not negative and respects max quantity
      let clampedQuantity = Math.max(0, newQuantity);
      if (maxQuantity !== undefined) {
        clampedQuantity = Math.min(clampedQuantity, maxQuantity);
      }

      // Ensure total bundle limit is not exceeded
      const otherVariantsTotal = Object.entries(selectedQuantities)
        .filter(([id]) => id !== variantId)
        .reduce((sum, [, qty]) => sum + qty, 0);

      const maxAllowedForThisVariant = requiredPackSize - otherVariantsTotal;
      clampedQuantity = Math.min(clampedQuantity, maxAllowedForThisVariant);

      onQuantityChange(variantId, clampedQuantity);
    },
    [onQuantityChange, bundleVariants, selectedQuantities, requiredPackSize]
  );

  const incrementQuantity = useCallback(
    (variantId: string) => {
      if (isPlusButtonDisabled(variantId)) return;

      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity + 1);
    },
    [selectedQuantities, handleQuantityChange, isPlusButtonDisabled]
  );

  const decrementQuantity = useCallback(
    (variantId: string) => {
      if (isMinusButtonDisabled(variantId)) return;

      const currentQuantity = selectedQuantities[variantId] || 0;
      handleQuantityChange(variantId, currentQuantity - 1);
    },
    [selectedQuantities, handleQuantityChange, isMinusButtonDisabled]
  );

  // Type guard: Only render for BYOB products
  if (productType !== "BYOB") {
    return null;
  }

  return (
    <div className="mb-6">
      {/* Bundle Variants List */}
      <div className="space-y-0 border border-[#00000033] rounded-sm overflow-hidden">
        {bundleVariants.map((variant, index) => {
          const variantId = variant.systemId || "";
          const currentQuantity = selectedQuantities[variantId] || 0;
          const isPlusDisabled = isPlusButtonDisabled(variantId);
          const isMinusDisabled = isMinusButtonDisabled(variantId);

          // Get variant color from metadata or use a default color scheme
          const variantColor = (variant as any).primary_color;

          return (
            <div
              key={variantId}
              className={`flex items-center justify-between p-2.5 ${
                index !== bundleVariants.length - 1
                  ? "border-b border-[#00000033]"
                  : ""
              }`}
              style={{
                backgroundColor: `${variant.bg_color}`,
              }}
            >
              {/* Variant Title */}
              <div className="max-w-[60%]">
                <h4
                  className="font-bold text-xl font-narrow break-words pr-2.5"
                  style={{ color: variantColor }}
                >
                  {variant.title}
                </h4>
              </div>

              {/* Quantity Selector */}
              <div className="flex items-center justify-between">
                <div
                  className="h-fit max-w-21 rounded-[6px] overflow-hidden flex items-center justify-between bg-white border min-w-21"
                  style={{ borderColor: variantColor }}
                >
                  {/* Minus Button */}
                  <button
                    className={`w-7 h-7 text-white flex items-center justify-center transition-all duration-200 focus:outline-none font-semibold ${
                      isMinusDisabled ? "cursor-not-allowed" : "cursor-pointer"
                    }`}
                    style={{
                      backgroundColor: variantColor,
                      cursor: isMinusDisabled ? "not-allowed" : "pointer",
                    }}
                    onClick={() => decrementQuantity(variantId)}
                    disabled={isMinusDisabled}
                    aria-label={`Decrease quantity for ${variant.title}`}
                  >
                    -
                  </button>

                  {/* Quantity Display */}
                  <div className="h-7 w-10 text-center text-xs flex items-center justify-center font-medium font-obviously  bg-white">
                    {currentQuantity}
                  </div>

                  {/* Plus Button */}
                  <button
                    className={`w-7 h-7 text-white flex items-center justify-center transition-all duration-200 focus:outline-none font-semibold ${
                      isPlusDisabled ? "cursor-not-allowed" : "cursor-pointer"
                    }`}
                    style={{
                      backgroundColor: variantColor,
                      cursor: isPlusDisabled ? "not-allowed" : "pointer",
                    }}
                    onClick={() => incrementQuantity(variantId)}
                    disabled={isPlusDisabled}
                    aria-label={`Increase quantity for ${variant.title}`}
                  >
                    +
                  </button>
                </div>
              </div>
            </div>
          );
        })}
        <div>
          <div className="flex items-center justify-between font-obviously py-[5px] px-2.5">
            <span className="w-1/2 text-sm leading-5  font-normal">
              No of Bars:
            </span>
            <span className="w-1/2 text-end text-base leading-6 text-[#1a181e] font-semibold">
              {totalSelectedQuantity}/{requiredPackSize}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BundleVariantSelector;
