// Interface automatically generated by schemas-to-ts

import { Titles } from '../../common/interfaces/Titles';
import { Titles_Plain } from '../../common/interfaces/Titles';
import { Titles_NoRelations } from '../../common/interfaces/Titles';

export interface SearchKeywords {
  title?: any;
  keyword_items: Titles[];
}
export interface SearchKeywords_Plain {
  title?: any;
  keyword_items: Titles_Plain[];
}

export interface SearchKeywords_NoRelations {
  title?: any;
  keyword_items: Titles_NoRelations[];
}

