// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface SearchUpsellCollections {
  title?: any;
  category?: { data: Category };
}
export interface SearchUpsellCollections_Plain {
  title?: any;
  category?: Category_Plain;
}

export interface SearchUpsellCollections_NoRelations {
  title?: any;
  category?: number;
}

