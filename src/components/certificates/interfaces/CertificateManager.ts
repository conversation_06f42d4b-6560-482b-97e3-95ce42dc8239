// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { SingleBanner } from '../../banner/interfaces/SingleBanner';
import { Image_Plain } from '../../media/interfaces/Image';
import { SingleBanner_Plain } from '../../banner/interfaces/SingleBanner';
import { Image_NoRelations } from '../../media/interfaces/Image';
import { SingleBanner_NoRelations } from '../../banner/interfaces/SingleBanner';

export interface CertificateManager {
  bg_image?: Image;
  certificate_items: SingleBanner[];
}
export interface CertificateManager_Plain {
  bg_image?: Image_Plain;
  certificate_items: SingleBanner_Plain[];
}

export interface CertificateManager_NoRelations {
  bg_image?: Image_NoRelations;
  certificate_items: SingleBanner_NoRelations[];
}

