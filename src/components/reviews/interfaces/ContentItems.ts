// Interface automatically generated by schemas-to-ts

import { ContentCommentItems } from './ContentCommentItems';
import { ContentCommentItems_Plain } from './ContentCommentItems';
import { ContentCommentItems_NoRelations } from './ContentCommentItems';

export interface ContentItems {
  youtube_link?: string;
  title?: string;
  ratings?: string;
  comments: ContentCommentItems[];
  read_more_comment_link?: string;
}
export interface ContentItems_Plain {
  youtube_link?: string;
  title?: string;
  ratings?: string;
  comments: ContentCommentItems_Plain[];
  read_more_comment_link?: string;
}

export interface ContentItems_NoRelations {
  youtube_link?: string;
  title?: string;
  ratings?: string;
  comments: ContentCommentItems_NoRelations[];
  read_more_comment_link?: string;
}

