// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface TruthSayerItems {
  image?: { data: Media };
  name?: string;
  username?: string;
}
export interface TruthSayerItems_Plain {
  image?: Media_Plain;
  name?: string;
  username?: string;
}

export interface TruthSayerItems_NoRelations {
  image?: number;
  name?: string;
  username?: string;
}

