// Interface automatically generated by schemas-to-ts

import { Stars } from './Stars';
import { Stars_Plain } from './Stars';
import { Stars_NoRelations } from './Stars';

export interface CelebrityStars {
  bg_color?: any;
  title?: any;
  stars: Stars[];
}
export interface CelebrityStars_Plain {
  bg_color?: any;
  title?: any;
  stars: Stars_Plain[];
}

export interface CelebrityStars_NoRelations {
  bg_color?: any;
  title?: any;
  stars: Stars_NoRelations[];
}

