// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface Stars {
  star_image?: { data: Media };
  star_name?: string;
  star_post?: { data: Media };
}
export interface Stars_Plain {
  star_image?: Media_Plain;
  star_name?: string;
  star_post?: Media_Plain;
}

export interface Stars_NoRelations {
  star_image?: number;
  star_name?: string;
  star_post?: number;
}

