// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface FamCommentItems {
  fam_image?: { data: Media };
  fam_name?: string;
  fam_designation?: string;
  fam_comment?: string;
  company_logo?: { data: Media };
}
export interface FamCommentItems_Plain {
  fam_image?: Media_Plain;
  fam_name?: string;
  fam_designation?: string;
  fam_comment?: string;
  company_logo?: Media_Plain;
}

export interface FamCommentItems_NoRelations {
  fam_image?: number;
  fam_name?: string;
  fam_designation?: string;
  fam_comment?: string;
  company_logo?: number;
}

