// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface LoveWall {
  bg_image?: { data: Media };
  title?: any;
  image?: { data: Media };
}
export interface LoveWall_Plain {
  bg_image?: Media_Plain;
  title?: any;
  image?: Media_Plain;
}

export interface LoveWall_NoRelations {
  bg_image?: number;
  title?: any;
  image?: number;
}

