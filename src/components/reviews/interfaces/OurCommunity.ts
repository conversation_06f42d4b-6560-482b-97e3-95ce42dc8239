// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { CommunityPostItems } from './CommunityPostItems';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { CommunityPostItems_Plain } from './CommunityPostItems';
import { CommunityPostItems_NoRelations } from './CommunityPostItems';

export interface OurCommunity {
  bg_image?: { data: Media };
  title?: any;
  community_post_items: CommunityPostItems[];
}
export interface OurCommunity_Plain {
  bg_image?: Media_Plain;
  title?: any;
  community_post_items: CommunityPostItems_Plain[];
}

export interface OurCommunity_NoRelations {
  bg_image?: number;
  title?: any;
  community_post_items: CommunityPostItems_NoRelations[];
}

