// Interface automatically generated by schemas-to-ts

import { SocialMedia } from '../../common/interfaces/SocialMedia';
import { SocialMedia_Plain } from '../../common/interfaces/SocialMedia';
import { SocialMedia_NoRelations } from '../../common/interfaces/SocialMedia';

export interface InfluencerTruth {
  title?: string;
  description?: string;
  youtube_link?: SocialMedia;
  mobile_bg_color?: any;
  web_bg_color?: any;
  mobile_text_color?: any;
  mobile_description_color?: any;
  web_title_color?: any;
  web_description_color?: any;
}
export interface InfluencerTruth_Plain {
  title?: string;
  description?: string;
  youtube_link?: SocialMedia_Plain;
  mobile_bg_color?: any;
  web_bg_color?: any;
  mobile_text_color?: any;
  mobile_description_color?: any;
  web_title_color?: any;
  web_description_color?: any;
}

export interface InfluencerTruth_NoRelations {
  title?: string;
  description?: string;
  youtube_link?: SocialMedia_NoRelations;
  mobile_bg_color?: any;
  web_bg_color?: any;
  mobile_text_color?: any;
  mobile_description_color?: any;
  web_title_color?: any;
  web_description_color?: any;
}

