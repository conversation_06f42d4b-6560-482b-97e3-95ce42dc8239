// Interface automatically generated by schemas-to-ts

import { TruthSayerItems } from './TruthSayerItems';
import { TruthSayerItems_Plain } from './TruthSayerItems';
import { TruthSayerItems_NoRelations } from './TruthSayerItems';

export interface TruthSayers {
  bg_color?: any;
  title?: any;
  items: TruthSayerItems[];
}
export interface TruthSayers_Plain {
  bg_color?: any;
  title?: any;
  items: TruthSayerItems_Plain[];
}

export interface TruthSayers_NoRelations {
  bg_color?: any;
  title?: any;
  items: TruthSayerItems_NoRelations[];
}

