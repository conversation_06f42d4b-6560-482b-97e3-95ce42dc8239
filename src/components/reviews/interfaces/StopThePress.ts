// Interface automatically generated by schemas-to-ts

import { PressQuoteItems } from '../../homepage/interfaces/PressQuoteItems';
import { Media } from '../../../common/schemas-to-ts/Media';
import { PressQuoteItems_Plain } from '../../homepage/interfaces/PressQuoteItems';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { PressQuoteItems_NoRelations } from '../../homepage/interfaces/PressQuoteItems';

export interface StopThePress {
  title?: any;
  bg_color?: any;
  press_quotes: PressQuoteItems[];
  bottom_icon?: { data: Media };
}
export interface StopThePress_Plain {
  title?: any;
  bg_color?: any;
  press_quotes: PressQuoteItems_Plain[];
  bottom_icon?: Media_Plain;
}

export interface StopThePress_NoRelations {
  title?: any;
  bg_color?: any;
  press_quotes: PressQuoteItems_NoRelations[];
  bottom_icon?: number;
}

