// Interface automatically generated by schemas-to-ts

import { FlippableCard } from '../../common/interfaces/FlippableCard';
import { FlippableCard_Plain } from '../../common/interfaces/FlippableCard';
import { FlippableCard_NoRelations } from '../../common/interfaces/FlippableCard';

export interface RealPeopleReviews {
  review_items?: FlippableCard;
  title?: string;
}
export interface RealPeopleReviews_Plain {
  review_items?: FlippableCard_Plain;
  title?: string;
}

export interface RealPeopleReviews_NoRelations {
  review_items?: FlippableCard_NoRelations;
  title?: string;
}

