// Interface automatically generated by schemas-to-ts

import { GangItems } from './GangItems';
import { GangItems_Plain } from './GangItems';
import { GangItems_NoRelations } from './GangItems';

export interface JoinTheGang {
  bg_color?: any;
  title?: string;
  items: GangItems[];
}
export interface JoinTheGang_Plain {
  bg_color?: any;
  title?: string;
  items: GangItems_Plain[];
}

export interface JoinTheGang_NoRelations {
  bg_color?: any;
  title?: string;
  items: GangItems_NoRelations[];
}

