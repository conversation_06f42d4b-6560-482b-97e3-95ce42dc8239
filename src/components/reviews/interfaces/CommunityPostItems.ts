// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { CommunityCommentItems } from './CommunityCommentItems';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { CommunityCommentItems_Plain } from './CommunityCommentItems';
import { CommunityCommentItems_NoRelations } from './CommunityCommentItems';

export interface CommunityPostItems {
  thumbnail_image?: { data: Media };
  likes_count?: number;
  comments_count?: number;
  shared_count?: number;
  comment_items: CommunityCommentItems[];
}
export interface CommunityPostItems_Plain {
  thumbnail_image?: Media_Plain;
  likes_count?: number;
  comments_count?: number;
  shared_count?: number;
  comment_items: CommunityCommentItems_Plain[];
}

export interface CommunityPostItems_NoRelations {
  thumbnail_image?: number;
  likes_count?: number;
  comments_count?: number;
  shared_count?: number;
  comment_items: CommunityCommentItems_NoRelations[];
}

