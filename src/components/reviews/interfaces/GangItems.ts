// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface GangItems {
  thumbnail_image?: { data: Media };
  platform_name?: string;
  total_followers?: string;
}
export interface GangItems_Plain {
  thumbnail_image?: Media_Plain;
  platform_name?: string;
  total_followers?: string;
}

export interface GangItems_NoRelations {
  thumbnail_image?: number;
  platform_name?: string;
  total_followers?: string;
}

