// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface PressQuoteItems {
  press_logo?: { data: Media };
  quote?: string;
}
export interface PressQuoteItems_Plain {
  press_logo?: Media_Plain;
  quote?: string;
}

export interface PressQuoteItems_NoRelations {
  press_logo?: number;
  quote?: string;
}

