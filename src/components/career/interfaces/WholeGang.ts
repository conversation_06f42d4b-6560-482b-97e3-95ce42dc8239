// Interface automatically generated by schemas-to-ts

import { FlippableCardItems } from '../../common/interfaces/FlippableCardItems';
import { FlippableCardItems_Plain } from '../../common/interfaces/FlippableCardItems';
import { FlippableCardItems_NoRelations } from '../../common/interfaces/FlippableCardItems';

export interface WholeGang {
  title?: any;
  items: FlippableCardItems[];
  bg_color?: any;
}
export interface WholeGang_Plain {
  title?: any;
  items: FlippableCardItems_Plain[];
  bg_color?: any;
}

export interface WholeGang_NoRelations {
  title?: any;
  items: FlippableCardItems_NoRelations[];
  bg_color?: any;
}

