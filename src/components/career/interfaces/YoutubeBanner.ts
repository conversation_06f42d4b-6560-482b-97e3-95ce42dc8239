// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface YoutubeBanner {
  bg_image?: { data: Media };
  title?: string;
  description?: string;
  button_text?: string;
  youtube_link?: string;
  thumbnail_image?: { data: Media };
  auto_play?: boolean;
}
export interface YoutubeBanner_Plain {
  bg_image?: Media_Plain;
  title?: string;
  description?: string;
  button_text?: string;
  youtube_link?: string;
  thumbnail_image?: Media_Plain;
  auto_play?: boolean;
}

export interface YoutubeBanner_NoRelations {
  bg_image?: number;
  title?: string;
  description?: string;
  button_text?: string;
  youtube_link?: string;
  thumbnail_image?: number;
  auto_play?: boolean;
}

