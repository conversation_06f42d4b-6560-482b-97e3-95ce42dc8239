// Interface automatically generated by schemas-to-ts

import { EmployeeImages } from './EmployeeImages';
import { EmployeeImages_Plain } from './EmployeeImages';
import { EmployeeImages_NoRelations } from './EmployeeImages';

export interface EmployeePhotoGrid {
  bg_color?: any;
  title?: string;
  description?: string;
  images: EmployeeImages[];
}
export interface EmployeePhotoGrid_Plain {
  bg_color?: any;
  title?: string;
  description?: string;
  images: EmployeeImages_Plain[];
}

export interface EmployeePhotoGrid_NoRelations {
  bg_color?: any;
  title?: string;
  description?: string;
  images: EmployeeImages_NoRelations[];
}

