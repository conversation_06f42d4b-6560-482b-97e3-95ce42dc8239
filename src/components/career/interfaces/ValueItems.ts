// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ValueItems {
  image?: { data: Media };
  primary_color?: any;
  bg_color?: any;
  footer_text?: any;
  footer_icon?: { data: Media };
  description?: string;
}
export interface ValueItems_Plain {
  image?: Media_Plain;
  primary_color?: any;
  bg_color?: any;
  footer_text?: any;
  footer_icon?: Media_Plain;
  description?: string;
}

export interface ValueItems_NoRelations {
  image?: number;
  primary_color?: any;
  bg_color?: any;
  footer_text?: any;
  footer_icon?: number;
  description?: string;
}

