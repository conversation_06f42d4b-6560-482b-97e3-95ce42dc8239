{"collectionName": "components_homepage_press_quote_items", "info": {"displayName": "Press Quote Items", "description": ""}, "options": {}, "attributes": {"quote": {"type": "text"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "press_logo": {"type": "component", "repeatable": false, "component": "media.icon"}, "press_name": {"type": "string"}, "subtitle": {"type": "string"}}}