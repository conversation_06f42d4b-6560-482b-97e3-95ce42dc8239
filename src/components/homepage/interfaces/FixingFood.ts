// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { Image_Plain } from '../../media/interfaces/Image';
import { Image_NoRelations } from '../../media/interfaces/Image';

export interface FixingFood {
  title?: string;
  description?: string;
  image?: Image;
}
export interface FixingFood_Plain {
  title?: string;
  description?: string;
  image?: Image_Plain;
}

export interface FixingFood_NoRelations {
  title?: string;
  description?: string;
  image?: Image_NoRelations;
}

