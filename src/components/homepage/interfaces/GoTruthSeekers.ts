// Interface automatically generated by schemas-to-ts

import { ImageOrVideo } from '../../media/interfaces/ImageOrVideo';
import { Button } from '../../elements/interfaces/Button';
import { ImageOrVideo_Plain } from '../../media/interfaces/ImageOrVideo';
import { Button_Plain } from '../../elements/interfaces/Button';
import { ImageOrVideo_NoRelations } from '../../media/interfaces/ImageOrVideo';
import { Button_NoRelations } from '../../elements/interfaces/Button';

export interface GoTruthSeekers {
  title_image?: ImageOrVideo;
  title?: string;
  video?: ImageOrVideo;
  instagram_community?: Button;
  youtube_community?: Button;
  linkedin_community?: Button;
}
export interface GoTruthSeekers_Plain {
  title_image?: ImageOrVideo_Plain;
  title?: string;
  video?: ImageOrVideo_Plain;
  instagram_community?: Button_Plain;
  youtube_community?: Button_Plain;
  linkedin_community?: Button_Plain;
}

export interface GoTruthSeekers_NoRelations {
  title_image?: ImageOrVideo_NoRelations;
  title?: string;
  video?: ImageOrVideo_NoRelations;
  instagram_community?: Button_NoRelations;
  youtube_community?: Button_NoRelations;
  linkedin_community?: Button_NoRelations;
}

