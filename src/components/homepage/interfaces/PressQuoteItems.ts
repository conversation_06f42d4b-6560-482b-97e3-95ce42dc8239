// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface PressQuoteItems {
  quote?: string;
  bg_color?: any;
  press_logo?: Icon;
  press_name?: string;
  subtitle?: string;
}
export interface PressQuoteItems_Plain {
  quote?: string;
  bg_color?: any;
  press_logo?: Icon_Plain;
  press_name?: string;
  subtitle?: string;
}

export interface PressQuoteItems_NoRelations {
  quote?: string;
  bg_color?: any;
  press_logo?: Icon_NoRelations;
  press_name?: string;
  subtitle?: string;
}

