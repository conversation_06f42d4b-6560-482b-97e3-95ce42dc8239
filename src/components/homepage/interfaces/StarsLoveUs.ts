// Interface automatically generated by schemas-to-ts

import { ImageOrVideo } from '../../media/interfaces/ImageOrVideo';
import { Stars } from '../../reviews/interfaces/Stars';
import { ImageOrVideo_Plain } from '../../media/interfaces/ImageOrVideo';
import { Stars_Plain } from '../../reviews/interfaces/Stars';
import { ImageOrVideo_NoRelations } from '../../media/interfaces/ImageOrVideo';
import { Stars_NoRelations } from '../../reviews/interfaces/Stars';

export interface StarsLoveUs {
  image?: ImageOrVideo;
  subtitle?: string;
  star_comments: Stars[];
}
export interface StarsLoveUs_Plain {
  image?: ImageOrVideo_Plain;
  subtitle?: string;
  star_comments: Stars_Plain[];
}

export interface StarsLoveUs_NoRelations {
  image?: ImageOrVideo_NoRelations;
  subtitle?: string;
  star_comments: Stars_NoRelations[];
}

