// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export enum CategoryEnum {
  Powder = 'POWDER',
  Chocolate = 'CHOCOLATE',
  Bar = 'BAR',
  PeanutButter = 'PEANUT BUTTER',}

export interface TrustBuildingFoodItems {
  category: CategoryEnum;
  primary_image?: { data: Media };
  secondary_image?: { data: Media };
}
export interface TrustBuildingFoodItems_Plain {
  category: CategoryEnum;
  primary_image?: Media_Plain;
  secondary_image?: Media_Plain;
}

export interface TrustBuildingFoodItems_NoRelations {
  category: CategoryEnum;
  primary_image?: number;
  secondary_image?: number;
}

