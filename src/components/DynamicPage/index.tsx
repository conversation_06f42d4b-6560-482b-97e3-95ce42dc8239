import { CollectionType } from "@/types/Collections/Collection";
import { ProductType } from "@/types/Collections/Product";
import dynamic from "next/dynamic";
import React from "react";

interface ComponentMapType {
  [key: string]: {
    FF: React.ComponentType<any>;
    Lazy: React.ComponentType<any>;
  };
}

import { PageType, PageTypeEnum } from "@/types/Page";
import DynamicBlock from "@/components/blocks/Dynamic";
import SingleBanner from "@/components/blocks/SingleBanner";

const Marquee = {
  ComponentCommonMarquee: {
    FF: DynamicBlock,
    Lazy: dynamic(() => import("@/components/blocks/Dynamic")),
  },
};

const SingleBannerComp = {
  ComponentBannerSingleBanner: {
    FF: SingleBanner,
    Lazy: dynamic(() => import("@/components/blocks/SingleBanner")),
  },
};

// Combine all components
export const componentMap: ComponentMapType = {
  ...<PERSON>quee,
  ...SingleBannerComp,
};

interface DynamicPageProps {
  page: string;
  pageType: PageTypeEnum;
  pageData?: PageType | null;
  collectionData?: CollectionType;
  productData?: ProductType;
}

const DynamicPage: React.FC<DynamicPageProps> = async ({
  page,
  pageData,
  pageType,
  collectionData,
  productData,
}) => {
  const blocks = pageData?.blocks || [];
  if (blocks.length === 0) {
    console.warn(`-> No blocks found for template: ${page}`);
    return <p>page not founds</p>;
  }

  const renderBlock = (block: any, index: number) => {
    const ComponentInfo = componentMap[block.__typename];
    if (!ComponentInfo) return null;

    const Component = ComponentInfo.FF;
    const commonProps = {
      block,
      page,
      pageType,
      collectionData,
      productData,
      priority: index < 4,
    };

    console.log("component", Component);

    return <Component key={`block-${index}`} {...(commonProps as any)} />;
  };

  const blockElements = blocks.map((block: any, index: number) =>
    renderBlock(block, index)
  );

  console.log("blockElements.....", blockElements);

  return <>{blockElements}</>;
};

export default DynamicPage;
