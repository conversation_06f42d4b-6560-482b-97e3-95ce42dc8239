{"collectionName": "components_elements_buttons", "info": {"displayName": "<PERSON><PERSON>", "description": ""}, "options": {}, "attributes": {"title": {"type": "text"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "action_link": {"type": "text"}, "title_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}}}