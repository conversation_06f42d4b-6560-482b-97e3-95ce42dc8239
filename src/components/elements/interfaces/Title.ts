// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export enum IconPositionEnum {
  Left = 'left',
  Right = 'right',
  Top = 'top',}

export interface Title {
  title?: string;
  color?: any;
  icon?: { data: Media };
  iconColor?: any;
  iconPosition?: IconPositionEnum;
}
export interface Title_Plain {
  title?: string;
  color?: any;
  icon?: Media_Plain;
  iconColor?: any;
  iconPosition?: IconPositionEnum;
}

export interface Title_NoRelations {
  title?: string;
  color?: any;
  icon?: number;
  iconColor?: any;
  iconPosition?: IconPositionEnum;
}

