// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ImageOrVideo {
  web?: { data: Media };
  mobile?: { data: Media };
}
export interface ImageOrVideo_Plain {
  web?: Media_Plain;
  mobile?: Media_Plain;
}

export interface ImageOrVideo_NoRelations {
  web?: number;
  mobile?: number;
}

