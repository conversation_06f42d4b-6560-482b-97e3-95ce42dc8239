// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface ExternalMediaEmbed {
  thumbnail_image?: { data: Media };
  action_link?: string;
}
export interface ExternalMediaEmbed_Plain {
  thumbnail_image?: Media_Plain;
  action_link?: string;
}

export interface ExternalMediaEmbed_NoRelations {
  thumbnail_image?: number;
  action_link?: string;
}

