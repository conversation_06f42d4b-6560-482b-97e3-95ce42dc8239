import React from "react";
import SeactionHeading from "@/components/Common/Section/Heading";
import SubHeading from "@/components/Common/Section/SubHeading";
import Image from "next/image";

interface CompareProps {
  /** Section title */
  title: string;
  /** Color for the section title text */
  titleColor?: string;
  /** Background color for the section */
  backgroundColor?: string;
  /** Optional background image URL */
  backgroundImage?: string;
  /** Array of card data to display */
  /** Tailwind class for gap between cards */
  gapClassName?: string;
  /** Optional icon to display with the title */
  icon?: React.ReactNode;
  /** Color for the icon */
  iconColor?: string;
  /** Additional classes for the icon */
  iconClassName?: string;
  /** Whether to hide the scrollbar */
  hideScrollbar?: boolean;
  /** Color for the scrollbar */
  scrollbarColor?: string;
  /** Width for each flip card (in pixels) */
  cardWidth?: number;
  /** Height for each flip card (in pixels) */
  cardHeight?: number;
  /** Additional classes to apply to each flip card */
  cardClassName?: string;
}

export default function Compare({
  title,
  titleColor = "#1a181e",
  backgroundColor = "#F9DEA5",
  backgroundImage,
  icon,
  iconColor,
  iconClassName,
}: CompareProps) {
  // Prepare the style object based on props
  let containerStyle: React.CSSProperties = {};

  // Set background color if not transparent
  if (backgroundColor !== "transparent") {
    containerStyle.backgroundColor = backgroundColor;
  }

  // Add background image if provided
  if (backgroundImage) {
    containerStyle = {
      ...containerStyle,
      backgroundImage: `url('${backgroundImage}')`,
      backgroundSize: "cover",
      backgroundPosition: "center",
      backgroundRepeat: "no-repeat",
    };
  }

  return (
    <div
      className={`py-6 ${
        backgroundImage ? "bg-cover bg-center bg-no-repeat" : ""
      }`}
      style={containerStyle}
    >
      <div className="max-w-[600px] mx-auto px-4 flex flex-col items-center space-y-2.5">
        <div className="md:space-y-5 space-y-2.5 max-w-[400px]">
          <SeactionHeading
            title={title}
            color={titleColor}
            icon={icon}
            iconColor={iconColor}
            iconClassName={iconClassName}
          />
          <SubHeading
            title="All the protein you need, none of the chemicals you don't."
            color="white"
          />
        </div>

        <div className="relative aspect-square w-full">
          <Image
            src={"/images/banners/comparison_with_other.png"}
            alt="with other"
            fill
            style={{ objectFit: "contain" }}
          />
        </div>
      </div>
    </div>
  );
}
