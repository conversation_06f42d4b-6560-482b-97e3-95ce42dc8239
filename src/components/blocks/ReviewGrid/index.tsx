import React from "react";
import CompactReviewCard from "@/components/Common/CompactReviewCard";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { cn } from "@/libs/utils";

export interface ReviewItem {
  id: string;
  name: string;
  review: string;
  rating: number;
  avatar?: string;
  emoji?: string;
  productLinks?: { text: string; href: string }[];
}

interface ReviewGridProps {
  reviews: ReviewItem[];
  className?: string;
  useCarousel?: boolean;
  hideScrollbar?: boolean;
}

const ReviewGrid: React.FC<ReviewGridProps> = ({
  reviews,
  className,
  useCarousel = false,
  hideScrollbar = false,
}) => {
  // Split the reviews into two rows: first 4 in row 1, next 3 in row 2
  const firstRowReviews = reviews.slice(0, 4);
  const secondRowReviews = reviews.slice(4, 7);

  // First row content
  const firstRowContent = (
    <div
      className={cn(
        useCarousel
          ? "flex gap-6"
          : "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",
        "mb-0 lg:mb-6"
      )}
    >
      {firstRowReviews.map((review) => (
        <div
          key={review.id}
          className={useCarousel ? "w-[280px] flex-shrink-0" : ""}
        >
          <CompactReviewCard
            name={review.name}
            review={review.review}
            rating={review.rating}
            avatar={review.avatar || "/avatar-placeholder.png"}
            emoji={review.emoji}
          />
        </div>
      ))}
    </div>
  );

  // Second row content
  const secondRowContent = secondRowReviews.length > 0 && (
    <div
      className={cn(
        useCarousel
          ? "flex gap-6"
          : "grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-3 gap-6 max-w-4xl mx-auto"
      )}
    >
      {secondRowReviews.map((review) => (
        <div
          key={review.id}
          className={useCarousel ? "w-[280px] flex-shrink-0" : ""}
        >
          <CompactReviewCard
            name={review.name}
            review={review.review}
            rating={review.rating}
            avatar={review.avatar || "/avatar-placeholder.png"}
            emoji={review.emoji}
          />
        </div>
      ))}
    </div>
  );

  return (
    <div className={className}>
      {useCarousel ? (
        <>
          <UniversalCarousel
            useNativeScrollbar={true}
            hideScrollbar={hideScrollbar}
          >
            {firstRowContent}
          </UniversalCarousel>

          {secondRowReviews.length > 0 && (
            <div>
              <UniversalCarousel
                useNativeScrollbar={true}
                hideScrollbar={hideScrollbar}
              >
                {secondRowContent}
              </UniversalCarousel>
            </div>
          )}
        </>
      ) : (
        <>
          {firstRowContent}
          {secondRowContent}
        </>
      )}
    </div>
  );
};

export default ReviewGrid;
