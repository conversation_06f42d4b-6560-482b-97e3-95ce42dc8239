import React, { useState } from "react";
import StarRating from "@/components/Common/StarRating";
import Img from "@/components/Elements/img";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselDots,
} from "@/components/ui/carousel";
import { Button } from "@/components/ui/button";
import ChevronLeft from "@/assets/icons/ChevronLeft";
import ChevronRight from "@/assets/icons/ChevronRight";

interface ProductHighlightProps {
  title?: string;
  rating?: number;
  images?: string[];
}

const ProductHighlight: React.FC<ProductHighlightProps> = ({
  title = "Badaaaam Chocolate - Pack of 10",
  rating = 4.5,
  images = [
    "/images/products/badaam/highlight/1.webp",
    "/images/products/badaam/highlight/2.webp",
    "/images/products/badaam/highlight/3.webp",
    "/images/products/badaam/highlight/4.webp",
    "/images/products/badaam/highlight/5.webp",
  ],
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  console.log("currentSlide", currentSlide);
  return (
    <section className="px-4 py-8 md:py-10 bg-white">
      <div className="mx-auto space-y-4 py-8 md:py-10 px-6 md:px-15 text-center rounded-[20px] bg-[#be259a] max-w-[960px] flex flex-col md:flex-row">
        {/* Content section - below for mobile, on left for desktop */}
        <div className="px-6 md:px-0 w-full md:w-1/2 text-left order-2 md:order-1 mt-4 md:mt-0">
          <div className="md:mr-6">
            <p className="text-[28px] md:text-4xl text-white font-narrow font-semibold">
              {title}
            </p>
            <StarRating rating={rating} className="mt-2" />

            <p className="text-sm text-white font-obviously mt-6 md:mt-[54px]">
              6-7 perfectly roasted, sliced almonds, sitting atop a bed of real,
              decadent, date-sweetened chocolate. Tasty. Simple.
            </p>

            <div className="mt-4">
              <Button className="h-[50px] leading-[26px] w-full py-3 rounded-sm bg-white text-[#be259a] text-sm font-obviously font-semibold uppercase cursor-pointer hover:bg-[#ffffff] hover:text-[#be259a]">
                ADD TO CART
              </Button>
            </div>
          </div>
        </div>
        {/* Carousel section - on top for mobile, on right for desktop */}
        <div className="px-0 md:px-10 w-full md:w-1/2 flex items-center justify-center order-1 md:order-2">
          <div className="w-full max-w-[100%] md:max-w-[400px] relative">
            <Carousel
              className="w-full relative"
              opts={{ loop: true }}
              onSlideChange={(index) => setCurrentSlide(index)}
            >
              <div className="rounded-lg overflow-hidden">
                <CarouselContent>
                  {images.map((image, index) => (
                    <CarouselItem key={index}>
                      <div className="relative aspect-square w-full flex items-center justify-center">
                        <Img
                          src={image}
                          alt={`${title} - Image ${index + 1}`}
                          width={300}
                          height={300}
                          className="object-contain"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>

                {/* Dot indicators */}
                <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                  <CarouselDots
                    className="gap-2"
                    baseClassName="transition-all duration-300"
                    activeClassName="bg-white w-6 h-2 rounded-full"
                    inactiveClassName="bg-white/50 w-2 h-2 rounded-full"
                  />
                </div>
              </div>

              {/* Navigation buttons - inside carousel but positioned outside visually */}
              <CarouselPrevious
                className="hidden md:block absolute -left-4 md:-left-7 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent text-white border-none h-8 w-8 md:h-12 md:w-12 cursor-pointer [&_svg]:!text-white"
                icon={<ChevronLeft />}
              />
              <CarouselNext
                className="hidden md:block absolute -right-4 md:-right-15 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent text-white border-none h-8 w-8 md:h-12 md:w-12 cursor-pointer [&_svg]:!text-white"
                icon={<ChevronRight />}
              />
            </Carousel>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductHighlight;
