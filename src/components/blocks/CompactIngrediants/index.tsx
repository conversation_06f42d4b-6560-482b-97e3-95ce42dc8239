import FlavorCTAButton from "@/components/Common/FlavorShowcase/FlavorCTAButton";
import IngredientBreakdown from "@/components/Common/FlavorShowcase/IngredientBreakdown";
import React from "react";

const CompactIngrediants = () => {
  return (
    <section className="py-10 bg-[#f3d4e9]">
      <div className="mx-auto space-y-4 py-5 px-6 md:px-22.5 text-center rounded-none md:rounded-4xl bg-[#be259a] shadow-[-8px_8px_0px_0px_#9c1e7e] max-w-[960px]">
        <div className="space-y-3">
          <p className=" text-4xl text-white font-narrow font-semibold">
            Just 4 Ingredients
          </p>
          <p className="text-2xl font-gooddog text-white">all add upto 100%</p>
        </div>

        <div className="max-w-3xl">
          <IngredientBreakdown
            ingredients={[
              {
                type: "Cocoa",
                percentage: 20,
                image: "/images/ingredients/cocoa.png",
              },
              {
                type: "Almonds",
                percentage: 40,
                image: "/images/ingredients/almonds.png",
              },
              {
                type: "Dates",
                percentage: 25,
                image: "/images/ingredients/dates.png",
              },
              {
                type: "Whole Milk",
                percentage: 15,
                image: "/images/ingredients/milk.png",
              },
            ]}
            gapClassName="gap-0"
            cardClassName="shadow-[-2px_2px_0px_0px_rgba(0,0,0,0.3)] min-w-[180px]"
            containerClassName="px-2"
          />
        </div>

        <FlavorCTAButton
          text={"SHOP BADAAM CHOCOLATE"}
          link={"https://www.google.com"}
        />
      </div>
    </section>
  );
};

export default CompactIngrediants;
