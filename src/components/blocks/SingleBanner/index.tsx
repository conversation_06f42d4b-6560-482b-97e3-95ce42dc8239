"use client";

import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";
import { getStrapiUrl } from "@/utils/strapiUrl";

export interface SingleBannerProps {
  block?: any;
}

const SingleBanner: React.FC<SingleBannerProps> = ({ block }) => {
  console.log("block here from the single banner", block);

  const BannerContent = () => (
    <div className={cn("aspect-square lg:aspect-[3/1]", "w-full relative")}>
      {block?.web?.url && (
        <Image
          src={
            block?.web?.url.startsWith("/uploads")
              ? getStrapiUrl(block?.web?.url)
              : block?.web?.url
          }
          alt={block?.web?.alternativeText || "Banner image desktop"}
          fill
          style={{ objectFit: "fill" }}
          priority
          className="hidden lg:block"
        />
      )}
      {block?.mobile?.url && (
        <Image
          src={
            block?.mobile?.url.startsWith("/uploads")
              ? getStrapiUrl(block?.mobile?.url)
              : block?.mobile?.url
          }
          alt={block?.mobile?.alternativeText || "Banner image mobile"}
          fill
          style={{ objectFit: "fill" }}
          priority
          className="block lg:hidden"
        />
      )}
    </div>
  );

  return (
    <div className={cn("w-full relative")}>
      {block?.action_link ? (
        <Link href={block?.action_link} className="block w-full">
          <BannerContent />
        </Link>
      ) : (
        <BannerContent />
      )}
    </div>
  );
};

export default SingleBanner;
