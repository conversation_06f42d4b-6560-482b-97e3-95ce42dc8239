import HeaderClient from "@/components/Common/Header";
import { getHeaderDetails } from "@/libs/middlewareAPIs";
import { HeaderType } from "@/types/Collections/Header";

const Header = async () => {
  try {
    const response = await getHeaderDetails();
    const headerData: HeaderType | null = response;
    if (!headerData) return null;
    return <HeaderClient headerData={headerData} />;
  } catch (error) {
    console.error("Error fetching header data:", error);
    return <HeaderClient headerData={null} />;
  }
};

export default Header;
