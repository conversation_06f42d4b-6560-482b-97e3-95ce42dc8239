import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";

interface FeatureCardProps {
  image: string;
  imageAlt?: string;
  className?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  image,
  imageAlt = "Feature image",
  className,
}) => {
  return (
    <div
      className={cn("rounded-lg overflow-hidden relative", className)}
      style={{ aspectRatio: "1/1" }}
    >
      <Image src={image} alt={imageAlt} fill className="object-cover" />
    </div>
  );
};

export default FeatureCard;
