'use client';

import { cn } from '@/libs/utils';
import Link from 'next/link';
import { forwardRef, memo, useMemo } from 'react';

type Size = 'sm' | 'md' | 'lg' | 'none';
type Variant = 'primary' | 'outline' | 'ghost' | 'text' | 'danger' | 'success' | 'info' | 'none';

interface LinkStyleProps {
  variant?: Variant;
  size?: Size;
  fullWidth?: boolean;
  rounded?: boolean;
  disabled?: boolean;
}

interface GenericLinkProps extends React.ComponentPropsWithoutRef<typeof Link>, LinkStyleProps {}

const variantStyles: Record<Variant, string> = {
  primary: 'rounded-4 bg-black px-6 py-3 text-sm capitalize text-white',
  outline:
    'rounded-4 border border-gray-300 bg-white px-6 py-3 text-sm capitalize text-gray-800 hover:bg-gray-50',
  danger: 'rounded-4 bg-red-600 px-6 py-3 text-sm capitalize text-white hover:bg-red-700',
  success: 'rounded-4 bg-green-600 px-6 py-3 text-sm capitalize text-white hover:bg-green-700',
  info: 'rounded-4 bg-blue-500 px-6 py-3 text-sm capitalize text-white hover:bg-blue-600',
  ghost:
    'rounded-4 bg-transparent px-6 py-3 text-sm capitalize text-gray-800 hover:bg-gray-100 active:bg-gray-200',
  text: 'bg-transparent px-2 py-1 text-sm capitalize text-gray-800 hover:text-black underline-offset-2 hover:underline',
  none: '',
} as const;

const sizeStyles: Record<Size, string> = {
  sm: '',
  md: '',
  lg: '',
  none: '',
} as const;

const Alink = memo(
  forwardRef<HTMLAnchorElement, GenericLinkProps>(
    (
      {
        href,
        children,
        className = '',
        variant = 'none',
        size = 'none',
        fullWidth,
        rounded,
        disabled,
        ...props
      },
      ref,
    ) => {
      const linkClasses = useMemo(
        () =>
          cn(
            // Base styles
            'flex items-center justify-center transition-all duration-200',

            // Variant and size styles
            variantStyles[variant],
            sizeStyles[size],

            // Optional styles
            fullWidth && 'w-full',
            rounded && 'rounded-full',
            disabled && 'pointer-events-none opacity-50',

            // Custom classes
            className,
          ),
        [variant, size, fullWidth, rounded, disabled, className],
      );

      if (disabled) {
        return (
          <span className={linkClasses} ref={ref as any}>
            {children}
          </span>
        );
      }

      return (
        <Link href={href || '#'} className={linkClasses} ref={ref} prefetch={false} {...props}>
          {children}
        </Link>
      );
    },
  ),
);

Alink.displayName = 'Alink';

export default Alink;
