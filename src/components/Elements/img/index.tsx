"use client";

import { InfiniteLoader } from "@/assets/icons/InfiniteLoader";
import { cn } from "@/libs/utils";
import { PLACEHOLDER_IMAGE } from "@/utils/constants";
import Image, {
  ImageLoaderProps,
  ImageProps,
  StaticImageData,
} from "next/image";
import React, { useCallback, useMemo, useState } from "react";

export interface ImgProps extends ImageProps {
  src: string | StaticImageData;
  alt: string;
  isShopify?: boolean;
  isGif?: boolean;
  showLoader?: boolean;
}

const DEFAULT_IMAGE_QUALITY = 75;

export const Img: React.FC<ImgProps> = React.memo(
  ({
    className = "",
    src,
    alt = "Image",
    width,
    height,
    quality = DEFAULT_IMAGE_QUALITY,
    isShopify,
    isGif,
    showLoader = false,
    ...restProps
  }) => {
    const [hasError, setHasError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const imageConfig = useMemo(() => {
      if (!src) return { src: null, loader: undefined };

      // Validate URL to prevent construction errors
      let validatedSrc = src;
      try {
        // For string URLs, validate them
        if (typeof src === "string") {
          // If it's not a valid URL and doesn't start with '/', add a leading slash
          if (
            !src.startsWith("http") &&
            !src.startsWith("/") &&
            !src.startsWith("data:")
          ) {
            validatedSrc = `/${src}`;
          }
        }
      } catch (error) {
        console.error("Invalid URL in Img component:", error);
        return { src: PLACEHOLDER_IMAGE, loader: undefined };
      }

      return {
        src: validatedSrc,
        loader: ({
          src: imageSrc,
          width: imageWidth,
          quality: imageQuality,
        }: ImageLoaderProps) => {
          if (isGif) {
            return imageSrc;
          } else if (isShopify) {
            return `${imageSrc}&width=${Math.min(imageWidth, 2000)}&q=${
              imageQuality || quality
            }`;
          } else {
            return `${imageSrc}?w=${Math.min(imageWidth, 2000)}&q=${
              imageQuality || quality
            }`;
          }
        },
      };
    }, [src, quality, isShopify, isGif]);

    const handleError = useCallback(() => {
      setHasError(true);
      setIsLoading(false);
    }, []);

    const handleLoadingComplete = useCallback(() => {
      setIsLoading(false);
    }, []);

    if (!src || !imageConfig.src || hasError) {
      return (
        <Image
          src={PLACEHOLDER_IMAGE}
          alt={alt || "Image"}
          width={width || 300}
          height={height || 300}
          className={cn("h-full w-full", className, "object-cover")}
        />
      );
    }

    if (!showLoader) {
      return (
        <Image
          className={cn("h-full w-full object-cover", className)}
          src={imageConfig.src}
          alt={alt || "Image"}
          width={width}
          height={height}
          onError={handleError}
          {...restProps}
        />
      );
    }

    return (
      <div className={cn("relative h-full w-full", className)}>
        {isLoading && showLoader && (
          <div
            className={cn("absolute inset-0 flex items-center justify-center")}
          >
            <InfiniteLoader />
          </div>
        )}
        <Image
          className={cn("h-full w-full object-cover", className)}
          src={imageConfig.src}
          alt={alt || "Image"}
          width={width}
          height={height}
          onError={handleError}
          onLoadingComplete={handleLoadingComplete}
          {...restProps}
        />
      </div>
    );
  }
);

Img.displayName = "Img";

export default Img;

// sizes = "(max-width: 480px) 450px, (max-width: 576px) 560px, (max-width: 768px) 600px, (max-width: 992px) 800px, (max-width: 1200px) 1000px, 1200px";

// sizes = "(max-width: 768px) 336px, 600px";
