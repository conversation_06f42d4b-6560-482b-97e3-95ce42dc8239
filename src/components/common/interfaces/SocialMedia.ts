// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface SocialMedia {
  action_link?: string;
  image?: { data: Media };
}
export interface SocialMedia_Plain {
  action_link?: string;
  image?: Media_Plain;
}

export interface SocialMedia_NoRelations {
  action_link?: string;
  image?: number;
}

