// Interface automatically generated by schemas-to-ts

import { FlippableCard } from './FlippableCard';
import { FlippableCard_Plain } from './FlippableCard';
import { FlippableCard_NoRelations } from './FlippableCard';

export interface FlippableCardCarousel {
  flippable_card_item: FlippableCard[];
  title?: string;
}
export interface FlippableCardCarousel_Plain {
  flippable_card_item: FlippableCard_Plain[];
  title?: string;
}

export interface FlippableCardCarousel_NoRelations {
  flippable_card_item: FlippableCard_NoRelations[];
  title?: string;
}

