// Interface automatically generated by schemas-to-ts

import { LinkText } from '../../elements/interfaces/LinkText';
import { LinkText_Plain } from '../../elements/interfaces/LinkText';
import { LinkText_NoRelations } from '../../elements/interfaces/LinkText';

export interface AnnouncementBar {
  titles: LinkText[];
  bg_color?: any;
}
export interface AnnouncementBar_Plain {
  titles: LinkText_Plain[];
  bg_color?: any;
}

export interface AnnouncementBar_NoRelations {
  titles: LinkText_NoRelations[];
  bg_color?: any;
}

