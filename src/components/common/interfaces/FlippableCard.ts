// Interface automatically generated by schemas-to-ts

import { FlippableCardItems } from './FlippableCardItems';
import { FlippableCardItems_Plain } from './FlippableCardItems';
import { FlippableCardItems_NoRelations } from './FlippableCardItems';

export interface FlippableCard {
  flippable_card_items: FlippableCardItems[];
}
export interface FlippableCard_Plain {
  flippable_card_items: FlippableCardItems_Plain[];
}

export interface FlippableCard_NoRelations {
  flippable_card_items: FlippableCardItems_NoRelations[];
}

