// TODO: need to call this across the application and replace the old api calls
// "use server";

// import { HttpTypes } from "@medusajs/types";
// import { sdk } from "@/libs/medusaClient";

// export const listProducts = async ({
//   pageParam = 1,
//   queryParams,
//   countryCode,
//   regionId,
// }: {
//   pageParam?: number;
//   queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams;
//   countryCode?: string;
//   regionId?: string;
// }): Promise<{
//   response: { products: HttpTypes.StoreProduct[]; count: number };
//   nextPage: number | null;
//   queryParams?: HttpTypes.FindParams & HttpTypes.StoreProductParams;
// }> => {
//   if (!countryCode && !regionId) {
//     throw new Error("Country code or region ID is required");
//   }

//   const limit = queryParams?.limit || 12;
//   const _pageParam = Math.max(pageParam, 1);
//   const offset = _pageParam === 1 ? 0 : (_pageParam - 1) * limit;

//   let region: HttpTypes.StoreRegion | undefined | null;

//   return sdk.client
//     .fetch<{ products: HttpTypes.StoreProduct[]; count: number }>(
//       `/store/products`,
//       {
//         method: "GET",
//         query: {
//           limit,
//           offset,
//           region_id: region?.id,
//           fields:
//             "*variants.calculated_price,+variants.inventory_quantity,+metadata,+tags",
//           ...queryParams,
//         },
//         cache: "force-cache",
//       }
//     )
//     .then(({ products, count }) => {
//       const nextPage = count > offset + limit ? pageParam + 1 : null;

//       return {
//         response: {
//           products,
//           count,
//         },
//         nextPage: nextPage,
//         queryParams,
//       };
//     });
// };
