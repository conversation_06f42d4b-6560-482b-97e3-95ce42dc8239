import { getStrapiGlobalSettings } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";

export async function fetchGlobalSettings(): Promise<FetchResult> {
  try {
    const globalSettings = await getStrapiGlobalSettings();
    return {
      success: true,
      data: globalSettings,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to fetch global settings",
    };
  }
}
