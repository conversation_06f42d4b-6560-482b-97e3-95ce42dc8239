import { getStrapiPageDetails } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";

interface PageFetchParams {
  pageHandle?: string;
  collectionHandle?: string;
  productHandle?: string;
}

export async function fetchPageData({
  pageHandle,
  collectionHandle,
  productHandle,
}: PageFetchParams): Promise<FetchResult> {
  try {
    if (!pageHandle && !collectionHandle && !productHandle) {
      return { success: false, error: "No handle provided" };
    }

    const strapiPage = await getStrapiPageDetails({
      pageHandle,
      collectionHandle,
      productHandle,
    });

    if (!strapiPage) {
      return { success: false, error: "Page not found in Strapi" };
    }

    return {
      success: true,
      data: strapiPage,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch page data",
    };
  }
}
