import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";
import { Product } from "@/components/Common/ColouredProductCard";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import SingleBanner from "@/components/Common/SingleBanner";
import ColouredProductCarouselSection from "@/components/Sections/ColouredProductCarouselSection";
import MindfulIndulgenceSection from "@/components/Sections/MindfulIndulgenceSection";
import React from "react";

const page = () => {
  const bannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/heroes/milk_chocolate_mobile.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: null,
      url: "/images/heroes/milk_chocolate_desktop.png",
      mime: "image/png",
    },
  };
  const accentCardProducts: Product[] = [
    {
      id: "1",
      title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
      image: "/images/products/milk_chocolate/1.png",
      price: 4777,
      originalPrice: 4499, // 1% discount
      isStartingPrice: false,
      rating: 2.5,
      weight: "8 x 27 g",
      primaryColor: "#DD8A03", // matches double cocoa
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      cardBgColor: "#FFF7E7",
    },
    {
      id: "2",
      title: "Everyone Party - Pack of 16 Mini Protein Bars",
      image: "/images/products/milk_chocolate/2.png",
      price: 960,
      isStartingPrice: false,
      rating: 4.1,
      weight: "16 x 27 g",
      primaryColor: "#935FC7",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      cardBgColor: "#F9F5FB",
    },
    {
      id: "3",
      title: "Personalised Box - Pack of 24 Mini Protein Bars",
      image: "/images/products/milk_chocolate/3.png",
      price: 1440,
      isStartingPrice: true,
      rating: 4.7,
      weight: "24 x 27 g",
      primaryColor: "#D83BB3",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      cardBgColor: "#FDF2FA",
    },
  ];

  return (
    <>
      <SingleBanner image={bannerImage} />

      <ColouredProductCarouselSection
        title="Shop The Range"
        products={accentCardProducts}
        backgroundColor="#FFECC7"
        titleColor="#000000"
        hideScrollbar={true}
        enableProductBg={false}
      />

      <FeatureHighlightSection
        title="TWT Chocolate Factory"
        textColor="text-[#1a181e]"
        subtitle="Bean to Bar. All in house"
        subtitleColor="#E78200"
        backgroundColor="#FFF7E7"
        images={[
          "/images/products/milk_chocolate/features/1.png",
          "/images/products/milk_chocolate/features/2.webp",
          "/images/products/milk_chocolate/features/3.webp",
          "/images/products/milk_chocolate/features/4.webp",
          "/images/products/milk_chocolate/features/5.webp",
          "/images/products/milk_chocolate/features/6.webp",
        ]}
      />

      <MindfulIndulgenceSection
        heading="Indulge"
        subheading="But mindfully"
        imageUrl="/images/products/milk_chocolate/full_banner/1.webp"
        bgColor="#ffecc7"
        headingColor="#1a181e"
        subheadingColor="#E78200"
        mobileImages={[
          "/images/products/milk_chocolate/Indulge/1.webp",
          "/images/products/milk_chocolate/Indulge/2.webp",
          "/images/products/milk_chocolate/Indulge/3.webp",
          "/images/products/milk_chocolate/Indulge/4.webp",
          "/images/products/milk_chocolate/Indulge/5.webp",
        ]}
      />
      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default page;
