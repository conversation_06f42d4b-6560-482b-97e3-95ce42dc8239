"use client";

import PageBreadcrumb from "@/components/Common/PageBreadcrumb";
import ProductInfoHeader from "@/components/Common/ProductInfoHeader";
import { Product, ProductGrid } from "@/components/Common/ProductCard";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";

export default function miniProteinBar() {
  const miniProteinBarsData: Product[] = [
    {
      id: "1",
      title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
      image: "/images/products/mini_protein_bars/1.png",
      price: 4443,
      // originalPrice: 4499, // 1% discount
      isStartingPrice: false,
      rating: 2.5,
      weight: "8 x 27 g",
      primaryColor: "#93385D", // matches double cocoa
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "2",
      title: "Everyone Party - Pack of 16 Mini Protein Bars",
      image: "/images/products/mini_protein_bars/1.png",
      price: 960,
      isStartingPrice: false,
      rating: 4.1,
      weight: "16 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "3",
      title: "Personalised Box - Pack of 24 Mini Protein Bars",
      image: "/images/products/mini_protein_bars/1.png",
      price: 1440,
      isStartingPrice: true,
      rating: 4.7,
      weight: "24 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      buttonAction: {
        type: "buildYourOwnBox",
        redirectSlug: "/build-your-own-mini-protein-box",
      },
    },
    {
      id: "4",
      title: "Personalised Box - Pack of 48 Mini Protein Bars",
      image: "/images/products/mini_protein_bars/1.png",
      price: 2880,
      isStartingPrice: true,
      rating: 4.2,
      weight: "48 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      buttonAction: {
        type: "buildYourOwnBox",
        redirectSlug: "/build-your-own-mini-protein-box",
      },
    },
    {
      id: "5",
      title: "Double Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/mini_protein_bars/2.png",
      price: 720,
      isStartingPrice: false,
      rating: 4.5,
      weight: "12 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "6",
      title: "Cranberry Mini Protein Bars - Box of 12",
      image: "/images/products/mini_protein_bars/3.png",
      price: 720,
      originalPrice: 800, // 10% discount
      isStartingPrice: false,
      rating: 2.4,
      weight: "12 x 27 g",
      primaryColor: "#D23C47", // red tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "7",
      title: "Peanut Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/mini_protein_bars/4.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#F05C1D", // orange tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "8",
      title: "Coffee Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/mini_protein_bars/5.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#59382B", // brown tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
  ];

  return (
    <main>
      <PageBreadcrumb items={[{ label: "Home", href: "/" }]} />
      <ProductInfoHeader
        title="Mini Protein Bars"
        hashtag="#mini. yet mighty."
        description="Your perfect, low-cal answer to that anytime snack attack!"
      />

      <div className="px-4 py-8">
        <ProductGrid products={miniProteinBarsData} />
      </div>

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </main>
  );
}
