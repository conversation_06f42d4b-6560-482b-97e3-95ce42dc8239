"use client";

import PageBreadcrumb from "@/components/Common/PageBreadcrumb";
import { ProductGrid } from "@/components/Common/ProductCard";
import ProductInfoHeader from "@/components/Common/ProductInfoHeader";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import React from "react";

const page = () => {
  const muesliData = [
    {
      id: "1",
      title: "Choco Fruit Crunch Muesli - Pack of - 350g x 1",
      image: "/images/products/muesli/1.png",
      price: 1125,
      isStartingPrice: true,
      rating: 5,
      weight: "6 x 50 g",
      primaryColor: "#C866A7", // blue tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      variants: [
        {
          id: "6-1",
          size: "1 x 350g",
          weight: "350g",
          price: 720,
          originalPrice: 800, // 10% discount
          image: "/images/products/muesli/1.png",
        },
        {
          id: "6-2",
          size: "2 x 350g",
          weight: "700g",
          price: 1350,
          originalPrice: 1500, // 10% discount
          image: "/images/products/muesli/1.png",
        },
        {
          id: "6-3",
          size: "3 x 350g",
          weight: "1050g",
          price: 1950,
          originalPrice: 2200, // ~11% discount
          image: "/images/products/muesli/1.png",
        },
      ],
    },
    {
      id: "2",
      title: "Personalised Box - Pack of 25 Energy Bars",
      image: "/images/products/muesli/2.png",
      price: 1875,
      isStartingPrice: true,
      rating: 5,
      weight: "6 x 50 g",
      primaryColor: "#F89ABA", // blue tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      variants: [
        {
          id: "6-1",
          size: "1 x 350g",
          weight: "350g",
          price: 720,
          originalPrice: 800, // 10% discount
          image: "/images/products/muesli/2.png",
        },
        {
          id: "6-2",
          size: "2 x 350g",
          weight: "700g",
          price: 1350,
          originalPrice: 1500, // 10% discount
          image: "/images/products/muesli/2.png",
        },
        {
          id: "6-3",
          size: "3 x 350g",
          weight: "1050g",
          price: 1950,
          originalPrice: 2200, // ~11% discount
          image: "/product-peanut.png",
        },
      ],
    },
    {
      id: "3",
      title: "Choco Fruit Crunch Muesli - Super Saver Pack of 1.5kg",
      image: "/images/products/muesli/3.png",
      price: 750,
      isStartingPrice: false,
      rating: 5,
      weight: "10 x 40 g",
      primaryColor: "#C866A7", // blue tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "4",
      title: "No Added Sugar 5 Grain Muesli - Pack of - 350g x 1",
      image: "/images/products/muesli/4.png",
      price: 750,
      isStartingPrice: false,
      rating: 5,
      weight: "10 x 40 g",
      primaryColor: "#F89ABA", // pink/magenta tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "5",
      title: "No Added Sugar 5 Grain Muesli - Pack of - 350g x 1",
      image: "/images/products/muesli/5.png",
      price: 750,
      isStartingPrice: false,
      rating: 5,
      weight: "10 x 40 g",
      primaryColor: "#FFC600", // purple tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
  ];

  return (
    <>
      <PageBreadcrumb items={[{ label: "Home", href: "/" }]} />
      <ProductInfoHeader
        title="Muesli"
        hashtag="#cerealkilla"
        description="A great breakfast would give you sustained energy, balanced nutrition (not just carbs), and spectacular yumms. This is a great breakfast."
      />
      <div className="px-4 py-8">
        <ProductGrid products={muesliData} />
      </div>

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default page;
