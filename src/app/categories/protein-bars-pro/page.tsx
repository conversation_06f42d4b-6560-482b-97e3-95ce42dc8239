"use client";
import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";
import VideoFeatureSection from "@/components/blocks/VideoFeatureSection";
import AccentProductCard from "@/components/Common/AccentProductCard";
import ImageCarousel from "@/components/Common/BannerCarousel";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import FlavorShowcaseSection from "@/components/Common/FlavorShowcase";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import SeactionHeading from "@/components/Common/Section/Heading";
import { PageTypeEnum } from "@/types/Page";
import React from "react";

const page = () => {


  const accentCardProducts = [
    {
      id: 1,
      title: "Hazelnut Cocoa Pro - Box of 5",
      price: 1000.0,
      originalPrice: 1200.0,
      image: "/images/products/protein_bars_pro/1.webp",
      rating: 4.5,
      reviewCount: 47,
      description: "A dessert junkie and a protein lover's dream come true.",
      deliveryInfo: "Same day delivery",
      weight: "5 x 67g",
      enableProductBg: true,
      primaryColor: "#7E4015",
    },
    {
      id: 2,
      title: "All in One - Protein Bars Pro - Box of 5",
      price: 800.0,
      originalPrice: 950.0,
      image: "/images/products/protein_bars_pro/2.webp",
      rating: 4.5,
      reviewCount: 37,
      description: "Fuel for balance and routine, four indulgent flavours.",
      deliveryInfo: "Same day delivery",
      weight: "5 x 67g",
      enableProductBg: true,
      primaryColor: "#7E4015",
    },
    {
      id: 3,
      title: "Personalised Box - Pack of 10",
      price: 1500.0,
      originalPrice: 1800.0,
      image: "/images/products/protein_bars_pro/3.webp",
      rating: 4.5,
      reviewCount: 47,
      description: "Fuel for balance and routine, four indulgent flavours.",
      deliveryInfo: "Same day delivery",
      weight: "10 x 67g",
      enableProductBg: true,
      primaryColor: "#3F2021",
    },
    {
      id: 4,
      title: "Double Cocoa Pro - Box of 5",
      price: 750.0,
      originalPrice: 900.0,
      image: "/images/products/protein_bars_pro/4.webp",
      rating: 4.5,
      reviewCount: 47,
      description: "It's hard not to go loco for the classic Double Cocoa.",
      deliveryInfo: "Same day delivery",
      weight: "5 x 67g",
      enableProductBg: true,
      primaryColor: "#6F1D46",
    },
    {
      id: 5,
      title: "Coffee Cocoa Pro - Box of 5",
      price: 750.0,
      originalPrice: 399.0,
      image: "/images/products/protein_bars_pro/5.webp",
      rating: 5,
      reviewCount: 47,
      description:
        "Every coffeeholic's pick, the perfect snack for that pre-workout pick-me-up.",
      deliveryInfo: "Same day delivery",
      weight: "5 x 67g",
      enableProductBg: true,
      primaryColor: "#7E4015",
    },
    {
      id: 6,
      title: "Coffee Cocoa Pro - Box of 5",
      price: 299.0,
      originalPrice: 399.0,
      image: "/images/products/protein_bars_pro/6.webp",
      rating: 5,
      reviewCount: 47,
      description:
        "For the protein shakers and PR breakers, the fitness freaks and health nuts!",
      deliveryInfo: "Same day delivery",
      weight: "5 x 67g",
      enableProductBg: true,
      primaryColor: "#CC3704",
    },
  ];

  // Banner images for ImageCarousel
  const bannerBlock = {
    images: [
      {
        desktop: {
          media: { url: "/images/banners/hazle_nut_1.webp" },
          action_link: "/products/protein-bars",
        },
        mobile: {
          media: { url: "/images/banners/hazle_nut_1_mobile.png" },
          action_link: "/products/protein-bars",
        },
        redirect_link: {
          title: "SHOP NOW",
          link: "/products/protein-bars",
        },
      },
      {
        desktop: {
          media: { url: "/images/banners/hazel_nut_2.webp" },
          action_link: "/products/protein-bars-premium",
        },
        mobile: {
          media: { url: "/images/banners/hazle_nut_2_mobile.png" },
          action_link: "/products/protein-bars-premium",
        },
        redirect_link: {
          title: "SHOP NOW",
          link: "/products/protein-bars-premium",
        },
      },
    ],
    auto_play: true,
    position: 0,
    show: true,
  };

  return (
    <div>
      {/* Banner Carousel */}
      <ImageCarousel
        block={{
          images: bannerBlock.images.map((image) => ({
            desktop: {
              media: {
                url: image.desktop.media.url,
                name: "",
                alternativeText: "",
                width: 0,
                height: 0,
                mime: "image/webp",
              },
              action_link: image.desktop.action_link,
            },
            mobile: {
              media: {
                url: image.mobile.media.url,
                name: "",
                alternativeText: "",
                width: 0,
                height: 0,
                mime: "image/webp",
              },
              action_link: image.mobile.action_link,
            },
            redirect_link: image.redirect_link,
          })),
          auto_play: bannerBlock.auto_play,
          position: bannerBlock.position,
          show: bannerBlock.show,
        }}
        collectionData={{
          strapi: {
            title: "Protein Bars Pro",
            banner_image_carousel: [{ images: bannerBlock.images }],
          },
          title: "Protein Bars Pro",
        }}
        pageType={PageTypeEnum.Page}
        page="category"
        priority={true}
      />

      <div className="bg-[#1A181E] py-8">
        <div className="max-w-6xl mx-auto px-4">
          <SeactionHeading title="Shop The Range" color="#FFFFFF" />
          <div className="mt-7.5">
            <UniversalCarousel useNativeScrollbar={true} hideScrollbar={true}>
              {accentCardProducts.map((card) => (
                <AccentProductCard
                  key={card.id}
                  product={{ ...card, id: card.id.toString() }}
                />
              ))}
            </UniversalCarousel>
          </div>
        </div>
      </div>

      <div className="bg-[#1A181E] pb-10 px-4">
        <FlavorShowcaseSection />
      </div>

      <FeatureHighlightSection
        title="No Added Sugar"
        subtitleColor="#C5C3C4"
        subtitle="Or any sugar substitutes"
        backgroundColor="bg-black"
        textColor="text-white"
        images={[
          "/images/products/protein_bars_pro/features/1.png",
          "/images/products/protein_bars_pro/features/2.png",
          "/images/products/protein_bars_pro/features/3.webp",
        ]}
      />

      <VideoFeatureSection
        videoSrc="https://www.youtube.com/embed/6BGCC4zqUCA?si=n9QWXzirJcu04vi_"
        ribbonImagePath="/images/products/protein_bars_pro/ribbon.png"
        backgroundColor="#313131"
      />

      <FeatureHighlightSection
        title="Real Protein"
        subtitle="Real Health. Real Indulgence."
        subtitleColor="#C5C3C4"
        backgroundColor="bg-black"
        textColor="text-white"
        images={[
          "/images/products/protein_bars_pro/more-features/1.png",
          "/images/products/protein_bars_pro/more-features/2.png",
          "/images/products/protein_bars_pro/more-features/3.png",
          "/images/products/protein_bars_pro/more-features/4.webp",
          "/images/products/protein_bars_pro/more-features/5.webp",
        ]}
      />

      {/* <PaymentMethodsBar /> */}
      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </div>
  );
};

export default page;
