import DynamicPage from "@/components/DynamicPage";
import { getPageDetails } from "@/libs/middlewareAPIs";
import { PageTypeEnum } from "@/types/Page";
import React from "react";

const page = async ({ params }: { params: Promise<{ dynamic: string }> }) => {
  const { dynamic } = await params;
  const pagesData = await getPageDetails({
    pageHandle: dynamic,
    productHandle: dynamic,
    collectionHandle: dynamic,
  });

  console.log(`pagesData`, pagesData);

  return (
    <DynamicPage
      page={dynamic}
      pageType={PageTypeEnum.Product}
      pageData={pagesData}
    />
  );
};

export default page;
