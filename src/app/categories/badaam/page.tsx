"use client";

import CompactIngrediants from "@/components/blocks/CompactIngrediants";
import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";
import ProductHighlight from "@/components/blocks/ProductHighlight";

import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import Image from "next/image";
import React from "react";
import FAQAccordion from "@/components/Common/FAQAccordion";

const page = () => {
  return (
    <>
      <div className="w-full relative">
        <div className="aspect-square lg:aspect-[3/1]">
          <Image
            src="/images/banners/badaam_desktop.png"
            alt="landing page desktop"
            fill
            style={{ objectFit: "fill" }}
            priority
            className="hidden lg:block"
          />
          <Image
            src="/images/heroes/beginner_hero_mobile.png"
            alt="landing page mobile"
            fill
            style={{ objectFit: "fill" }}
            priority
            className="block lg:hidden"
          />
        </div>
      </div>

      <FeatureHighlightSection
        title="Dont buy names. Buy what they mean."
        textColor="text-black"
        images={[
          "/images/products/badaam/features/1.png",
          "/images/products/badaam/features/2.png",
          "/images/products/badaam/features/3.png",
        ]}
      />

      <CompactIngrediants />

      <ProductHighlight />

      <FeatureHighlightSection
        title="Thinking?"
        erasedText="Still"
        visibleText="Stop"
        subtitleClassName="text-[#c5c3c4]"
        backgroundColor="bg-[#f3d4e9]"
        textColor="text-black"
        visibleTextColor="#be259a"
        images={[
          "/images/products/badaam/highlight/7.webp",
          "/images/products/badaam/highlight/8.webp",
          "/images/products/badaam/highlight/8.webp",
          "/images/products/badaam/highlight/9.webp",
          "/images/products/badaam/highlight/10.webp",
        ]}
      />

      <FeatureHighlightSection
        title="Walk through the Chocolate Factory"
        textColor="text-black"
        images={[
          "/images/products/badaam/more-features/1.png",
          "/images/products/badaam/more-features/2.png",
          "/images/products/badaam/more-features/3.png",
          "/images/products/badaam/more-features/4.webp",
          "/images/products/badaam/more-features/5.webp",
          "/images/products/badaam/more-features/6.webp",
          "/images/products/badaam/more-features/7.webp",
        ]}
      />

      {/* FAQ Section */}
      <section className="py-6 lg:py-12 px-6 lg:px-0 bg-[#f3d4e9]">
        {/* title */}
        <h2 className="font-narrow text-[32px] font-semibold text-center mb-10">
          FAQs
        </h2>

        <div className="max-w-[960px] mx-auto">
          <FAQAccordion
            borderColor="#be259a"
            iconColor="#be259a"
            items={[
              {
                id: "item-1",
                question: 'Does "no added sugar" means sugar free chocolate?',
                answer:
                  'No added sugar simply means that the product contains "no refined sugar" and no sugar from Sugarcane as a source. It however does not mean that the chocolate contains no sweetener. These chocolates are sweetened by dates, a natural sweetener with a low Glycemic Index. Natural sugar from dates is unrefined and contains fiber. Other sugar-free chocolates are often made with substitute sweeteners such as maltitol, stevia, or Fucto-oligo-sacchrides.',
              },
              {
                id: "item-2",
                question: "Is this okay for diabetics to consume?",
                answer:
                  "Our Milk Chocolate do not have any added sugar, it only contains natural sugar. We recommend consumption in moderation. However, its advisable to consult one's doctor before consuming any product, to minimise risk.",
              },
              {
                id: "item-3",
                question: 'Does "no added sugar" means sugar free chocolate?',
                answer:
                  'No, "no added sugar" means we don\'t add any extra sugar during production. The product still contains natural sugars from ingredients like milk and cocoa.',
              },
            ]}
          />
        </div>
      </section>

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default page;
