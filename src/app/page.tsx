/* eslint-disable @typescript-eslint/no-unused-vars */
import { AnnouncementBar } from "@/components/Common/AnnouncementBar";
import PageBreadcrumb from "@/components/Common/PageBreadcrumb";
import ProductInfoHeader from "@/components/Common/ProductInfoHeader";
import { ProductGrid } from "@/components/Common/ProductCard";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import FooterNav from "@/components/Common/FooterNav";

export default function Home() {
  return (
    <>
      <main>
        <h1>hello</h1>
        {/* Another example of secondary variant */}
        {/* <AnnouncementBar
          display={true}
          variant="secondary"
          messages={[
            { text: "Cash on delivery" },
            { text: "Free shipping to anywhere in India" },
            { text: "Order dispatch within 24-48 hours" },
          ]}
        /> */}

        {/* <Img
        src={"/milk-chocolate-landing-desktop.png"}
        alt="landing page"
        height={444}
        width={1366}
      /> */}

        {/* <PageBreadcrumb items={[{ label: "Home", href: "/" }]} />
        <ProductInfoHeader
          title="Mini Protein Bars"
          hashtag="#mini. yet mighty."
          description="Your perfect, low-cal answer to that anytime snack attack!"
        /> */}

        {/* <div className="px-4 py-8"> */}
        {/* <div className="container mx-auto mb-6 text-center">
          <Link
            href="/discount-demo"
            className="inline-block px-6 py-3 bg-[#93385D] text-white rounded-md font-medium hover:bg-opacity-90 transition-colors"
          >
            View Discount Price Display Demo
          </Link>
        </div> */}
        {/* <ProductGrid />
        </div> */}

        {/* <PaymentMethodsBar
          paymentMethods={[
            { id: "paytm", name: "Paytm", imageSrc: "/paytm.png" },
            { id: "visa", name: "Visa", imageSrc: "/visa.png" },
            {
              id: "mastercard",
              name: "Mastercard",
              imageSrc: "/mastercard.png",
            },
            {
              id: "cred",
              name: "CRED",
              imageSrc: "/cred.png",
            },
            {
              id: "american-express",
              name: "American Express",
              imageSrc: "/gpay.png",
            },
            {
              id: "phonepe",
              name: "PhonePe",
              imageSrc: "/phonepe.png",
            },
          ]}
        /> */}
      </main>
    </>
  );
}
