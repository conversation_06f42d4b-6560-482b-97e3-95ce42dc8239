import { RefObject, useCallback } from 'react';

interface ScrollToTabOptions {
  behavior?: ScrollBehavior;
  centerTab?: boolean;
}

/**
 * Custom hook for scrolling to a tab in a horizontally scrollable container
 *
 * @param containerRef - Reference to the container element
 * @returns A function to scroll to a specific tab index
 */
const useScrollToTab = (containerRef: RefObject<HTMLElement | null>) => {
  return useCallback(
    (index: number, options: ScrollToTabOptions = {}) => {
      const { behavior = 'smooth', centerTab = true } = options;
      const container = containerRef.current;
      if (!container) return;

      const tab = container.children[index] as HTMLElement;
      if (!tab) return;

      const containerWidth = container.offsetWidth;
      const tabLeft = tab.offsetLeft;
      const tabWidth = tab.offsetWidth;

      const isTabOutOfView =
        tabLeft < container.scrollLeft ||
        tabLeft + tabWidth > container.scrollLeft + containerWidth;

      if (isTabOutOfView || !centerTab) {
        const scrollPosition = centerTab ? tabLeft - containerWidth / 2 + tabWidth / 2 : tabLeft;

        container.scrollTo({ left: scrollPosition, behavior });
      }
    },
    [containerRef],
  );
};

export default useScrollToTab;
