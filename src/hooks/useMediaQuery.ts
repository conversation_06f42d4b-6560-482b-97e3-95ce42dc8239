import { useEffect, useState } from 'react';

export default function useMediaQuery(query: string) {
  const [value, setValue] = useState(false);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    function onChange(event: MediaQueryListEvent) {
      setValue(event.matches);
    }

    const mediaQuery = window.matchMedia(query);
    setValue(mediaQuery.matches);

    mediaQuery.addEventListener('change', onChange);
    return () => mediaQuery.removeEventListener('change', onChange);
  }, [query]);

  return value;
}
