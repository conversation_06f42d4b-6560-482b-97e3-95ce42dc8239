import { useCallback, useEffect, useRef, useState } from 'react';

interface UseInfiniteLoaderOptions {
  onIntersection: () => void | Promise<void>;
  enabled?: boolean;
  threshold?: number;
  rootMargin?: string;
  skipFirstIntersection?: boolean;
}

interface UseInfiniteLoaderReturn {
  loadingRef: React.RefObject<HTMLDivElement | null>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  forceIntersection: () => void;
}

/**
 * Custom hook for implementing infinite loading functionality
 * @param options Configuration options for the infinite loader
 * @returns Object containing the ref to attach to loading element and loading state
 */
export const useInfiniteLoader = ({
  onIntersection,
  enabled = true,
  threshold = 0.1,
  rootMargin = '0px',
  skipFirstIntersection = false,
}: UseInfiniteLoaderOptions): UseInfiniteLoaderReturn => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const loadingRef = useRef<HTMLDivElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const skipFirstRef = useRef<boolean>(skipFirstIntersection);

  const handleIntersection = useCallback(async () => {
    if (!enabled || isLoading) return;

    setIsLoading(true);
    try {
      await onIntersection();
    } catch (error) {
      console.error('Error in infinite loader intersection handler:', error);
    } finally {
      setIsLoading(false);
    }
  }, [onIntersection, enabled, isLoading]);

  // Force an intersection check programmatically
  const forceIntersection = useCallback(() => {
    if (enabled && !isLoading) {
      handleIntersection();
    }
  }, [enabled, isLoading, handleIntersection]);

  useEffect(() => {
    // Store the current scroll position to restore it after observer setup
    const scrollPosition = skipFirstIntersection ? window.scrollY : 0;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;

        if (entry.isIntersecting) {
          // Skip the first intersection if required (useful when element is initially in viewport)
          if (skipFirstRef.current) {
            skipFirstRef.current = false;
            return;
          }

          handleIntersection();
        }
      },
      { threshold, rootMargin },
    );

    observerRef.current = observer;

    const currentLoadingRef = loadingRef.current;
    if (currentLoadingRef) {
      observer.observe(currentLoadingRef);

      // Restore scroll position to prevent jump
      if (skipFirstIntersection) {
        window.scrollTo({
          top: scrollPosition,
          behavior: 'auto',
        });
      }
    }

    return () => {
      if (currentLoadingRef && observerRef.current) {
        observerRef.current.unobserve(currentLoadingRef);
        observerRef.current.disconnect();
      }
    };
  }, [handleIntersection, threshold, rootMargin, skipFirstIntersection]);

  return {
    loadingRef,
    isLoading,
    setIsLoading,
    forceIntersection,
  };
};

export default useInfiniteLoader;
