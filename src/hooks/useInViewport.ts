import { RefObject, useEffect, useState } from 'react';

const useInViewport = (
  ref: RefObject<Element | null>,
  options?: IntersectionObserverInit,
): boolean => {
  const [isInViewport, setIsInViewport] = useState(false);

  useEffect(() => {
    const current = ref.current;
    if (!current) return;
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInViewport(true);
          observer.unobserve(entry.target);
        }
      },
      options || { threshold: 0.1 },
    );

    observer.observe(current);

    return () => {
      observer.unobserve(current);
    };
  }, [ref, options]);

  return isInViewport;
};

export default useInViewport;
