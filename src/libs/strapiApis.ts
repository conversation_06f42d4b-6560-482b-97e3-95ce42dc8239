import {
  getPageDetailsQuery,
  getProductDetailsQuery,
} from "@/graphql/pageQuery";
import {
  getFooterQuery,
  getGlobalSettingsQuery,
  getHeaderQuery,
} from "@/graphql/queries";
import { FooterType } from "@/types/Collections/Footer";
import { HeaderType } from "@/types/Collections/Header";
import { GlobalSettingType } from "@/types/Collections/GlobalSettings";
import { PageType } from "@/types/Page";
import strapiGraphqlFetch from "@/utils/strapiFetch";

export const getStrapiFooter = async (): Promise<FooterType | null> => {
  try {
    const result = await strapiGraphqlFetch({
      query: getFooterQuery,
    });

    return result.footer || null;
  } catch (error) {
    console.error("Error fetching footer data:", error);
    return null;
  }
};

export const getStrapiHeader = async (): Promise<HeaderType | null> => {
  try {
    const result = await strapiGraphqlFetch({
      query: getHeaderQuery,
    });

    return result.header || null;
  } catch (error) {
    console.error("Error fetching header data:", error);
    return null;
  }
};

export const getStrapiPageDetails = async ({
  pageHandle,
  collectionHandle,
  productHandle,
}: {
  pageHandle?: string;
  collectionHandle?: string;
  productHandle?: string;
}): Promise<PageType | null> => {
  try {
    const result = await strapiGraphqlFetch({
      query: getPageDetailsQuery,
      variables: {
        filters: {
          handle: {
            eq: pageHandle,
          },
        },
      },
    });

    return result.pages?.[0] || null;
  } catch (error) {
    console.error(
      "Error fetching page details:",
      pageHandle || collectionHandle || productHandle,
      error
    );
    return null;
  }
};

export const getStrapiProductDetails = async ({
  productHandle,
}: {
  productHandle?: string;
}): Promise<PageType | null> => {
  try {
    const result = await strapiGraphqlFetch({
      query: getProductDetailsQuery,
      variables: {
        filters: {
          handle: {
            eq: productHandle,
          },
        },
      },
    });

    return result.products?.[0] || null;
  } catch (error) {
    console.error("Error fetching page details:", productHandle, error);
    return null;
  }
};

export const getStrapiGlobalSettings =
  async (): Promise<GlobalSettingType | null> => {
    try {
      const result = await strapiGraphqlFetch({
        query: getGlobalSettingsQuery,
      });

      return result.globalSetting || null;
    } catch (error) {
      console.error("Error fetching global settings:", error);
      return null;
    }
  };
