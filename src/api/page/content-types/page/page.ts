// Interface automatically generated by schemas-to-ts

import { Category } from '../../../category/content-types/category/category';
import { Product } from '../../../product/content-types/product/product';
import { Category_Plain } from '../../../category/content-types/category/category';
import { Product_Plain } from '../../../product/content-types/product/product';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export enum PageTypeEnum {
  Page = 'PAGE',
  Index = 'INDEX',
  Category = 'CATEGORY',
  Product = 'PRODUCT',}

export interface Page {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
  categories: { data: Category[] };
  products: { data: Product[] };
}
export interface Page_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
  categories: Category_Plain[];
  products: Product_Plain[];
}

export interface Page_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
  categories: number[];
  products: number[];
}

export interface Page_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  handle: string;
  page_type: PageTypeEnum;
  blocks?: any;
  categories: AdminPanelRelationPropertyModification<Category_Plain>;
  products: AdminPanelRelationPropertyModification<Product_Plain>;
}
