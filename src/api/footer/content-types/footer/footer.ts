// Interface automatically generated by schemas-to-ts

import { Media } from '../../../../common/schemas-to-ts/Media';
import { SocialMedia } from '../../../../components/common/interfaces/SocialMedia';
import { FooterMenu } from '../../../../components/layout/interfaces/FooterMenu';
import { Media_Plain } from '../../../../common/schemas-to-ts/Media';
import { SocialMedia_Plain } from '../../../../components/common/interfaces/SocialMedia';
import { FooterMenu_Plain } from '../../../../components/layout/interfaces/FooterMenu';
import { SocialMedia_NoRelations } from '../../../../components/common/interfaces/SocialMedia';
import { FooterMenu_NoRelations } from '../../../../components/layout/interfaces/FooterMenu';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Footer {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  logo?: { data: Media };
  newsletter_title?: string;
  newsletter_description?: string;
  social_media: SocialMedia[];
  newsletter_icon?: { data: Media };
  footer_links: FooterMenu[];
}
export interface Footer_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  logo?: Media_Plain;
  newsletter_title?: string;
  newsletter_description?: string;
  social_media: SocialMedia_Plain[];
  newsletter_icon?: Media_Plain;
  footer_links: FooterMenu_Plain[];
}

export interface Footer_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  logo?: number;
  newsletter_title?: string;
  newsletter_description?: string;
  social_media: SocialMedia_NoRelations[];
  newsletter_icon?: number;
  footer_links: FooterMenu_NoRelations[];
}

export interface Footer_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  logo?: AdminPanelRelationPropertyModification<Media_Plain>;
  newsletter_title?: string;
  newsletter_description?: string;
  social_media: SocialMedia_Plain[];
  newsletter_icon?: AdminPanelRelationPropertyModification<Media_Plain>;
  footer_links: FooterMenu_Plain[];
}
