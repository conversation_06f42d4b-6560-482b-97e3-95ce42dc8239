{"kind": "collectionType", "collectionName": "product_variants", "info": {"singularName": "product-variant", "pluralName": "product-variants", "displayName": "Product Variant", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "string"}, "systemId": {"type": "string", "required": true, "unique": true}, "sku": {"type": "string"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::product.product", "inversedBy": "variants"}, "tag": {"type": "string"}}}