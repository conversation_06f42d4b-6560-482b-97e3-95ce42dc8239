// Interface automatically generated by schemas-to-ts

import { Product } from '../../../product/content-types/product/product';
import { Product_Plain } from '../../../product/content-types/product/product';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface ProductVariant {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  sku?: string;
  product?: { data: Product };
  tag?: string;
}
export interface ProductVariant_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  sku?: string;
  product?: Product_Plain;
  tag?: string;
}

export interface ProductVariant_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  sku?: string;
  product?: number;
  tag?: string;
}

export interface ProductVariant_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  sku?: string;
  product?: AdminPanelRelationPropertyModification<Product_Plain>;
  tag?: string;
}
