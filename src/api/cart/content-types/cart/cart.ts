// Interface automatically generated by schemas-to-ts

import { Media } from '../../../../common/schemas-to-ts/Media';
import { SearchUpsellCollections } from '../../../../components/search/interfaces/SearchUpsellCollections';
import { Button } from '../../../../components/elements/interfaces/Button';
import { Media_Plain } from '../../../../common/schemas-to-ts/Media';
import { SearchUpsellCollections_Plain } from '../../../../components/search/interfaces/SearchUpsellCollections';
import { Button_Plain } from '../../../../components/elements/interfaces/Button';
import { SearchUpsellCollections_NoRelations } from '../../../../components/search/interfaces/SearchUpsellCollections';
import { Button_NoRelations } from '../../../../components/elements/interfaces/Button';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Cart {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  description?: string;
  icon?: { data: Media };
  upsell_categories: SearchUpsellCollections[];
  button?: Button;
}
export interface Cart_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  description?: string;
  icon?: Media_Plain;
  upsell_categories: SearchUpsellCollections_Plain[];
  button?: Button_Plain;
}

export interface Cart_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  description?: string;
  icon?: number;
  upsell_categories: SearchUpsellCollections_NoRelations[];
  button?: Button_NoRelations;
}

export interface Cart_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  description?: string;
  icon?: AdminPanelRelationPropertyModification<Media_Plain>;
  upsell_categories: SearchUpsellCollections_Plain[];
  button?: Button_Plain;
}
