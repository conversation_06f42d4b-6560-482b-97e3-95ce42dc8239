// Interface automatically generated by schemas-to-ts

import { SearchKeywords } from '../../../../components/search/interfaces/SearchKeywords';
import { SearchUpsellCollections } from '../../../../components/search/interfaces/SearchUpsellCollections';
import { SearchKeywords_Plain } from '../../../../components/search/interfaces/SearchKeywords';
import { SearchUpsellCollections_Plain } from '../../../../components/search/interfaces/SearchUpsellCollections';
import { SearchKeywords_NoRelations } from '../../../../components/search/interfaces/SearchKeywords';
import { SearchUpsellCollections_NoRelations } from '../../../../components/search/interfaces/SearchUpsellCollections';

export interface Search {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  keywords?: SearchKeywords;
  upsell_categories: SearchUpsellCollections[];
}
export interface Search_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  keywords?: SearchKeywords_Plain;
  upsell_categories: SearchUpsellCollections_Plain[];
}

export interface Search_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  keywords?: SearchKeywords_NoRelations;
  upsell_categories: SearchUpsellCollections_NoRelations[];
}

export interface Search_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  keywords?: SearchKeywords_Plain;
  upsell_categories: SearchUpsellCollections_Plain[];
}
