// Interface automatically generated by schemas-to-ts

export interface Collection {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
}
export interface Collection_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
}

export interface Collection_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
}

export interface Collection_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
}
