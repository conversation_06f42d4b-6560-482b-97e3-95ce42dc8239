{"kind": "singleType", "collectionName": "headers", "info": {"singularName": "header", "pluralName": "headers", "displayName": "Header", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"menu": {"displayName": "Header <PERSON><PERSON>", "type": "component", "repeatable": true, "component": "header.header-menu"}, "logo": {"type": "component", "repeatable": false, "component": "media.image"}, "social_media": {"type": "component", "repeatable": true, "component": "common.social-media"}, "external_links": {"type": "component", "repeatable": false, "component": "header.external-links"}}}