// Interface automatically generated by schemas-to-ts

import { HeaderMenu } from '../../../../components/header/interfaces/HeaderMenu';
import { Image } from '../../../../components/media/interfaces/Image';
import { SocialMedia } from '../../../../components/common/interfaces/SocialMedia';
import { ExternalLinks } from '../../../../components/header/interfaces/ExternalLinks';
import { HeaderMenu_Plain } from '../../../../components/header/interfaces/HeaderMenu';
import { Image_Plain } from '../../../../components/media/interfaces/Image';
import { SocialMedia_Plain } from '../../../../components/common/interfaces/SocialMedia';
import { ExternalLinks_Plain } from '../../../../components/header/interfaces/ExternalLinks';
import { HeaderMenu_NoRelations } from '../../../../components/header/interfaces/HeaderMenu';
import { Image_NoRelations } from '../../../../components/media/interfaces/Image';
import { SocialMedia_NoRelations } from '../../../../components/common/interfaces/SocialMedia';
import { ExternalLinks_NoRelations } from '../../../../components/header/interfaces/ExternalLinks';

export interface Header {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  menu: HeaderMenu[];
  logo?: Image;
  social_media: SocialMedia[];
  external_links?: ExternalLinks;
}
export interface Header_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  menu: HeaderMenu_Plain[];
  logo?: Image_Plain;
  social_media: SocialMedia_Plain[];
  external_links?: ExternalLinks_Plain;
}

export interface Header_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  menu: HeaderMenu_NoRelations[];
  logo?: Image_NoRelations;
  social_media: SocialMedia_NoRelations[];
  external_links?: ExternalLinks_NoRelations;
}

export interface Header_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  menu: HeaderMenu_Plain[];
  logo?: Image_Plain;
  social_media: SocialMedia_Plain[];
  external_links?: ExternalLinks_Plain;
}
