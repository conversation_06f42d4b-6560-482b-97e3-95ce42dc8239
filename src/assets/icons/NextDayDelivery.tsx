import * as React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const NextDayDevlivery = ({ color = "#00693B", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="140"
    height="25"
    fill="none"
    viewBox="0 0 140 25"
    {...props}
  >
    <path
      fill={color}
      d="M136.992 22.69a2.66 2.66 0 0 1-2.638 2.31H3.036A2.66 2.66 0 0 1 .4 21.99L3.007 2.31A2.66 2.66 0 0 1 5.644 0h131.32a2.66 2.66 0 0 1 2.637 3.01z"
    ></path>
  </svg>
);

export default NextDayDevlivery;
