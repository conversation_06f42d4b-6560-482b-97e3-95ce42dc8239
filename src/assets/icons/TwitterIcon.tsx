type IconProps = React.SVGProps<SVGSVGElement>;

export function TwitterIcon(props: IconProps) {
  return (
    <svg 
      width="26" 
      height="22" 
      viewBox="0 0 26 22" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M12.6204 6.34268L12.6735 7.21813L11.7886 7.11093C8.56777 6.70001 5.75396 5.30645 3.36487 2.96598L2.19687 1.80468L1.89602 2.66225C1.25894 4.57393 1.66596 6.59281 2.99323 7.95064C3.70111 8.70102 3.54184 8.80822 2.32075 8.36156C1.89602 8.21863 1.52439 8.11144 1.489 8.16503C1.36512 8.2901 1.78984 9.91592 2.12608 10.5591C2.5862 11.4524 3.52414 12.3279 4.55056 12.846L5.41771 13.2569L4.39129 13.2748C3.40026 13.2748 3.36487 13.2926 3.47105 13.6678C3.82499 14.8291 5.22305 16.0619 6.78038 16.5979L7.87759 16.9731L6.92195 17.5448C5.5062 18.3666 3.84269 18.8311 2.17918 18.8669C1.38281 18.8847 0.728027 18.9562 0.728027 19.0098C0.728027 19.1885 2.88705 20.189 4.14353 20.582C7.91298 21.7433 12.3903 21.2431 15.7527 19.2599C18.1418 17.8485 20.5309 15.0435 21.6458 12.3279C22.2475 10.8807 22.8492 8.2365 22.8492 6.968C22.8492 6.14616 22.9023 6.03896 23.8933 5.05632C24.4773 4.4846 25.0259 3.85929 25.1321 3.68063C25.3091 3.34117 25.2914 3.34117 24.3888 3.64489C22.8846 4.18088 22.6722 4.10941 23.4155 3.30544C23.9641 2.73372 24.6189 1.69748 24.6189 1.39376C24.6189 1.34016 24.3534 1.42949 24.0526 1.59028C23.734 1.76895 23.0262 2.03694 22.4953 2.19773L21.5396 2.50146L20.6725 1.91188C20.1947 1.59028 19.5222 1.23296 19.1682 1.12576C18.2657 0.875637 16.8853 0.91137 16.0713 1.19723C13.8591 2.00121 12.4611 4.07368 12.6204 6.34268Z" 
        fill="currentColor"
      />
    </svg>
  );
}

export default TwitterIcon;
