import React from "react";

interface ChevronDownProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

export const ChevronDown: React.FC<ChevronDownProps> = ({
  width = 24,
  height = 24,
  color = "#BE259A",
  className = "",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.29289 9.29289C4.68342 8.90237 5.31658 8.90237 5.70711 9.29289L12 15.5858L18.2929 9.29289C18.6834 8.90237 19.3166 8.90237 19.7071 9.29289C20.0976 9.68342 20.0976 10.3166 19.7071 10.7071L12.7071 17.7071C12.3166 18.0976 11.6834 18.0976 11.2929 17.7071L4.29289 10.7071C3.90237 10.3166 3.90237 9.68342 4.29289 9.29289Z"
        fill={color}
      />
    </svg>
  );
};

export default ChevronDown;
