interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function Shop({ color = "#FFFFFF", ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="18"
      height="20"
      viewBox="0 0 18 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.14023 6.3503C4.81976 5.66631 4.45549 4.73719 4.31624 3.80223C4.17816 2.87513 4.25044 1.85993 4.91094 1.0978C5.57498 0.331602 6.73616 -0.0677644 8.51185 0.00943932C10.2919 0.086835 11.4428 0.585101 12.1042 1.38218C12.7648 2.17829 12.862 3.18218 12.7403 4.10105C12.6183 5.02193 12.2699 5.4119 11.961 6.06635L11.2294 6.22664C11.5219 5.60703 11.832 4.79764 11.9383 3.99483C12.0449 3.19002 11.9403 2.45153 11.4816 1.89877C11.0237 1.34698 10.1379 0.889889 8.47671 0.817662C6.81108 0.745244 5.95236 1.13139 5.52229 1.62763C5.08868 2.12794 4.99264 2.8521 5.1164 3.68306C5.23899 4.50616 5.56672 5.3538 5.87279 6.00707L5.14023 6.3503Z"
        fill={color}
      ></path>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.844722 7.20944L2.04053 18.45C3.8177 18.8351 8.68758 19.258 15.9615 18.1714L17.1521 6.74203C9.82789 5.85869 3.45822 6.64136 0.844722 7.20944ZM0.309605 6.50199C2.7987 5.90118 9.70784 4.97054 17.6469 5.98837C17.8648 6.0163 18.0206 6.21301 17.9978 6.43149L16.7338 18.5663C16.7147 18.7492 16.5747 18.8962 16.3929 18.9242C8.47301 20.1426 3.23872 19.6087 1.56562 19.1684C1.40321 19.1256 1.2841 18.987 1.26633 18.82L0.00229244 6.93798C-0.0191814 6.73613 0.11228 6.54962 0.309605 6.50199Z"
        fill={color}
      ></path>
    </svg>
  );
}
