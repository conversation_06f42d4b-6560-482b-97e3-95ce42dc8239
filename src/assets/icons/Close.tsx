interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function CloseIcon({ ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className="close-icon"
      width="40"
      height="40"
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M29.7552 28.668C29.8307 28.7384 29.8914 28.8233 29.9334 28.9177C29.9755 29.0121 29.9981 29.1139 29.9999 29.2172C30.0017 29.3205 29.9827 29.4231 29.944 29.5189C29.9053 29.6146 29.8477 29.7017 29.7747 29.7747C29.7017 29.8477 29.6146 29.9053 29.5189 29.944C29.4231 29.9827 29.3205 30.0017 29.2172 29.9999C29.1139 29.9981 29.0121 29.9755 28.9177 29.9334C28.8233 29.8914 28.7384 29.8307 28.668 29.7552L19.9808 21.0693L11.2936 29.7552C11.1478 29.8911 10.9549 29.965 10.7557 29.9615C10.5564 29.958 10.3662 29.8773 10.2253 29.7363C10.0844 29.5954 10.0036 29.4052 10.0001 29.206C9.9966 29.0067 10.0706 28.8138 10.2065 28.668L18.8924 19.9808L10.2065 11.2936C10.0706 11.1478 9.9966 10.9549 10.0001 10.7557C10.0036 10.5564 10.0844 10.3662 10.2253 10.2253C10.3662 10.0844 10.5564 10.0036 10.7557 10.0001C10.9549 9.9966 11.1478 10.0706 11.2936 10.2065L19.9808 18.8924L28.668 10.2065C28.8138 10.0706 29.0067 9.9966 29.206 10.0001C29.4052 10.0036 29.5954 10.0844 29.7363 10.2253C29.8773 10.3662 29.958 10.5564 29.9615 10.7557C29.965 10.9549 29.8911 11.1478 29.7552 11.2936L21.0693 19.9808L29.7552 28.668Z"
        fill="#93385D"
      ></path>
    </svg>
  );
}
