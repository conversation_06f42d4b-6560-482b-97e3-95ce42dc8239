interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function TabLine({ color = "#00693B", ...props }: IconProps) {
  return (
    <svg
      width="3"
      height="100%"
      viewBox="0 0 3 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      preserveAspectRatio="none"
      {...props}
    >
      <path
        d="M1.3335 1.61084L1.33349 46.4108"
        stroke={color}
        strokeWidth="2.5"
        strokeLinecap="round"
      />
    </svg>
  );
}
