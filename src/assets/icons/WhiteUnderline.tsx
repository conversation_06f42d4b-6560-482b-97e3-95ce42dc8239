type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const WhiteUnderline = ({ color = "#fff", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="132"
    height="5"
    fill="none"
    viewBox="0 0 132 5"
    {...props}
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeWidth="1.008"
      d="M1.3 1.685c4.137-.168 93.626-.62 130.08 0m-78.9 2.708c24.896-.34 71.925-.537 78.9 0"
    ></path>
  </svg>
);

export default WhiteUnderline;
