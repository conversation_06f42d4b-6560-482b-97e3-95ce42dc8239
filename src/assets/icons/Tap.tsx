type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const Tap = ({ color = "#EFD8E0", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="16"
    fill="none"
    viewBox="0 0 12 16"
    {...props}
  >
    <path
      fill={color}
      d="m4.98 15.394 5.723-5.814.653-.663a1 1 0 0 0 .15-.13c.149-.168.248-.368.285-.58.071-.351-.017-.737-.286-.989q-.042-.036-.088-.069L5.782 1.425 4.979.611C4.582.207 3.851.176 3.46.61c-.395.437-.426 1.113 0 1.544L9.182 7.97l.033.033-4.952 5.03-.802.815c-.397.403-.428 1.146 0 1.544.43.401 1.096.432 1.52 0z"
    ></path>
    <path
      fill={color}
      d="m1.682 13.546 4.453-4.36.508-.498a.7.7 0 0 0 .116-.098.9.9 0 0 0 .223-.434.78.78 0 0 0-.223-.742q-.032-.028-.069-.052L2.306 3.069l-.624-.61c-.31-.303-.879-.327-1.184 0-.307.327-.33.834 0 1.158l4.454 4.36.025.025-3.853 3.772-.624.612c-.309.302-.333.86 0 1.158.335.3.853.324 1.184 0z"
    ></path>
  </svg>
);

export default Tap;
