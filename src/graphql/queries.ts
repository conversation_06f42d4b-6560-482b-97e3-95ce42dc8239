export const getFooterQuery = /* GraphQL */ `
 query Footer {
    footer {
      logo {
        url
        mime
        alternativeText
      }
      newsletter_description
      newsletter_icon {
        alternativeText
        url
        mime
      }
      newsletter_title
      social_media {
        id
        action_link
        image {
          url
          alternativeText
          mime
        }
      }
      footer_links {
        id
        items {
          id
          action_link
          text
        }
      }
    }
  }
`;

export const getHeaderQuery = /* GraphQL */ `
  query Header {
    header {
      documentId
      logo {
        web {
          url
          mime
          alternativeText
        }
        mobile {
          url
          mime
          alternativeText
        }
      }
      menu {
        id
        title
        action_link
        image {
          id
          web {
            url
            mime
            alternativeText
          }
          mobile {
            url
            mime
            alternativeText
          }
        }
        menu_items {
          id
          title
          action_link
          image {
            web {
              url
              mime
              alternativeText
            }
            mobile {
              url
              mime
              alternativeText
            }
          }
        }
        bg_color
        tag
      }
      social_media {
        id
        action_link
        image {
          url
          alternativeText
          mime
        }
      }
      external_links {
        id
        external_link_items {
          id
          action_link
          text
        }
      }
      createdAt
      updatedAt
      publishedAt
    }
  }
`;

export const getGlobalSettingsQuery = /* GraphQL */ `
  query GlobalSetting {
    globalSetting {
      documentId
      tax_description
      createdAt
      updatedAt
      publishedAt
      certificates {
        id
        bg_image {
          id
          web {
            alternativeText
            mime
            url
          }
          mobile {
            alternativeText
            mime
            url
          }
        }
        certificate_items {
          id
          web {
            alternativeText
            mime
            url
          }
          mobile {
            alternativeText
            mime
            url
          }
          action_link
        }
      }
      announcement_bar {
        bg_color
        id
        titles {
          id
          action_link
          text
        }
      }
      cashback {
        id
        description
        tooltip
        icon {
          alternativeText
          url
          mime
        }
      }
      real_people_reviews {
        id
        review_items {
          id
          flippable_card_items {
            id
            back {
              web {
                alternativeText
                url
                mime
              }
            }
            front {
              web {
                alternativeText
                url
                mime
              }
            }
          }
        }
        title
      }
    }
  }
`;
