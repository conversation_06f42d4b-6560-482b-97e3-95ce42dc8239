import React, { useState, useMemo } from "react";
import { AddToCartButtonProps } from "../types";
import { useProductContext } from "../context/ProductContext";

/**
 * AddToCartButton Component
 *
 * Displays the add to cart button with loading state support.
 * Uses context for active variant and quantity data.
 *
 * @param onAddToCart - Optional external callback function when button is clicked
 */
const AddToCartButton: React.FC<AddToCartButtonProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  onAddToCart,
}) => {
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const {
    activeVariant,
    quantity,
    currentPrice,
    medusaProduct: contextMedusaProduct,
  } = useProductContext();
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  // Calculate button state based on active variant
  const buttonState = useMemo(() => {
    if (isAddingToCart) {
      return {
        text: "Adding...",
        disabled: true,
      };
    }

    if (!activeVariant) {
      return {
        text: "Select Variant",
        disabled: true,
      };
    }

    return {
      text: "Add to Cart",
      disabled: false,
    };
  }, [activeVariant, isAddingToCart]);

  const handleAddToCart = async () => {
    if (buttonState.disabled || !activeVariant) return;

    setIsAddingToCart(true);

    try {
      // Call external callback if provided
      if (onAddToCart && contextMedusaProduct) {
        onAddToCart({
          product: contextMedusaProduct,
          variant: activeVariant,
          quantity: quantity,
          totalPrice: currentPrice * quantity,
        });
      }
    } catch (error) {
      console.error("Error adding to cart:", error);
    } finally {
      setIsAddingToCart(false);
    }
  };
  return (
    <div className="mb-4">
      <button
        className="h-12.5 uppercase w-full p-2 rounded-[6px] text-white text-center font-semibold text-sm font-obviously border"
        style={{
          backgroundColor: primaryColor,
          borderColor: primaryColor,
        }}
        onClick={handleAddToCart}
        disabled={buttonState.disabled}
      >
        {buttonState.text}
      </button>
    </div>
  );
};

export default AddToCartButton;
