// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { BannersAspectRatio } from '../../../api/banners-aspect-ratio/content-types/banners-aspect-ratio/banners-aspect-ratio';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { BannersAspectRatio_Plain } from '../../../api/banners-aspect-ratio/content-types/banners-aspect-ratio/banners-aspect-ratio';

export interface SingleBanner {
  web?: { data: Media };
  mobile?: { data: Media };
  action_link?: string;
  web_ratio?: { data: BannersAspectRatio };
  mobile_ratio?: { data: BannersAspectRatio };
}
export interface SingleBanner_Plain {
  web?: Media_Plain;
  mobile?: Media_Plain;
  action_link?: string;
  web_ratio?: BannersAspectRatio_Plain;
  mobile_ratio?: BannersAspectRatio_Plain;
}

export interface SingleBanner_NoRelations {
  web?: number;
  mobile?: number;
  action_link?: string;
  web_ratio?: number;
  mobile_ratio?: number;
}

