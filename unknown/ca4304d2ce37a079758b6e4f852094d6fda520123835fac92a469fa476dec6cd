// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { BigFoodDidItem } from './BigFoodDidItem';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { BigFoodDidItem_Plain } from './BigFoodDidItem';
import { Icon_NoRelations } from '../../media/interfaces/Icon';
import { BigFoodDidItem_NoRelations } from './BigFoodDidItem';

export interface PyaarSecretIngredient {
  button_image?: Icon;
  secret_ingredient_item?: BigFoodDidItem;
}
export interface PyaarSecretIngredient_Plain {
  button_image?: Icon_Plain;
  secret_ingredient_item?: BigFoodDidItem_Plain;
}

export interface PyaarSecretIngredient_NoRelations {
  button_image?: Icon_NoRelations;
  secret_ingredient_item?: BigFoodDidItem_NoRelations;
}

