// Interface automatically generated by schemas-to-ts

import { FamCommentItems } from './FamCommentItems';
import { FamCommentItems_Plain } from './FamCommentItems';
import { FamCommentItems_NoRelations } from './FamCommentItems';

export interface StartupFam {
  bg_color?: any;
  title?: any;
  fam_comment_items: FamCommentItems[];
}
export interface StartupFam_Plain {
  bg_color?: any;
  title?: any;
  fam_comment_items: FamCommentItems_Plain[];
}

export interface StartupFam_NoRelations {
  bg_color?: any;
  title?: any;
  fam_comment_items: FamCommentItems_NoRelations[];
}

