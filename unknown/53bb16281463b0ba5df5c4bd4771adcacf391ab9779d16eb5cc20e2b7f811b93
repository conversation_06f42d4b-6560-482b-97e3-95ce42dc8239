interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function LoveWall({ color = "#93385D", ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className="mb-1"
      width="24"
      height="23"
      viewBox="0 0 24 23"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.1066 1.20185L10.0826 1.21787L9.99354 1.2776C9.91551 1.33 9.80119 1.40699 9.65645 1.50504C9.36693 1.70116 8.95579 1.98148 8.46984 2.3179C7.49733 2.99116 6.22782 3.88732 5.03462 4.78221C3.87456 5.65226 3.00284 6.59632 2.42144 7.32308C2.26359 7.5204 2.12745 7.7013 2.01306 7.85984C3.63078 8.10169 5.95071 9.28208 7.00477 12.6551C7.72394 14.9564 9.39433 17.2928 11.1271 19.0624C11.9903 19.9441 12.856 20.6715 13.6062 21.1746C13.9815 21.4263 14.3202 21.6167 14.6092 21.7426C14.7187 21.7903 14.8166 21.8267 14.903 21.8537L15.3851 20.1664C15.4501 19.9388 15.6713 19.7926 15.9062 19.822L17.7091 20.0473L17.469 17.646C17.4543 17.4992 17.508 17.3539 17.6145 17.2518C17.7209 17.1497 17.8685 17.1022 18.0145 17.1231L19.5666 17.3448V15.4983C19.5666 15.2332 19.7815 15.0183 20.0466 15.0183H21.2932L21.0714 13.4662C21.0346 13.208 21.2105 12.9677 21.4677 12.9248L22.3293 12.7812C21.3375 11.8099 20.063 10.5194 18.7892 9.15069L15.879 11.9445C15.8187 12.0024 15.7442 12.0436 15.663 12.0639L12.063 12.9639C11.8908 13.007 11.7089 12.9515 11.5901 12.8196C11.4712 12.6877 11.4349 12.5011 11.4955 12.3342L12.6955 9.03422C12.7217 8.96222 12.7647 8.89752 12.821 8.84555L15.9642 5.9442C15.1289 4.92572 14.4071 3.95458 13.932 3.14013C13.1384 1.77958 12.304 1.22913 11.6376 1.04309C10.9651 0.855394 10.4014 1.01993 10.1066 1.20185ZM1.14662 8.29825C0.729861 8.0601 0.729989 8.05988 0.729989 8.05988L0.730541 8.05892L0.731665 8.05696L0.735246 8.05078L0.74774 8.02952C0.758439 8.01147 0.773883 7.98574 0.794071 7.95293C0.834444 7.88732 0.893827 7.79335 0.972233 7.67574C1.12899 7.4406 1.36213 7.11047 1.6718 6.72338C2.2904 5.95013 3.21868 4.94417 4.45862 4.01421C5.66542 3.10911 6.94591 2.2053 7.9234 1.52859C8.41245 1.19002 8.82631 0.907851 9.11804 0.710231C9.26392 0.611413 9.37929 0.533715 9.45829 0.480655L9.5488 0.419976L9.5798 0.399263C9.5798 0.399263 9.58037 0.398881 9.84662 0.798268L9.5798 0.399263C9.58371 0.396655 9.58823 0.393722 9.59222 0.391229C10.099 0.0745119 10.9423 -0.147669 11.8957 0.118443C12.8592 0.387407 13.8749 1.13696 14.7612 2.65641C15.2494 3.4932 16.0438 4.54631 16.9922 5.68041C17.1565 5.87677 17.1377 6.16743 16.9496 6.34105L13.5579 9.47188L12.7064 11.8135L15.3063 11.1635L18.477 8.11969C18.5701 8.03034 18.6952 7.98222 18.8241 7.98618C18.9531 7.99015 19.075 8.04586 19.1625 8.14076C20.8468 9.96943 22.5774 11.6983 23.6784 12.7514C23.8097 12.877 23.8589 13.066 23.8053 13.2397C23.7518 13.4133 23.6048 13.5419 23.4255 13.5717L22.0881 13.7946L22.3218 15.4304C22.3415 15.5681 22.3004 15.7076 22.2092 15.8128C22.1181 15.9179 21.9858 15.9783 21.8466 15.9783H20.5266V17.8983C20.5266 18.0374 20.4662 18.1697 20.3611 18.2609C20.256 18.3521 20.1165 18.3931 19.9787 18.3734L18.4852 18.1601L18.7242 20.5505C18.7388 20.6958 18.6863 20.8399 18.5818 20.9418C18.4773 21.0438 18.332 21.0927 18.1871 21.0746L16.1952 20.8256L15.7082 22.5301C15.6493 22.7362 15.4609 22.8783 15.2466 22.8783C14.9266 22.8783 14.5752 22.7749 14.2258 22.6227C13.8691 22.4673 13.479 22.2452 13.0714 21.9719C12.256 21.425 11.3404 20.6525 10.4412 19.7341C8.6489 17.9037 6.8693 15.4401 6.08847 12.9414C4.96252 9.33839 2.28598 8.69202 1.18343 8.77683C1.00625 8.79046 0.836042 8.70509 0.741015 8.55493C0.645988 8.40477 0.641825 8.21417 0.729989 8.05988L1.14662 8.29825ZM11.5123 4.2818C11.448 4.02462 11.1874 3.86825 10.9302 3.93255C9.87789 4.19562 8.91553 4.79031 8.15114 5.40389C7.38455 6.01926 6.78654 6.67832 6.46262 7.11021C6.30356 7.32229 6.34654 7.62316 6.55862 7.78221C6.7707 7.94127 7.07156 7.89829 7.23062 7.68621C7.5067 7.31811 8.04869 6.71717 8.75209 6.15253C9.45768 5.58614 10.2953 5.08081 11.163 4.86388C11.4202 4.79959 11.5766 4.53898 11.5123 4.2818ZM10.6974 3.00121C11.4689 2.80832 12.2507 3.27742 12.4436 4.04896C12.5051 4.29493 12.4993 4.54195 12.4379 4.77059C13.0054 4.8468 13.4963 5.25966 13.6436 5.84896C13.8365 6.62051 13.3674 7.40233 12.5959 7.59522C11.9128 7.76599 11.1999 8.18195 10.553 8.70117C9.91283 9.21508 9.42685 9.75791 9.19862 10.0622C8.72145 10.6984 7.81885 10.8274 7.18262 10.3502C6.69818 9.98689 6.50785 9.37693 6.65524 8.8257C6.41884 8.79423 6.18699 8.70349 5.98262 8.55021C5.34639 8.07304 5.21744 7.17045 5.69462 6.53421C6.06639 6.03852 6.72041 5.32135 7.5502 4.65526C8.3734 3.99445 9.46046 3.31044 10.6974 3.00121ZM12.7123 6.0818C12.648 5.82462 12.3874 5.66825 12.1302 5.73255C11.0779 5.99562 10.1155 6.59031 9.35114 7.20389C8.58455 7.81926 7.98654 8.47832 7.66262 8.91021C7.50356 9.12229 7.54654 9.42316 7.75862 9.58221C7.9707 9.74127 8.27156 9.69829 8.43062 9.48621C8.70669 9.11811 9.24869 8.51717 9.95209 7.95253C10.6577 7.38614 11.4953 6.88081 12.363 6.66388C12.6202 6.59959 12.7766 6.33898 12.7123 6.0818Z"
        fill={color}
      ></path>
    </svg>
  );
}
