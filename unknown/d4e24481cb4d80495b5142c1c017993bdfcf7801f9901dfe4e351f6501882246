// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { Category } from '../../../api/category/content-types/category/category';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { Category_Plain } from '../../../api/category/content-types/category/category';
import { Icon_NoRelations } from '../../media/interfaces/Icon';

export interface MultiCategoryItems {
  image?: Icon;
  category?: { data: Category };
  subtitle?: string;
  title?: string;
}
export interface MultiCategoryItems_Plain {
  image?: Icon_Plain;
  category?: Category_Plain;
  subtitle?: string;
  title?: string;
}

export interface MultiCategoryItems_NoRelations {
  image?: Icon_NoRelations;
  category?: number;
  subtitle?: string;
  title?: string;
}

