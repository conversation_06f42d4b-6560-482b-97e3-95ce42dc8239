# MindfulIndulgenceSection Component

A reusable section component that displays different content based on screen size:

- On desktop (≥1024px): Shows a heading, subheading, and a full-width image
- On mobile (<1024px): Shows a FeatureHighlightSection with the same heading and subheading

## Features

- Customizable heading and subheading text
- Customizable background color
- Customizable text colors for heading and subheading
- Responsive design that shows different layouts on mobile and desktop
- Maintains consistent styling with the rest of the application

## Props

| Prop              | Type       | Default                | Description                                                    |
| ----------------- | ---------- | ---------------------- | -------------------------------------------------------------- |
| `heading`         | `string`   | Required               | The main heading text                                          |
| `subheading`      | `string`   | Required               | The subheading text                                            |
| `imageUrl`        | `string`   | Required               | The URL of the image to display on desktop                     |
| `bgColor`         | `string`   | `"#FFECC7"`            | The background color of the section                            |
| `headingColor`    | `string`   | `undefined`            | The color of the heading text (defaults to current text color) |
| `subheadingColor` | `string`   | `"#E78200"`            | The color of the subheading text                               |
| `className`       | `string`   | `undefined`            | Additional CSS classes to apply to the container               |
| `imageAlt`        | `string`   | `"Mindful indulgence"` | Alt text for the image                                         |
| `mobileImages`    | `string[]` | Default images         | Array of image URLs to display in the mobile version           |

## Usage Examples

### Basic Usage

```tsx
import MindfulIndulgenceSection from "@/components/Sections/MindfulIndulgenceSection";

<MindfulIndulgenceSection
  heading="Indulge"
  subheading="But mindfully"
  imageUrl="/images/products/dark_chocolate/full_banner/1.webp"
/>;
```

### With Custom Colors

```tsx
<MindfulIndulgenceSection
  heading="Indulge"
  subheading="But mindfully"
  imageUrl="/images/products/dark_chocolate/full_banner/1.webp"
  bgColor="#F5E1D3"
  headingColor="#333333"
  subheadingColor="#D35400"
/>
```

### With Custom Mobile Images

```tsx
<MindfulIndulgenceSection
  heading="Indulge"
  subheading="But mindfully"
  imageUrl="/images/products/dark_chocolate/full_banner/1.webp"
  mobileImages={[
    "/images/products/dark_chocolate/mobile_features/1.webp",
    "/images/products/dark_chocolate/mobile_features/2.webp",
    "/images/products/dark_chocolate/mobile_features/3.webp",
  ]}
/>
```

### With Additional Classes

```tsx
<MindfulIndulgenceSection
  heading="Indulge"
  subheading="But mindfully"
  imageUrl="/images/products/dark_chocolate/full_banner/1.webp"
  className="my-8"
/>
```

## Notes

- The component uses the `font-narrow` class for the heading and `font-gooddog` for the subheading to maintain consistent typography with the rest of the application.
- On desktop (≥1024px), the component shows a full-width image with heading and subheading.
- On mobile (<1024px), the component shows a FeatureHighlightSection with the same heading and subheading, plus a carousel of images.
- The desktop image is loaded with priority to improve performance.
- The component uses inline styles for dynamic color properties.
- Tailwind's responsive utility classes (`hidden lg:flex` and `block lg:hidden`) are used to toggle between the desktop and mobile versions.
