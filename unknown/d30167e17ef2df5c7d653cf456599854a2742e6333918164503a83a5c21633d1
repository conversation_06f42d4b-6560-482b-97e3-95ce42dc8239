// Interface automatically generated by schemas-to-ts

import { ProductVariant } from '../../../product-variant/content-types/product-variant/product-variant';
import { WhatsInside } from '../../../../components/pdp/interfaces/WhatsInside';
import { NutritionalFacts } from '../../../../components/pdp/interfaces/NutritionalFacts';
import { WhySection } from '../../../../components/pdp/interfaces/WhySection';
import { Faqs } from '../../../../components/faqs/interfaces/Faqs';
import { Category } from '../../../category/content-types/category/category';
import { KeyFeatures } from '../../../../components/pdp/interfaces/KeyFeatures';
import { ProductDetailsExtra } from '../../../../components/pdp/interfaces/ProductDetailsExtra';
import { ProductAdditionalDescription } from '../../../../components/pdp/interfaces/ProductAdditionalDescription';
import { Offer } from '../../../../components/pdp/interfaces/Offer';
import { SingleBanner } from '../../../../components/banner/interfaces/SingleBanner';
import { Page } from '../../../page/content-types/page/page';
import { ProductVariant_Plain } from '../../../product-variant/content-types/product-variant/product-variant';
import { WhatsInside_Plain } from '../../../../components/pdp/interfaces/WhatsInside';
import { NutritionalFacts_Plain } from '../../../../components/pdp/interfaces/NutritionalFacts';
import { WhySection_Plain } from '../../../../components/pdp/interfaces/WhySection';
import { Faqs_Plain } from '../../../../components/faqs/interfaces/Faqs';
import { Category_Plain } from '../../../category/content-types/category/category';
import { KeyFeatures_Plain } from '../../../../components/pdp/interfaces/KeyFeatures';
import { ProductDetailsExtra_Plain } from '../../../../components/pdp/interfaces/ProductDetailsExtra';
import { ProductAdditionalDescription_Plain } from '../../../../components/pdp/interfaces/ProductAdditionalDescription';
import { Offer_Plain } from '../../../../components/pdp/interfaces/Offer';
import { SingleBanner_Plain } from '../../../../components/banner/interfaces/SingleBanner';
import { Page_Plain } from '../../../page/content-types/page/page';
import { WhatsInside_NoRelations } from '../../../../components/pdp/interfaces/WhatsInside';
import { NutritionalFacts_NoRelations } from '../../../../components/pdp/interfaces/NutritionalFacts';
import { WhySection_NoRelations } from '../../../../components/pdp/interfaces/WhySection';
import { Faqs_NoRelations } from '../../../../components/faqs/interfaces/Faqs';
import { KeyFeatures_NoRelations } from '../../../../components/pdp/interfaces/KeyFeatures';
import { ProductDetailsExtra_NoRelations } from '../../../../components/pdp/interfaces/ProductDetailsExtra';
import { ProductAdditionalDescription_NoRelations } from '../../../../components/pdp/interfaces/ProductAdditionalDescription';
import { Offer_NoRelations } from '../../../../components/pdp/interfaces/Offer';
import { SingleBanner_NoRelations } from '../../../../components/banner/interfaces/SingleBanner';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Product {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  handle?: string;
  productType?: string;
  variants: { data: ProductVariant[] };
  primary_color: any;
  bg_color: any;
  whats_inside?: WhatsInside;
  nutritional_facts?: NutritionalFacts;
  show_certificates?: boolean;
  show_real_people_reviews?: boolean;
  why_section?: WhySection;
  faqs?: Faqs;
  quickly_added_category?: { data: Category };
  show_navigation_chips?: boolean;
  key_features?: KeyFeatures;
  show_review_navigation?: boolean;
  short_description?: string;
  product_detail_extra?: ProductDetailsExtra;
  additional_description?: ProductAdditionalDescription;
  show_delievery_option: boolean;
  offer?: Offer;
  banner_images: SingleBanner[];
  single_product_title?: string;
  cross_selling_categories?: { data: Category[] };
  template?: { data: Page };
  locale: string;
  localizations?: { data: Product[] };
}
export interface Product_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  handle?: string;
  productType?: string;
  variants: ProductVariant_Plain[];
  primary_color: any;
  bg_color: any;
  whats_inside?: WhatsInside_Plain;
  nutritional_facts?: NutritionalFacts_Plain;
  show_certificates?: boolean;
  show_real_people_reviews?: boolean;
  why_section?: WhySection_Plain;
  faqs?: Faqs_Plain;
  quickly_added_category?: Category_Plain;
  show_navigation_chips?: boolean;
  key_features?: KeyFeatures_Plain;
  show_review_navigation?: boolean;
  short_description?: string;
  product_detail_extra?: ProductDetailsExtra_Plain;
  additional_description?: ProductAdditionalDescription_Plain;
  show_delievery_option: boolean;
  offer?: Offer_Plain;
  banner_images: SingleBanner_Plain[];
  single_product_title?: string;
  cross_selling_categories?: Category_Plain[];
  template?: Page_Plain;
  locale: string;
  localizations?: Product_Plain[];
}

export interface Product_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  handle?: string;
  productType?: string;
  variants: number[];
  primary_color: any;
  bg_color: any;
  whats_inside?: WhatsInside_NoRelations;
  nutritional_facts?: NutritionalFacts_NoRelations;
  show_certificates?: boolean;
  show_real_people_reviews?: boolean;
  why_section?: WhySection_NoRelations;
  faqs?: Faqs_NoRelations;
  quickly_added_category?: number;
  show_navigation_chips?: boolean;
  key_features?: KeyFeatures_NoRelations;
  show_review_navigation?: boolean;
  short_description?: string;
  product_detail_extra?: ProductDetailsExtra_NoRelations;
  additional_description?: ProductAdditionalDescription_NoRelations;
  show_delievery_option: boolean;
  offer?: Offer_NoRelations;
  banner_images: SingleBanner_NoRelations[];
  single_product_title?: string;
  cross_selling_categories?: number[];
  template?: number;
  locale: string;
  localizations?: Product[];
}

export interface Product_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  title?: string;
  systemId: string;
  handle?: string;
  productType?: string;
  variants: AdminPanelRelationPropertyModification<ProductVariant_Plain>;
  primary_color: any;
  bg_color: any;
  whats_inside?: WhatsInside_Plain;
  nutritional_facts?: NutritionalFacts_Plain;
  show_certificates?: boolean;
  show_real_people_reviews?: boolean;
  why_section?: WhySection_Plain;
  faqs?: Faqs_Plain;
  quickly_added_category?: AdminPanelRelationPropertyModification<Category_Plain>;
  show_navigation_chips?: boolean;
  key_features?: KeyFeatures_Plain;
  show_review_navigation?: boolean;
  short_description?: string;
  product_detail_extra?: ProductDetailsExtra_Plain;
  additional_description?: ProductAdditionalDescription_Plain;
  show_delievery_option: boolean;
  offer?: Offer_Plain;
  banner_images: SingleBanner_Plain[];
  single_product_title?: string;
  cross_selling_categories?: AdminPanelRelationPropertyModification<Category_Plain>;
  template?: AdminPanelRelationPropertyModification<Page_Plain>;
  locale: string;
  localizations?: Product[];
}
