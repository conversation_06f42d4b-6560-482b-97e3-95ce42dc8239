// Interface automatically generated by schemas-to-ts

import { NutritionalFactItems } from './NutritionalFactItems';
import { NutritionalFactItems_Plain } from './NutritionalFactItems';
import { NutritionalFactItems_NoRelations } from './NutritionalFactItems';

export interface NutritionalFactDetails {
  nutritional_fact_items: NutritionalFactItems[];
}
export interface NutritionalFactDetails_Plain {
  nutritional_fact_items: NutritionalFactItems_Plain[];
}

export interface NutritionalFactDetails_NoRelations {
  nutritional_fact_items: NutritionalFactItems_NoRelations[];
}

