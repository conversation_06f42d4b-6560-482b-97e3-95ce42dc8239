type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const Location = ({ color = "#44426A", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    style={{ minHeight: "20px", minWidth: "20px" }}
    viewBox="0 0 20 20"
    {...props}
  >
    <mask
      id="mask0_162_745"
      width="20"
      height="20"
      x="0"
      y="0"
      fill="none"
      maskUnits="userSpaceOnUse"
      style={{ maskType: "alpha" }}
    >
      <path fill="#D9D9D9" d="M0 0h20v20H0z"></path>
    </mask>
    <g mask="url(#mask0_162_745)">
      <path
        fill={color}
        d="M10 10q.687 0 1.177-.49.49-.489.49-1.177 0-.687-.49-1.177A1.6 1.6 0 0 0 10 6.666q-.687 0-1.177.49-.49.491-.49 1.177 0 .688.49 1.178T10 10m0 8.333q-3.354-2.854-5.01-5.302-1.657-2.448-1.657-4.53 0-3.126 2.01-4.98Q7.355 1.667 10 1.667t4.656 1.854q2.01 1.854 2.01 4.98 0 2.082-1.655 4.53Q13.354 15.48 10 18.333"
      ></path>
    </g>
  </svg>
);

export default Location;
