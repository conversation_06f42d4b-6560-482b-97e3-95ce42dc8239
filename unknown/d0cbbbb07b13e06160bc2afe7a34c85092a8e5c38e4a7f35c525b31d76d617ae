// Interface automatically generated by schemas-to-ts

import { Video } from '../../media/interfaces/Video';
import { Video_Plain } from '../../media/interfaces/Video';
import { Video_NoRelations } from '../../media/interfaces/Video';

export interface RealFoodIsFlawed {
  description?: any;
  videos: Video[];
}
export interface RealFoodIsFlawed_Plain {
  description?: any;
  videos: Video_Plain[];
}

export interface RealFoodIsFlawed_NoRelations {
  description?: any;
  videos: Video_NoRelations[];
}

