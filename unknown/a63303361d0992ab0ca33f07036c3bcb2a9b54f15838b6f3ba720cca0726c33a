// Interface automatically generated by schemas-to-ts

import { ExternalMediaEmbed } from '../../media/interfaces/ExternalMediaEmbed';
import { ExternalMediaEmbed_Plain } from '../../media/interfaces/ExternalMediaEmbed';
import { ExternalMediaEmbed_NoRelations } from '../../media/interfaces/ExternalMediaEmbed';

export interface BigFoodDidItem {
  comment?: string;
  title?: any;
  subtitle?: string;
  youtube_link?: ExternalMediaEmbed;
}
export interface BigFoodDidItem_Plain {
  comment?: string;
  title?: any;
  subtitle?: string;
  youtube_link?: ExternalMediaEmbed_Plain;
}

export interface BigFoodDidItem_NoRelations {
  comment?: string;
  title?: any;
  subtitle?: string;
  youtube_link?: ExternalMediaEmbed_NoRelations;
}

