// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { SingleBanner } from './SingleBanner';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';
import { SingleBanner_Plain } from './SingleBanner';
import { SingleBanner_NoRelations } from './SingleBanner';

export enum ShowOnEnum {
  Web = 'WEB',
  Mobile = 'MOBILE',
  Both = 'BOTH',}

export interface BannerWithTitle {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: { data: Media };
  image?: SingleBanner;
}
export interface BannerWithTitle_Plain {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: Media_Plain;
  image?: SingleBanner_Plain;
}

export interface BannerWithTitle_NoRelations {
  title?: string;
  subtitle?: string;
  title_color?: any;
  subtitle_color?: any;
  bg_color?: any;
  show_on?: ShowOnEnum;
  bg_image?: number;
  image?: SingleBanner_NoRelations;
}

