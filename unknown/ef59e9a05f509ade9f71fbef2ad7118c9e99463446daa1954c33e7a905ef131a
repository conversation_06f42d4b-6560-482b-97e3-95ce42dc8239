interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function Love({ color = "#EFA146", ...props }: IconProps) {
  return (
    <svg
      width="28"
      height="30"
      viewBox="0 0 28 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M3.15479 16.8923L16.8817 28.5767C28.2937 11.4615 28.3921 2.07611 22.3947 1.70563C17.5968 1.40926 15.1117 6.55361 14.4689 9.16283C13.8134 8.07879 11.6417 5.80937 8.19905 5.40406C3.89573 4.89743 -2.33599 12.2185 3.15479 16.8923Z"
        fill={color}
        stroke={color}
        strokeWidth="1.58126"
        strokeLinejoin="round"
      />
    </svg>
  );
}
