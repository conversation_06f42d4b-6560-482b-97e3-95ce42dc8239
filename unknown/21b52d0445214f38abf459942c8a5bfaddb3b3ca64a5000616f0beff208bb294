"use client";

import React from "react";
import { cn } from "@/libs/utils";
import FlavorSelector from "./FlavorSelector";
import FlavorOverviewCard from "./FlavorOverviewCard";
import IngredientsHeader from "./IngredientsHeader";
import MainIngredientCard from "./MainIngredientCard";
import IngredientBreakdown from "./IngredientBreakdown";
import FlavorCTAButton from "./FlavorCTAButton";

interface FlavorShowcaseSectionProps {
  className?: string;
  flavors?: FlavorType[];
  initialFlavor?: string;
}

export type FlavorType = {
  id: string;
  name: string;
  icon?: React.ReactNode;
  protein: string;
  description: string;
  ingredients: string;
  usageDescription: string;
  mainIngredient: {
    name: string;
    percentage: number;
    description: string;
    image: string;
  };
  ingredientBreakdown: {
    type: string;
    percentage: number;
    image: string;
  }[];
  images: {
    product: string;
  };
  ctaText: string;
  ctaLink: string;
};

const defaultFlavors: FlavorType[] = [
  {
    id: "hazelnut-cocoa",
    name: "HAZELNUT COCOA",
    protein: "20g protein",
    description:
      "Dessert junkies and protein lovers, assemble! Did you ever think it was possible to bring both in one team? It's nutssss! No really, it's hazelnuts that's doing the magic.",
    ingredients: "Made with hazelnuts, dates, cashews, cocoa and whey!",
    usageDescription:
      "Your new favourite post-workout snack or late-night dessert.",
    mainIngredient: {
      name: "Whey",
      percentage: 22,
      description:
        "The right blend of Concentrate + Isolate that delivers the right protein density and digestibility.",
      image: "/images/ingredients/cashews.png",
    },
    ingredientBreakdown: [
      {
        type: "Hazelnuts",
        percentage: 40,
        image: "/images/ingredients/hazelnuts.png",
      },
      {
        type: "Dates",
        percentage: 25,
        image: "/images/ingredients/dates.png",
      },
      {
        type: "Cocoa",
        percentage: 9,
        image: "/images/ingredients/cocoa.png",
      },
      {
        type: "Cashews",
        percentage: 4,
        image: "/images/ingredients/cashews.png",
      },
    ],
    images: {
      product: "/images/products/protein_bars_pro/flavour_1.png",
    },
    ctaText: "SHOP HAZELNUT COCOA",
    ctaLink: "/products/hazelnut-cocoa",
  },
  {
    id: "double-cocoa",
    name: "DOUBLE COCOA",
    protein: "20g protein",
    description:
      "It's hard not to go loco for the classic Double Cocoa. Rich, indulgent and packed with protein.",
    ingredients: "Made with cocoa, dates, cashews and whey!",
    usageDescription:
      "Perfect for chocolate lovers looking for a protein boost.",
    mainIngredient: {
      name: "Whey",
      percentage: 22,
      description:
        "The right blend of Concentrate + Isolate that delivers the right protein density and digestibility.",
      image: "/images/ingredients/cashews.png",
    },
    ingredientBreakdown: [
      {
        type: "Cocoa",
        percentage: 45,
        image: "/images/ingredients/cocoa.png",
      },
      {
        type: "Dates",
        percentage: 25,
        image: "/images/ingredients/dates.png",
      },
      {
        type: "Cashews",
        percentage: 8,
        image: "/images/ingredients/cashews.png",
      },
    ],
    images: {
      product: "/images/products/double-cocoa.png",
    },
    ctaText: "SHOP DOUBLE COCOA",
    ctaLink: "/products/double-cocoa",
  },
  {
    id: "coffee-cocoa",
    name: "COFFEE COCOA",
    protein: "20g protein",
    description:
      "The perfect blend of coffee and cocoa for an energizing protein boost.",
    ingredients: "Made with coffee beans, cocoa, dates, and whey!",
    usageDescription: "Ideal for morning workouts or an afternoon pick-me-up.",
    mainIngredient: {
      name: "Whey",
      percentage: 22,
      description:
        "The right blend of Concentrate + Isolate that delivers the right protein density and digestibility.",
      image: "/images/ingredients/whey.png",
    },
    ingredientBreakdown: [
      {
        type: "Coffee",
        percentage: 35,
        image: "/images/ingredients/coffee.png",
      },
      {
        type: "Cocoa",
        percentage: 20,
        image: "/images/ingredients/cocoa.png",
      },
      {
        type: "Dates",
        percentage: 20,
        image: "/images/ingredients/dates.png",
      },
      {
        type: "Cashews",
        percentage: 3,
        image: "/images/ingredients/cashews.png",
      },
    ],
    images: {
      product: "/images/products/coffee-cocoa.png",
    },
    ctaText: "SHOP COFFEE COCOA",
    ctaLink: "/products/coffee-cocoa",
  },
  {
    id: "peanut-cocoa",
    name: "PEANUT COCOA",
    protein: "20g protein",
    description:
      "A delicious combination of peanuts and cocoa for protein lovers.",
    ingredients: "Made with peanuts, cocoa, dates, and whey!",
    usageDescription:
      "Great for satisfying cravings while getting your protein fix.",
    mainIngredient: {
      name: "Whey",
      percentage: 22,
      description:
        "The right blend of Concentrate + Isolate that delivers the right protein density and digestibility.",
      image: "/images/ingredients/whey.png",
    },
    ingredientBreakdown: [
      {
        type: "Peanuts",
        percentage: 38,
        image: "/images/ingredients/peanuts.png",
      },
      {
        type: "Dates",
        percentage: 25,
        image: "/images/ingredients/dates.png",
      },
      {
        type: "Cocoa",
        percentage: 12,
        image: "/images/ingredients/cocoa.png",
      },
      {
        type: "Cashews",
        percentage: 3,
        image: "/images/ingredients/cashews.png",
      },
    ],
    images: {
      product: "/images/products/peanut-cocoa.png",
    },
    ctaText: "SHOP PEANUT COCOA",
    ctaLink: "/products/peanut-cocoa",
  },
];

const FlavorShowcaseSection: React.FC<FlavorShowcaseSectionProps> = ({
  className,
  flavors = defaultFlavors,
  initialFlavor = "hazelnut-cocoa",
}) => {
  const [selectedFlavorId, setSelectedFlavorId] = React.useState(initialFlavor);

  const selectedFlavor = React.useMemo(() => {
    return (
      flavors.find((flavor) => flavor.id === selectedFlavorId) || flavors[0]
    );
  }, [flavors, selectedFlavorId]);

  return (
    <div
      className={cn(
        "w-full bg-[#c47c5a] text-white py-6 px-4 rounded-lg max-w-[1100px] mx-auto flex flex-col justify-center items-center",
        className
      )}
    >
      <IngredientsHeader
        heading="Four Indulgent Flavours"
        subheading="for infinite yumm."
      />

      <div className="max-w-[800px] mx-auto w-full overflow-hidden">
        <FlavorSelector
          flavors={flavors}
          selectedFlavorId={selectedFlavorId}
          onSelectFlavor={setSelectedFlavorId}
          className="w-full"
        />
      </div>

      <div className="flex justify-center w-full">
        <FlavorOverviewCard flavor={selectedFlavor} />
      </div>

      <IngredientsHeader
        heading={`Just ${
          selectedFlavor.ingredientBreakdown.length + 1
        } ingredients`}
        subheading="all add upto 100%"
      />

      <MainIngredientCard ingredient={selectedFlavor.mainIngredient} />

      <IngredientBreakdown
        ingredients={selectedFlavor.ingredientBreakdown}
        // Using default values for FlavorShowcase context
      />

      <FlavorCTAButton
        text={selectedFlavor.ctaText}
        link={selectedFlavor.ctaLink}
      />
    </div>
  );
};

export default FlavorShowcaseSection;
