import * as React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function UserIcon({ color = "#1A181E", ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="40"
      height="40"
      fill="none"
      viewBox="0 0 40 40"
    >
      <path
        stroke={color}
        strokeLinecap="round"
        strokeWidth="1.538"
        d="M24.606 15.394a4.921 4.921 0 1 1-9.842 0 4.921 4.921 0 0 1 9.842 0Z"
      ></path>
      <path
        stroke={color}
        strokeLinecap="round"
        strokeWidth="1.538"
        d="M29.527 30.158v0c0-5.436-4.407-9.843-9.843-9.843v0c-5.436 0-9.842 4.407-9.842 9.843v0"
      ></path>
    </svg>
  );
}

export default UserIcon;
