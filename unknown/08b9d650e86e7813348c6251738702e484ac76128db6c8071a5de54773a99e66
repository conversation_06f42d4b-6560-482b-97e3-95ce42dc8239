// Interface automatically generated by schemas-to-ts

import { FlippableCardItems } from '../../common/interfaces/FlippableCardItems';
import { FlippableCardItems_Plain } from '../../common/interfaces/FlippableCardItems';
import { FlippableCardItems_NoRelations } from '../../common/interfaces/FlippableCardItems';

export interface RealPeopleLove {
  title?: string;
  subtitle?: string;
  likes_count?: string;
  real_people_comments: FlippableCardItems[];
}
export interface RealPeopleLove_Plain {
  title?: string;
  subtitle?: string;
  likes_count?: string;
  real_people_comments: FlippableCardItems_Plain[];
}

export interface RealPeopleLove_NoRelations {
  title?: string;
  subtitle?: string;
  likes_count?: string;
  real_people_comments: FlippableCardItems_NoRelations[];
}

