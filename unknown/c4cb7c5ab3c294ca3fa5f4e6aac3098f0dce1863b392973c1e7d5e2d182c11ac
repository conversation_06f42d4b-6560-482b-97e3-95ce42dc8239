"use client";

import { usePathname } from "next/navigation";
import FooterNav from "@/components/Common/FooterNav";

/**
 * ConditionalFooterNav Component
 * 
 * Conditionally renders the FooterNav component based on the current route.
 * Hides FooterNav on product pages (/products/[handle]) to prevent conflicts
 * with the StickyAddToCart component.
 * 
 * Features:
 * - Route detection using Next.js usePathname hook
 * - Pattern matching for product page routes
 * - Maintains FooterNav functionality on all other pages
 */
const ConditionalFooterNav = () => {
  const pathname = usePathname();
  
  // Check if current route is a product page
  // Pattern: /products/[handle] where handle can be any product slug
  const isProductPage = pathname?.startsWith('/products/') && pathname !== '/products';
  
  // Don't render FooterNav on product pages to avoid conflicts with StickyAddToCart
  if (isProductPage) {
    return null;
  }
  
  // Render FooterNav on all other pages
  return <FooterNav />;
};

export default ConditionalFooterNav;
