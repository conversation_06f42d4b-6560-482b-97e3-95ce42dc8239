type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

export function LoyaltyCashback({ color = "#F9DEA5", ...props }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 16 16"
      {...props}
    >
      <path
        fill={color}
        d="M15.985 8.203C15.982 3.773 12.395.18 7.99.194 3.576.208.005 3.772 0 8.168a7.99 7.99 0 0 0 8.016 8.026c4.392 0 7.973-3.591 7.97-7.991"
      ></path>
      <path
        fill={color}
        d="M16 8.203c.004 4.399-3.58 7.99-7.977 7.991A7.996 7.996 0 0 1 0 8.168C.005 3.772 3.579.208 7.997.194c4.41-.013 8 3.578 8.003 8.009M.602 8.186C.6 12.275 3.89 15.59 7.96 15.6c4.106.01 7.432-3.287 7.439-7.377.006-4.11-3.292-7.432-7.38-7.434A7.386 7.386 0 0 0 .601 8.185"
      ></path>
      <path
        fill={color}
        d="M.602 8.186A7.38 7.38 0 0 1 8.01.79c4.085.002 7.38 3.324 7.374 7.434-.007 4.089-3.33 7.387-7.433 7.377C3.887 15.592.6 12.276.602 8.187zm9.75 4.279c.52.02.887-.397.802-.934a47 47 0 0 0-.325-1.842c-.052-.262-.018-.454.196-.647.472-.427.918-.883 1.368-1.333.243-.241.322-.534.2-.858-.117-.308-.358-.457-.683-.502-.63-.088-1.258-.2-1.89-.277-.235-.029-.36-.132-.455-.335-.266-.564-.55-1.12-.822-1.68-.155-.32-.39-.53-.754-.527-.362.003-.596.215-.75.535-.27.561-.556 1.116-.823 1.679a.52.52 0 0 1-.458.328c-.606.072-1.205.19-1.81.264-.373.046-.648.196-.77.56-.123.372.039.655.302.907.43.412.845.843 1.285 1.244.196.179.247.354.196.608-.123.612-.214 1.229-.329 1.841-.06.326.027.598.278.8.267.218.57.233.876.076.253-.13.526-.235.75-.405.83-.628 1.643-.692 2.478-.013.165.135.377.212.571.31.18.09.352.21.568.202zm4.492-4.486c-.107-1.87-.793-3.418-2.077-4.697a1.7 1.7 0 0 0-.285-.226c-.12-.077-.239-.04-.33.057-.096.098-.115.223-.04.335.067.1.16.182.246.267a6.25 6.25 0 0 1 1.715 3.034c.111.463.165.931.173 1.407.003.19.103.318.294.32.194.001.286-.131.302-.316.006-.08 0-.162 0-.18zM1.458 7.905c-.207.027-.316.15-.312.356q.008.325.043.648a6.74 6.74 0 0 0 1.781 3.932c.15.163.313.414.569.193.264-.228.023-.404-.122-.576-.14-.165-.286-.327-.416-.5-.791-1.065-1.22-2.259-1.254-3.588-.006-.217-.023-.412-.288-.465m13.277 1.55c.02-.246-.062-.383-.243-.422-.17-.037-.287.064-.332.229q-.126.47-.24.945c-.045.184.017.332.206.381.169.044.297-.048.347-.208.1-.324.185-.651.261-.925zm-1.478 2.916c.044-.01.084-.014.121-.029.188-.075.705-.918.688-1.115a.28.28 0 0 0-.18-.247c-.133-.059-.246-.018-.32.093-.193.292-.397.58-.555.892-.108.214.023.355.246.406m-1.449 1.431c.222.004 1.029-.678 1.052-.891a.28.28 0 0 0-.126-.28c-.098-.067-.209-.084-.302-.007q-.424.346-.832.711c-.084.076-.104.19-.047.3.053.102.131.171.256.167M1.921 5.06c.003.15-.018.29.114.35.147.067.298.037.391-.104q.278-.42.535-.852c.078-.13.051-.262-.07-.361-.12-.1-.253-.107-.361.009-.267.287-.456.626-.609.958m1.473-1.245c.042-.01.091-.005.118-.027.3-.25.6-.497.887-.76.098-.091.077-.223-.005-.333-.074-.099-.186-.14-.287-.093-.385.18-.707.452-.942.797-.132.194.012.351.229.416M1.268 6.894c-.027.282.041.415.206.45.185.04.31-.06.357-.242.078-.3.147-.6.227-.9.049-.184 0-.33-.18-.39-.19-.063-.316.048-.37.22-.099.32-.18.646-.24.862"
      ></path>
      <path
        fill="#FFF2C2"
        d="M10.353 12.464c-.216.008-.387-.111-.568-.202-.194-.097-.405-.174-.57-.309-.836-.679-1.648-.615-2.479.013-.224.17-.498.275-.75.405-.307.157-.61.141-.876-.075-.25-.203-.339-.475-.278-.8.116-.614.207-1.23.329-1.842.051-.254 0-.43-.196-.608-.44-.402-.855-.832-1.285-1.244-.262-.252-.425-.535-.302-.907.122-.364.397-.514.77-.56.605-.074 1.205-.192 1.81-.264.233-.028.362-.123.458-.328.266-.564.553-1.118.823-1.68.154-.319.388-.53.75-.534.364-.003.6.207.754.527.271.56.556 1.115.822 1.68.096.203.22.306.454.334.633.077 1.26.19 1.891.278.324.045.565.193.682.502.123.324.044.617-.199.858-.45.45-.896.906-1.368 1.333-.213.193-.248.385-.196.647q.181.918.325 1.842c.085.537-.281.952-.801.934m-4.932-.87c-.018.272.133.33.377.205.495-.252 1-.49 1.471-.782.495-.305.94-.318 1.439-.006.447.28.93.5 1.4.742.115.059.235.167.375.07.141-.099.08-.244.057-.373-.1-.588-.182-1.18-.309-1.762-.091-.42.021-.732.33-1.017.448-.414.874-.85 1.31-1.279.085-.084.197-.161.154-.306-.043-.15-.183-.148-.299-.165-.59-.089-1.179-.186-1.771-.25-.434-.046-.728-.24-.911-.639-.244-.529-.508-1.049-.767-1.57-.065-.13-.108-.313-.28-.32-.177-.006-.218.178-.283.309-.264.534-.533 1.066-.786 1.606-.18.382-.466.566-.884.61-.566.061-1.128.154-1.69.24-.141.022-.325-.005-.386.167-.07.199.117.286.225.394q.632.633 1.278 1.252c.269.256.366.544.291.916-.128.638-.225 1.281-.341 1.958M14.844 7.979c0 .018.005.1-.001.18-.016.185-.109.318-.302.316-.192-.002-.292-.13-.294-.32a6.5 6.5 0 0 0-.173-1.407 6.25 6.25 0 0 0-1.715-3.034c-.086-.085-.179-.168-.246-.267-.076-.112-.056-.237.04-.335.092-.096.21-.134.33-.057.101.065.199.14.285.226 1.284 1.28 1.97 2.826 2.076 4.697M1.458 7.905c.265.053.282.248.288.465.035 1.33.463 2.523 1.254 3.588.13.173.276.335.416.5.145.172.387.348.122.576-.256.221-.42-.03-.569-.193A6.74 6.74 0 0 1 1.19 8.91a8 8 0 0 1-.044-.65c-.004-.205.105-.327.312-.355zM14.735 9.455c-.077.273-.161.601-.262.925-.05.16-.178.252-.346.208-.189-.049-.251-.197-.207-.381q.114-.475.241-.946c.045-.164.162-.264.332-.229.18.04.262.177.243.423M13.257 12.372c-.223-.051-.354-.193-.246-.406.158-.312.362-.6.555-.893.073-.11.187-.151.32-.093a.28.28 0 0 1 .18.248c.017.196-.5 1.04-.688 1.115-.037.015-.078.019-.12.028M11.808 13.802c-.124.004-.203-.065-.256-.167-.057-.11-.037-.224.047-.3q.408-.365.832-.71c.095-.078.204-.06.302.005.1.066.139.165.126.28-.023.214-.83.896-1.052.892zM1.92 5.06c.154-.33.341-.67.61-.958.107-.116.24-.109.361-.01.121.1.147.232.07.362q-.258.433-.535.853c-.094.14-.245.169-.391.103-.132-.06-.111-.2-.114-.35M3.394 3.815c-.217-.065-.361-.221-.23-.416.235-.346.559-.618.943-.797.1-.047.212-.006.287.093.082.11.103.242.005.332-.287.263-.589.51-.887.76-.027.023-.077.019-.118.028M1.268 6.894c.06-.217.141-.542.24-.862.054-.171.18-.282.369-.22.18.06.23.206.18.391-.078.298-.148.6-.226.899-.047.183-.172.283-.357.243-.165-.036-.233-.169-.206-.45"
      ></path>
      <path
        fill="#FFCF1E"
        d="M5.42 11.594c.117-.677.214-1.321.342-1.958.075-.372-.022-.66-.29-.916a77 77 0 0 1-1.279-1.252c-.108-.108-.295-.195-.225-.393.06-.174.245-.146.385-.167.563-.087 1.125-.18 1.69-.24.419-.044.706-.228.885-.611.253-.54.521-1.072.786-1.606.065-.13.106-.314.283-.308.171.006.215.188.28.319.259.521.522 1.041.767 1.57.183.398.477.592.91.64.593.063 1.182.16 1.772.25.116.017.257.015.3.164.04.145-.07.222-.156.307-.434.428-.86.864-1.309 1.278-.309.286-.422.597-.33 1.018.127.582.209 1.173.31 1.76.021.131.083.276-.058.374-.14.097-.26-.01-.375-.07-.47-.241-.953-.462-1.4-.742-.497-.312-.944-.3-1.439.006-.472.291-.976.53-1.47.783-.244.123-.396.066-.378-.205"
      ></path>
    </svg>
  );
}

export default LoyaltyCashback;
