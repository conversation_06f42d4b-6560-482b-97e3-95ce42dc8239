type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const Copy = ({ color = "#FFF", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="18"
    fill="none"
    viewBox="0 0 15 18"
    {...props}
  >
    <path
      fill={color}
      d="M14.682 13.478c-.35.652-.86 1.104-1.588 1.29l-1.007.03H5.52c-.768-.011-1.376-.345-1.865-.922l-.225-.399c-.07-.286-.15-.57-.166-.866V3.144l.018-1.01.355-.87.09-.137.79-.62c.31-.065.607-.19.93-.194l5.764.016c.348 0 .697.019 1.044-.002.644-.04 1.24.086 1.779.45l.444.488.356.869.017 1.01a43 43 0 0 0 0 2.764v.994q-.044 1.41 0 2.821v1.009c-.007.466-.025.933-.017 1.4.008.456-.008.907-.15 1.345zM4.86 2.124c-.087.053-.114.133-.108.23-.052.262-.033.526-.012.79v.87c-.026.36-.03.72.002 1.08a3.7 3.7 0 0 0-.002.8v6.717c.04.49.279.708.778.706h6.567c.19.005.382.021.573.013.442-.018.722-.312.726-.762.003-.323-.01-.646-.015-.969V3.147c.088-1.205-.136-1.441-1.283-1.357H5.52c-.297-.038-.522.065-.663.337z"
    ></path>
    <path
      fill={color}
      d="m.376 13.479-.002-.865V5.89l.018-.866c.2-.769.64-1.342 1.373-1.676.152.205.02.447.088.663v8.603a.7.7 0 0 0 .014.357c-.021.08-.022.155.042.218-.011.104-.008.206.059.294.22 1.247 1.39 2.396 2.685 2.637l.216.031c.111.083.236.07.362.053l.286.003h5.703c.178.1.451-.144.573.158-.353.648-.848 1.12-1.587 1.29l-.864.03H5.517q-.505 0-1.01-.003l-.86-.118-.02-.005c-.198-.118-.42-.173-.638-.236l-.215-.087c-.46-.301-.909-.619-1.302-1.007l-.07-.078a.22.22 0 0 0-.134-.17L.9 15.427c.003-.064-.026-.108-.078-.143l-.301-.793-.146-1.014z"
    ></path>
  </svg>
);

export default Copy;
