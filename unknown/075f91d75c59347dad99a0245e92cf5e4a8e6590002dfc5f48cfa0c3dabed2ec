"use client";
import React, { ReactNode } from "react";
import Marquee from "@/components/Common/Marquee";
import { cn } from "@/libs/utils";

interface MarqueeSectionProps {
  /**
   * Background color for the marquee section
   */
  backgroundColor?: string;
  
  /**
   * Text color for the marquee content
   */
  textColor?: string;
  
  /**
   * Content to display in the marquee
   * If not provided, children will be used
   */
  content?: string[];
  
  /**
   * Children to render inside the marquee
   */
  children?: ReactNode;
  
  /**
   * Number of copies to display in the marquee
   */
  numberOfCopies?: number;
  
  /**
   * Whether to reverse the direction of the marquee
   */
  reverse?: boolean;
  
  /**
   * Whether to apply fade effect to the edges
   */
  fade?: boolean;
  
  /**
   * Additional CSS classes for the container
   */
  className?: string;
  
  /**
   * Additional CSS classes for the inner content
   */
  innerClassName?: string;
  
  /**
   * Gap between items in the marquee
   */
  gap?: string;
}

/**
 * A reusable section component that displays a marquee with customizable styling
 */
const MarqueeSection: React.FC<MarqueeSectionProps> = ({
  backgroundColor = "#00693B",
  textColor = "#FFFFFF",
  content,
  children,
  numberOfCopies = 3,
  reverse = false,
  fade = true,
  className,
  innerClassName,
  gap = "gap-8",
}) => {
  // Determine what content to render
  const renderContent = () => {
    if (children) {
      return children;
    }
    
    if (content && content.length > 0) {
      return (
        <div className={cn("flex items-center", gap, "px-4")}>
          {content.map((item, index) => (
            <span key={index} className="text-lg font-bold">{item}</span>
          ))}
        </div>
      );
    }
    
    // Default content if nothing is provided
    return (
      <div className={cn("flex items-center", gap, "px-4")}>
        <span className="text-lg font-bold">BACK IN STOCK</span>
        <span className="text-lg font-bold">BACK IN STOCK</span>
        <span className="text-lg font-bold">BACK IN STOCK</span>
        <span className="text-lg font-bold">BACK IN STOCK</span>
      </div>
    );
  };

  return (
    <div 
      className={cn("", className)} 
      style={{ 
        backgroundColor, 
        color: textColor 
      }}
    >
      <Marquee 
        fade={fade} 
        className="py-2" 
        numberOfCopies={numberOfCopies} 
        reverse={reverse}
        innerClassName={innerClassName}
      >
        {renderContent()}
      </Marquee>
    </div>
  );
};

export default MarqueeSection;
