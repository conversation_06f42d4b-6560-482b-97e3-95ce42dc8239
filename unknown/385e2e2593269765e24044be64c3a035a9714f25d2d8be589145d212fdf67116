import { ProductVariantType } from "./ProductVariant";
import { WhatsInsideType } from "../PDP/WhatInside";
import { NutritionalFactsType } from "../PDP/NutritionalFacts";
import { TemplateType } from "./Template";
import { WhySectionType } from "../PDP/WhySection";
import { FaqsType } from "../Components/Faqs/Faqs";
import { CategoryType } from "./Category";
import { KeyFeaturesType } from "../PDP/KeyFeatures";
import { ProductDetailsExtraType } from "../PDP/ProductDetailsExtra";
import { ProductAdditionalDescriptionType } from "../PDP/ProductAdditionalDescription";
import { OfferType } from "../PDP/Offer";
import { SingleBannerType } from "../Components/Banner/SingleBanner";

export interface ProductDetailsType {
  documentId: string;
  createdAt: Date;
  updatedAt: Date;
  publishedAt?: Date;
  title?: string;
  systemId: string;
  handle?: string;
  productType?: string;
  variants: ProductVariantType[];
  primary_color: any;
  bg_color: any;
  whats_inside?: WhatsInsideType;
  nutritional_facts?: NutritionalFactsType;
  template?: TemplateType;
  show_certificates?: boolean;
  show_real_people_reviews?: boolean;
  why_section?: WhySectionType;
  faqs?: FaqsType;
  quickly_added_category?: CategoryType;
  show_navigation_chips?: boolean;
  key_features?: KeyFeaturesType;
  show_review_navigation?: boolean;
  short_description?: string;
  product_detail_extra?: ProductDetailsExtraType;
  additional_description?: ProductAdditionalDescriptionType;
  show_delievery_option: boolean;
  offer?: OfferType;
  banner_images: SingleBannerType[];
  locale: string;
  localizations?: { data: ProductDetailsType[] };
}
