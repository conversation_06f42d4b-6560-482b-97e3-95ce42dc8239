// Interface automatically generated by schemas-to-ts

import { Button } from '../../elements/interfaces/Button';
import { ImageOrVideo } from '../../media/interfaces/ImageOrVideo';
import { Button_Plain } from '../../elements/interfaces/Button';
import { ImageOrVideo_Plain } from '../../media/interfaces/ImageOrVideo';
import { Button_NoRelations } from '../../elements/interfaces/Button';
import { ImageOrVideo_NoRelations } from '../../media/interfaces/ImageOrVideo';

export interface HeadToInfiniteLove {
  infinite_love_button_text?: Button;
  image?: ImageOrVideo;
}
export interface HeadToInfiniteLove_Plain {
  infinite_love_button_text?: Button_Plain;
  image?: ImageOrVideo_Plain;
}

export interface HeadToInfiniteLove_NoRelations {
  infinite_love_button_text?: Button_NoRelations;
  image?: ImageOrVideo_NoRelations;
}

