type IconProps = React.SVGProps<SVGSVGElement>;

export function HazlenutCoca(props: IconProps) {
  return (
    <svg
      width="20"
      height="17"
      viewBox="0 0 20 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clip-path="url(#clip0_8_3032)">
        <path
          d="M0.862306 9.71033C0.861551 7.95502 1.19237 6.56904 2.10855 5.37567C2.73847 4.55391 3.54437 3.9255 4.37444 3.30616C5.18563 2.70041 6.06857 2.21551 6.88203 1.6226C7.14865 1.42849 7.35333 1.14148 7.56557 0.880142C7.84428 0.536482 8.12223 0.508536 8.3035 0.902801C8.52858 1.39299 8.96967 1.64073 9.35789 1.89451C10.2212 2.45796 11.0943 3.00631 11.9304 3.6113C12.0445 3.69363 12.1381 3.71402 12.2726 3.67625C13.1011 3.44664 13.9441 3.2835 14.7862 3.1128C14.9728 3.07504 15.1321 2.89679 15.3021 2.78047C15.5468 2.61355 15.784 2.677 15.8565 2.96325C16.0287 3.64227 16.4584 4.1672 16.8542 4.7163C17.6669 5.84471 18.5129 6.96255 18.6941 8.4014C18.8799 9.87725 18.42 11.1922 17.6307 12.4166C17.2137 13.0638 16.7198 13.6386 16.1631 14.1711C15.4894 14.8154 14.7311 15.0782 13.83 14.7988C13.6042 14.7285 13.4222 14.8033 13.3534 14.9521C13.0732 15.5563 12.5347 15.7482 11.9629 15.9098C11.0989 16.1538 10.2099 16.2346 9.32088 16.2565C7.6056 16.2973 5.88881 16.3373 4.18939 15.9906C3.29436 15.8086 2.63952 15.3698 2.21278 14.5457C1.38271 12.94 0.891007 11.2519 0.863817 9.70957L0.862306 9.71033ZM12.6404 4.20496C13.2749 4.87416 13.8519 5.47311 14.2175 6.23294C15.185 8.24505 15.0846 10.2511 14.5 12.3267C14.3285 12.9347 14.0906 13.6122 13.8391 14.2247C14.3398 14.2565 14.8451 14.4793 15.2923 14.1318C16.4554 13.2277 17.281 12.017 17.784 10.6386C18.2243 9.43238 18.1737 8.24732 17.5899 7.07434C16.963 5.8145 15.9879 4.79032 15.3066 3.55918C14.4924 3.92701 13.5657 3.99575 12.6404 4.20496ZM3.70071 15.2459C5.07913 15.7739 6.57085 15.6915 7.98325 15.7044C9.37375 15.7172 10.7846 15.7044 12.1714 15.2338C9.60185 13.5057 5.44319 13.9098 3.70071 15.2459ZM2.77774 9.33872C3.0995 9.23147 3.16898 8.98373 3.19542 8.6937C3.28001 7.77752 3.63576 6.98068 4.28154 6.32357C4.48849 6.11284 4.72188 5.92855 4.93185 5.71933C5.06554 5.5864 5.06856 5.42024 4.94016 5.28126C4.82686 5.1589 4.69846 5.16797 4.53381 5.23217C4.13123 5.38776 3.88878 5.74804 3.64029 6.03203C3.00886 6.75485 2.65614 7.64534 2.53227 8.61137C2.49828 8.87723 2.5081 9.13177 2.77699 9.33872H2.77774ZM7.20983 3.95949C7.21285 3.69967 6.9855 3.53199 6.77629 3.64453C6.50136 3.79257 6.24078 3.97007 5.98171 4.14605C5.82386 4.2533 5.7876 4.41191 5.89183 4.57355C5.99455 4.73216 6.15166 4.77672 6.31707 4.67702C6.57462 4.52219 6.82387 4.35376 7.07538 4.19061C7.16526 4.1317 7.21662 4.05013 7.20983 3.95949Z"
          fill="#C47C5A"
          fill-opacity="0.5"
        />
        <path
          d="M2.78087 9.41431C2.51123 9.20736 2.50141 8.95358 2.5354 8.68696C2.65927 7.72093 3.01275 6.83119 3.64493 6.10762C3.89418 5.82287 4.13663 5.46259 4.53996 5.30776C4.70537 5.24431 4.83377 5.23525 4.94707 5.35685C5.07547 5.49582 5.07245 5.66123 4.93876 5.79492C4.72879 6.00338 4.4954 6.18843 4.28769 6.39916C3.6404 7.05627 3.2839 7.85311 3.19931 8.76928C3.17287 9.05932 3.10263 9.30706 2.78087 9.41431Z"
          fill="#C47C5A"
        />
        <path
          d="M7.22159 4.03501C7.22839 4.12489 7.17703 4.20722 7.08715 4.26613C6.83488 4.43003 6.58563 4.59846 6.32732 4.75254C6.16116 4.85224 6.00405 4.80768 5.90133 4.64906C5.79635 4.48743 5.8326 4.32882 5.99121 4.22157C6.25104 4.04558 6.51161 3.86809 6.7873 3.72005C6.99727 3.60751 7.22461 3.77518 7.22159 4.03501Z"
          fill="#C47C5A"
        />
        <path
          d="M7.88296 1.53202C8.16922 1.93006 8.54762 2.17478 8.86409 2.37946C8.90034 2.40288 8.93584 2.42554 8.97134 2.44895C9.1579 2.57131 9.34521 2.69367 9.53253 2.81527C10.1942 3.24579 10.8785 3.69066 11.5212 4.15819C11.6277 4.23599 11.7365 4.29188 11.8483 4.32738L12.1806 4.67935C12.2697 4.77376 12.3589 4.86666 12.4465 4.95805C12.9593 5.4928 13.4019 5.95504 13.67 6.51547C14.4503 8.14616 14.5182 9.95888 13.8838 12.2202C13.7373 12.7421 13.5265 13.258 13.303 13.8033L13.2388 13.9597L13.0915 14.32C12.9351 14.4197 12.8083 14.5609 12.7282 14.7354C12.7176 14.758 12.707 14.7792 12.695 14.7996L12.5107 14.675C11.2886 13.8494 9.65715 13.3947 7.9177 13.3947C6.17825 13.3947 4.39348 13.8804 3.33758 14.6938L3.16386 14.8275C3.01884 14.6802 2.89573 14.505 2.78999 14.2996C1.98106 12.7278 1.53997 11.1507 1.51353 9.73833C1.51353 8.01549 1.85568 6.79947 2.62231 5.79567C3.19709 5.04264 3.96145 4.44294 4.7598 3.84474C5.16464 3.54111 5.58534 3.27072 6.03097 2.98446C6.43807 2.72237 6.85953 2.45197 7.26588 2.15514C7.51664 1.9716 7.7115 1.74501 7.88296 1.53202ZM7.95622 0.593185C7.82858 0.593185 7.68885 0.686842 7.54836 0.859805C7.33612 1.12265 7.13068 1.41117 6.86331 1.60679C6.04909 2.20348 5.16389 2.6914 4.35119 3.30093C3.5196 3.92405 2.71294 4.55623 2.08152 5.38328C1.16458 6.58421 0.832254 7.97849 0.833009 9.74513C0.8602 11.2973 1.35341 12.9952 2.18499 14.6108C2.61173 15.4401 3.26809 15.8812 4.16463 16.0647C5.31721 16.3004 6.4781 16.3585 7.64051 16.3585C8.19565 16.3585 8.75079 16.3457 9.30594 16.3321C10.1964 16.3109 11.0869 16.2294 11.9525 15.9831C12.525 15.8207 13.0643 15.6274 13.3453 15.0194C13.3966 14.9083 13.5099 14.8381 13.6572 14.8381C13.7086 14.8381 13.7645 14.8464 13.8226 14.8645C14.0651 14.9401 14.2969 14.9763 14.5198 14.9763C15.127 14.9763 15.6663 14.7074 16.1603 14.2331C16.7184 13.6976 17.2124 13.119 17.6308 12.468C18.4216 11.2353 18.8824 9.9128 18.6958 8.42789C18.5145 6.97998 17.6671 5.85534 16.8529 4.72013C16.4571 4.16801 16.0266 3.64006 15.8536 2.95651C15.8075 2.77297 15.6942 2.68083 15.5553 2.68083C15.476 2.68083 15.3876 2.71104 15.2977 2.77222C15.127 2.88929 14.9676 3.0683 14.7811 3.10606C13.9374 3.27751 13.093 3.44141 12.2629 3.67253C12.2199 3.68462 12.1806 3.69066 12.1436 3.69066C12.0658 3.69066 11.9978 3.66347 11.92 3.60682C11.0832 2.99805 10.2078 2.44669 9.34295 1.87946C8.95397 1.62417 8.51212 1.37492 8.28704 0.881709C8.19641 0.683821 8.08236 0.591675 7.95547 0.591675L7.95622 0.593185ZM14.6965 14.3177C14.4306 14.3177 14.1572 14.2346 13.8679 14.2165C14.1194 13.6002 14.3664 13.0148 14.5379 12.4038C15.124 10.3146 15.2516 8.24586 14.2826 6.22166C13.9163 5.4573 13.3105 4.88554 12.6746 4.21257C13.6013 4.00184 14.488 3.92556 15.3038 3.55546C15.9865 4.79491 16.9631 5.82513 17.5908 7.09252C18.1754 8.27305 18.2268 9.46491 17.7849 10.6787C17.2804 12.0662 16.4375 13.215 15.2728 14.1243C15.0825 14.2731 14.8914 14.3177 14.6957 14.3177H14.6965ZM7.25002 15.6893C6.07402 15.6893 4.90104 15.6742 3.75375 15.2324C4.67974 14.5186 6.25756 14.0745 7.91921 14.0745C9.38902 14.0745 10.9238 14.4219 12.1323 15.2384C10.9578 15.6387 9.78706 15.6954 8.61106 15.6954C8.39732 15.6954 8.18281 15.6939 7.96831 15.6916C7.81649 15.6901 7.66392 15.6901 7.51211 15.6901C7.42525 15.6901 7.33839 15.6901 7.25153 15.6901L7.25002 15.6893Z"
          fill="#C47C5A"
          stroke="#C47C5A"
          stroke-width="0.205564"
        />
      </g>
      <defs>
        <clipPath id="clip0_8_3032">
          <rect
            width="18.5826"
            height="16.4451"
            fill="white"
            transform="translate(0.495117 0.253296)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
