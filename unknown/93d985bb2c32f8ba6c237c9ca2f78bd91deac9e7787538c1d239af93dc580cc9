import * as React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function CartCountWrapper({ color = "#1A181E", ...props }: IconProps) {
  return (
    <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="40"
    fill="none"
    viewBox="0 0 40 40"
  >
    <path
      fill={color}
      stroke={color}
      strokeLinejoin="round"
      strokeWidth="0.462"
      d="M34.463 11.277c-8.554-.042-17.198.005-25.753-.038-2.406.013-4.9.027-7.307-.049-.357.002-.621.447-.352.8.896.883 1.702 1.767 2.598 2.65l-.003-.622c-.62.625-1.24 1.338-1.86 1.963-.178.178-.177.356-.087.533a8.5 8.5 0 0 0 1.794 2.209l-.003-.621c-.531.624-.974 1.247-1.504 1.96a.43.43 0 0 0 .003.621c.449.62.986 1.149 1.435 1.768 0-.178-.09-.355-.092-.532a61 61 0 0 0-1.857 2.495c-.089.178-.087.444.092.532.895.794.992 2.302.105 3.106-.265.267-.084.799.361.796 9.177-.316 18.353-.72 27.619-1.037l7.84-.308c.535-.003.62-.714.084-.888-.892-.262-1.343-1.147-1.258-2.035.086-.533.352-.8.706-1.157.355-.357.71-.714.618-1.158-.003-.532-.45-.796-.808-1.06-.447-.353-.629-.884-.631-1.417-.008-1.331 1.145-2.314 2.032-3.117a.43.43 0 0 0-.003-.622c-.717-.794-1.523-1.411-2.239-2.029l.003.622c.71-.714 1.418-1.517 1.948-2.407.088-.178-.003-.533-.182-.62-1.16-.527-2.41-.786-3.746-.601-.534.091-.529.979.005.887 1.158-.184 2.228-.012 3.3.515-.09-.177-.091-.444-.181-.62-.53.801-1.061 1.603-1.77 2.228a.43.43 0 0 0 .003.621c.716.617 1.521 1.234 2.238 1.852l-.004-.622c-.975.893-2.128 1.965-2.298 3.385a3 3 0 0 0 .277 1.774c.269.354.448.53.627.707.268.176.715.35.45.707-.354.446-.798.715-1.063 1.249a2.84 2.84 0 0 0-.17 1.51c.184 1.063.813 1.947 1.883 2.208-.001-.267.086-.622.085-.888-9.177.316-18.353.72-27.62 1.037-2.583.103-5.256.206-7.84.309.091.265.182.531.362.796 1.152-1.16 1.14-3.29-.114-4.436.001.177.091.354.092.532a61 61 0 0 0 1.858-2.496c.088-.178.087-.355-.092-.532-.449-.618-.986-1.148-1.435-1.767l.003.621c.532-.624.974-1.248 1.505-1.96a.43.43 0 0 0-.004-.622c-.627-.617-1.165-1.236-1.615-2.032.001.177-.087.355-.086.533.62-.714 1.24-1.338 1.95-2.052a.43.43 0 0 0-.004-.621c-.896-.883-1.703-1.766-2.599-2.649-.087.267-.175.534-.352.8 8.555.043 17.199-.003 25.753.039 2.406-.013 4.901-.027 7.308.05.624.084.62-.803-.005-.8Z"
    ></path>
  </svg>
    
    );  
}

export default CartCountWrapper;
