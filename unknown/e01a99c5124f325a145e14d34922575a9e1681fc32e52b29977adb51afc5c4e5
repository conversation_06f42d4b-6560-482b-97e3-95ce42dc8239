// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface HiringProcess {
  title?: string;
  bg_color?: any;
  image?: { data: Media };
}
export interface HiringProcess_Plain {
  title?: string;
  bg_color?: any;
  image?: Media_Plain;
}

export interface HiringProcess_NoRelations {
  title?: string;
  bg_color?: any;
  image?: number;
}

