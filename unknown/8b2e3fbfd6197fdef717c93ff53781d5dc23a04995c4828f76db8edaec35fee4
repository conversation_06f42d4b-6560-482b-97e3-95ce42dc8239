import { getStrapiFooter, getStrapiHeader } from "@/libs/strapiApis";
import { FetchResult } from "@/types/Common";

export async function fetchFooterData(): Promise<FetchResult> {
  try {
    const footerData = await getStrapiFooter();
    return {
      success: true,
      data: footerData,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch footer data",
    };
  }
}

export async function fetchHeaderData(): Promise<FetchResult> {
  try {
    const headerData = await getStrapiHeader();
    return {
      success: true,
      data: headerData,
    };
  } catch (error) {
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to fetch header data",
    };
  }
}