interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
  width?: number | string;
  height?: number | string;
}

export function MobileMenuIcon({ width = 40, height = 40, ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className="menu-icon"
      width={width}
      height={height}
      viewBox="0 0 40 40"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_1_2984)" fill="none">
        <path
          d="M8.49998 16.4C15.7 15.9 23 15.5 30.3 15C30.9 15 30.9 14 30.3 14C23 14.5 15.7 14.9 8.49998 15.4C7.79998 15.4 7.79998 16.4 8.49998 16.4Z"
          fill="#93385D"
          stroke="#93385D"
          strokeWidth="0.5px"
          strokeLinejoin="round"
        ></path>
        <path
          d="M9.49998 26.025C16.9 25.925 24.4 26.125 31.8 26.625C32.4 26.625 32.4 25.625 31.8 25.625C24.4 25.125 16.9 24.925 9.49998 25.025C8.79998 25.125 8.79998 26.125 9.49998 26.025Z"
          fill="#93385D"
          stroke="#93385D"
          strokeWidth="0.5px"
          strokeLinejoin="round"
        ></path>
      </g>
      <defs>
        <clipPath id="clip0_1_2984">
          <rect
            width="24.3"
            height="23.1"
            fill="white"
            transform="translate(8 8)"
          ></rect>
        </clipPath>
      </defs>
    </svg>
  );
}
