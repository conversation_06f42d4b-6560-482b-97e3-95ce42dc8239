"use client";
import CarouselProductCard from "@/components/Common/CarouselProductCard";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import InfoCard from "@/components/Common/InfoCard";
import ProductPromoBanner from "@/components/Common/ProductPromoBanner";
import SingleBanner from "@/components/Common/SingleBanner";

import React from "react";

const page = () => {
  const bannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/heroes/peanut_butter_mobile.webp",
      mime: "image/png",
    },
    web_image: {
      alternativeText: null,
      url: "/images/heroes/peanut_butter_desktop.webp",
      mime: "image/png",
    },
  };
  return (
    <>
      <SingleBanner image={bannerImage} />
      <div className="pt-6 bg-[#EAFCFF] px-[30px] flex lg:flex-row flex-col gap-6">
        {/* Product Info */}
        <div>
          <InfoCard
            heading="Unsweetened Peanut Butter"
            subheading="Just high-quality, slow-roasted peanuts. That's all!"
            description="Premium 'Java' peanuts, we only pick the freshest lot! (pink skin = fresh)"
            imageSrc="/images/products/peanut_butter/peanuts.png"
            imageAlt="Unsweetened Peanut Butter"
            topBgColor="#ff8abb"
            bottomBgColor="white"
            linkUrl="/products/peanut-butter"
          />
        </div>
        <div className="flex gap-6">
          <UniversalCarousel>
            <CarouselProductCard
              title="CREAMY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              primaryColor="#C866A7"
              sizes={["325g", "500g", "1kg"]}
              enableProductBg={false}
              buttonAction={{ type: "add_to_cart" }}
              sameDayDelivery={true}
              deliveryTime={{ hours: 2, minutes: 30, seconds: 0 }}
            />
            <CarouselProductCard
              title="CRUNCHY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This crunchy peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#FF8ABB"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
          </UniversalCarousel>
        </div>
      </div>
      <div className="pt-6 bg-[#EAFCFF] px-[30px] flex lg:flex-row flex-col gap-6">
        {/* Product Info */}
        <div>
          <InfoCard
            heading="Peanut Spread With Dates"
            subheading="Premium Spread + Whole Dates = ♥"
            description="Sweetened with raw whole dates that we de-pit ourselves. You'll see the date chunks inside."
            imageSrc="/images/products/peanut_butter/peanuts_with_dates.png"
            imageAlt="Peanut Spread With Dates"
            topBgColor="#48b6d4"
            bottomBgColor="white"
            linkUrl="/products/peanut-butter"
          />
        </div>
        <div className="flex gap-6">
          <UniversalCarousel>
            <CarouselProductCard
              title="CRUNCHY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This crunchy peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#FF8ABB"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
          </UniversalCarousel>
        </div>
      </div>
      {/* ProductPromoBanner */}
      <ProductPromoBanner
        imageSrc="/images/promo/whey_protein.png"
        imageAlt="Protein Powder Single Serve Sachet"
        title="NEW: Protein Powder Single Serve Sachet"
        subtitle="Cleanest, lightest, 24g protein. Now at ₹999"
        actionUrl="/products/protein-powder"
        buttonText="Shop Now"
      />

      <div className="pt-6 bg-[#EAFCFF] px-[30px] flex lg:flex-row flex-col gap-6">
        {/* Product Info */}
        <div>
          <InfoCard
            heading="Peanut Spread With Dates"
            subheading="Premium Spread + Whole Dates = ♥"
            description="Sweetened with raw whole dates that we de-pit ourselves. You'll see the date chunks inside."
            imageSrc="/images/products/peanut_butter/whey_concentrate.png"
            imageAlt="Peanut Spread With Dates"
            topBgColor="#000000"
            bottomBgColor="white"
            linkUrl="/products/peanut-butter"
          />
        </div>
        <div className="flex gap-6">
          <UniversalCarousel>
            <CarouselProductCard
              title="CREAMY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#ff8abb"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
          </UniversalCarousel>
        </div>
      </div>
      <div className="pt-6 bg-[#EAFCFF] px-[30px] flex lg:flex-row flex-col gap-6">
        {/* Product Info */}
        <div>
          <InfoCard
            heading="Peanut Spread With Dates"
            subheading="Premium Spread + Whole Dates = ♥"
            description="Sweetened with raw whole dates that we de-pit ourselves. You'll see the date chunks inside."
            imageSrc="/images/products/peanut_butter/dark_chocolate.png"
            imageAlt="Peanut Spread With Dates"
            topBgColor="#8694db"
            bottomBgColor="white"
            linkUrl="/products/peanut-butter"
          />
        </div>
        <div className="flex gap-6">
          <UniversalCarousel>
            <CarouselProductCard
              title="CREAMY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#ff8abb"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
            <CarouselProductCard
              title="CRUNCHY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This crunchy peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#FF8ABB"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
          </UniversalCarousel>
        </div>
      </div>
      <div className="pt-6 bg-[#EAFCFF] px-[30px] flex lg:flex-row flex-col gap-6">
        {/* Product Info */}
        <div>
          <InfoCard
            heading="Peanut Spread With Dates"
            subheading="Premium Spread + Whole Dates = ♥"
            description="Sweetened with raw whole dates that we de-pit ourselves. You'll see the date chunks inside."
            imageSrc="/images/products/peanut_butter/hazlenut_spread.png"
            imageAlt="Peanut Spread With Dates"
            topBgColor="#5b3838"
            bottomBgColor="white"
            linkUrl="/products/peanut-butter"
          />
        </div>
        <div className="flex gap-6">
          <UniversalCarousel>
            <CarouselProductCard
              title="CREAMY- Unsweetened Peanut Butter - Pack of 325g"
              price={225}
              description="This peanut butter is made with just peanuts."
              rating={4}
              size="1 X 325g"
              images={[
                "/images/products/badaam/highlight/1.webp",
                "/images/products/badaam/highlight/2.webp",
                "/images/products/badaam/highlight/3.webp",
                "/images/products/badaam/highlight/4.webp",
                "/images/products/badaam/highlight/5.webp",
              ]}
              onAddToCart={() => console.log("Added to cart")}
              variant="outline"
              selectedSize="325g"
              enableProductBg={false}
              primaryColor="#ff8abb"
              sizes={["325g", "500g", "1kg"]}
              buttonAction={{ type: "add_to_cart" }}
            />
          </UniversalCarousel>
        </div>
      </div>

      {/* Another ProductPromoBanner example */}
      <ProductPromoBanner
        imageSrc="/images/promo/whey_protein.png"
        imageAlt="Peanut Butter Jar"
        title="Try Our New Crunchy Peanut Butter"
        subtitle="Made with 100% natural ingredients. No added sugar or preservatives."
        actionUrl="/products/crunchy-peanut-butter"
        buttonText="Explore Now"
      />
    </>
  );
};

export default page;
