// Interface automatically generated by schemas-to-ts

import { SearchKeywords } from './SearchKeywords';
import { SearchUpsellCollections } from './SearchUpsellCollections';
import { SearchKeywords_Plain } from './SearchKeywords';
import { SearchUpsellCollections_Plain } from './SearchUpsellCollections';
import { SearchKeywords_NoRelations } from './SearchKeywords';
import { SearchUpsellCollections_NoRelations } from './SearchUpsellCollections';

export interface SearchPage {
  search_keywords?: SearchKeywords;
  search_upsell_category: SearchUpsellCollections[];
}
export interface SearchPage_Plain {
  search_keywords?: SearchKeywords_Plain;
  search_upsell_category: SearchUpsellCollections_Plain[];
}

export interface SearchPage_NoRelations {
  search_keywords?: SearchKeywords_NoRelations;
  search_upsell_category: SearchUpsellCollections_NoRelations[];
}

