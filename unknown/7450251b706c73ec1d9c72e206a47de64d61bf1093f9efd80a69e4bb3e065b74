interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}
export function Categories({ color = "#93385d", ...props }: IconProps) {
  return (
    <svg
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className="mb-1"
      width="25"
      height="26"
      viewBox="0 0 25 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.8062 15.3925C18.4326 14.8168 20.2024 15.729 20.7541 17.4262"
        stroke={color}
        strokeWidth="0.537382px"
        strokeMiterlimit="10"
        strokeLinecap="round"
        fill={color}
      ></path>
      <path
        d="M9.39034 1H1.39411C1.10128 1 0.863892 1.24771 0.863892 1.55327V9.8972C0.863892 10.2028 1.10128 10.4505 1.39411 10.4505H9.39034C9.68317 10.4505 9.92056 10.2028 9.92056 9.8972V1.55327C9.92056 1.24771 9.68317 1 9.39034 1Z"
        stroke={color}
        strokeMiterlimit="10"
        fill={color}
      ></path>
      <path
        d="M9.39034 13.8374H1.39411C1.10128 13.8374 0.863892 14.0851 0.863892 14.3907V22.7346C0.863892 23.0402 1.10128 23.2879 1.39411 23.2879H9.39034C9.68317 23.2879 9.92056 23.0402 9.92056 22.7346V14.3907C9.92056 14.0851 9.68317 13.8374 9.39034 13.8374Z"
        stroke={color}
        strokeMiterlimit="10"
        fill={color}
      ></path>
      <path
        d="M21.7788 1H13.7825C13.4897 1 13.2523 1.24771 13.2523 1.55327V9.8972C13.2523 10.2028 13.4897 10.4505 13.7825 10.4505H21.7788C22.0716 10.4505 22.309 10.2028 22.309 9.8972V1.55327C22.309 1.24771 22.0716 1 21.7788 1Z"
        stroke={color}
        strokeMiterlimit="10"
        fill={color}
      ></path>
      <path
        d="M17.7807 23.2802C20.3053 23.2802 22.352 21.1445 22.352 18.5101C22.352 15.8756 20.3053 13.74 17.7807 13.74C15.256 13.74 13.2094 15.8756 13.2094 18.5101C13.2094 21.1445 15.256 23.2802 17.7807 23.2802Z"
        stroke={color}
        strokeMiterlimit="10"
        fill={color}
      ></path>
      <path
        d="M21.0765 21.8074L24.136 24.9999"
        stroke={color}
        strokeMiterlimit="10"
        strokeLinecap="round"
        fill={color}
      ></path>
    </svg>
  );
}
