"use client";
import { ReviewStar } from "@/assets/icons/ReviewStar";
import React from "react";
import { getStarFillPercentage } from "@/libs/utils";
import { useReviewStats } from "../../hooks";

interface ReviewStatsProps {
  productId?: string;
  primaryColor?: string;
}

const ReviewStats: React.FC<ReviewStatsProps> = ({
  productId = "prod_01JWB88PWETQJBSPG29JAZZAGE",
  primaryColor = "#44426a", // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const { data: stats, isLoading } = useReviewStats({
    productId,
    enabled: !!productId,
  });

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-start w-1/2 space-y-1.5 pb-4">
        <div className="items-start flex">
          {Array.from({ length: 5 }).map((_, index) => (
            <ReviewStar key={index} color="#E5E7EB" fillPercentage={0} />
          ))}
        </div>
        <p className="text-lg font-bold text-[#44426a] font-obviously">
          Loading...
        </p>
        <p className="text-[13px] leading-5 font-normal text-[#44426a] font-obviously">
          Fetching review data
        </p>
      </div>
    );
  }

  // Show error state or fallback to default values
  const averageRating = stats?.average_rating || 0;
  const reviewCount = stats?.review_count || 0;

  return (
    <div className="flex flex-col items-start w-1/2 space-y-1.5 pb-4 -mt-4">
      <div className="items-start flex ">
        {Array.from({ length: 5 }).map((_, index) => (
          <ReviewStar
            key={index}
            color="#FFC83A"
            fillPercentage={getStarFillPercentage(averageRating, index)}
          />
        ))}
      </div>
      <p className="text-lg font-bold text-black font-obviously">
        {averageRating} out of 5
      </p>
      <p className="text-[13px] leading-5 font-normal text-[#44426a] font-obviously">
        Based on {reviewCount} reviews
      </p>
    </div>
  );
};

export default ReviewStats;
