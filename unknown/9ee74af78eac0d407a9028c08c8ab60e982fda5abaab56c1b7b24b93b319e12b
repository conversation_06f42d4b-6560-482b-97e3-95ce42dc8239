import * as React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function SearchIcon({ color = "#1A181E", ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      className="dkn-header-search-icon hide-on-mobile"
      viewBox="0 0 24 24"
    >
      <g
        fill={color}
        fillRule="evenodd"
        clipPath="url(#clip0_1133_513)"
        clipRule="evenodd"
      >
        <path d="M10.875 3.75a7.125 7.125 0 1 0 0 14.25 7.125 7.125 0 0 0 0-14.25M2.25 10.875a8.625 8.625 0 1 1 17.25 0 8.625 8.625 0 0 1-17.25 0"></path>
        <path d="M15.913 15.914a.75.75 0 0 1 1.06 0l4.557 4.556a.75.75 0 1 1-1.06 1.06l-4.557-4.556a.75.75 0 0 1 0-1.06"></path>
      </g>
      <defs>
        <clipPath id="clip0_1133_513">
          <path fill="#fff" d="M0 0h24v24H0z"></path>
        </clipPath>
      </defs>
    </svg>
  );
}

export default SearchIcon;
