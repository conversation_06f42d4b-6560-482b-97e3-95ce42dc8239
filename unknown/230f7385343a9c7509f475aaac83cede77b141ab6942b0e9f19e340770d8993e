// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { MenuItems } from './MenuItems';
import { Image_Plain } from '../../media/interfaces/Image';
import { MenuItems_Plain } from './MenuItems';
import { Image_NoRelations } from '../../media/interfaces/Image';
import { MenuItems_NoRelations } from './MenuItems';

export interface HeaderMenu {
  title: string;
  action_link?: string;
  image?: Image;
  menu_items: MenuItems[];
  bg_color?: any;
  tag?: string;
}
export interface HeaderMenu_Plain {
  title: string;
  action_link?: string;
  image?: Image_Plain;
  menu_items: MenuItems_Plain[];
  bg_color?: any;
  tag?: string;
}

export interface HeaderMenu_NoRelations {
  title: string;
  action_link?: string;
  image?: Image_NoRelations;
  menu_items: MenuItems_NoRelations[];
  bg_color?: any;
  tag?: string;
}

