"use client";

import * as React from "react";
import { ChevronDownIcon, MinusIcon, PlusIcon } from "lucide-react";
import { Button, buttonVariants } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/libs/utils";
import { useRouter } from "next/navigation";

type AccentProductAddToCartProps = {
  sizes?: string[];
  selectedSize?: string;
  onSizeChange?: (size: string) => void;
  onAddToCart: () => void;
  variant?: "default" | "outline";
  primaryColor?: string;
  buttonAction?: {
    type: string;
    redirectSlug?: string;
  };
};

export function AccentProductAddToCart({
  sizes,
  selectedSize: propSelectedSize,
  onSizeChange,
  onAddToCart,
  variant = "default",
  primaryColor = "#93385D",
  buttonAction = { type: "addToCart" },
}: AccentProductAddToCartProps) {
  const router = useRouter();
  const hasSizes = sizes && sizes.length > 0;

  // Use a map to track counts and added state for each variant
  const [variantCounts, setVariantCounts] = React.useState<
    Record<string, number>
  >({});
  const [variantAdded, setVariantAdded] = React.useState<
    Record<string, boolean>
  >({});

  const [selectedSize, setSelectedSize] = React.useState(
    propSelectedSize || (sizes && sizes.length > 0 ? sizes[0] : "")
  );

  // Get current count and added state for the selected size or for non-variant products
  const variantKey = selectedSize || "default";
  const currentCount = variantCounts[variantKey] || 1;
  const isAdded = variantAdded[variantKey] || false;

  // Update local state when prop changes
  React.useEffect(() => {
    if (propSelectedSize) {
      // If the selected size is changing, just update the selected size
      if (selectedSize !== propSelectedSize) {
        setSelectedSize(propSelectedSize);
        console.log(`Selected size changed to: ${propSelectedSize}`);
      }
    }
  }, [propSelectedSize, selectedSize]);

  // Log when selected size changes
  React.useEffect(() => {
    if (selectedSize) {
      console.log(`Selected size changed to: ${selectedSize}`);
      console.log(`Current count: ${currentCount}, isAdded: ${isAdded}`);
    }
  }, [selectedSize, currentCount, isAdded]);

  const handleAddToCart = () => {
    if (buttonAction.type === "buildYourOwnBox" && buttonAction.redirectSlug) {
      // Redirect to the specified slug
      router.push(buttonAction.redirectSlug);
    } else {
      // Default "Add to Cart" behavior
      setVariantAdded((prev) => ({
        ...prev,
        [variantKey]: true,
      }));
      onAddToCart();
    }
  };

  const handleIncrement = () => {
    // Increment the count for the current variant or default
    setVariantCounts((prev) => ({
      ...prev,
      [variantKey]: (prev[variantKey] || 1) + 1,
    }));
  };

  const handleDecrement = () => {
    const currentVariantCount = variantCounts[variantKey] || 1;

    if (currentVariantCount > 1) {
      // Decrement the count for the current variant or default
      setVariantCounts((prev) => ({
        ...prev,
        [variantKey]: currentVariantCount - 1,
      }));
    } else {
      // Reset the added state for the current variant or default
      setVariantAdded((prev) => ({
        ...prev,
        [variantKey]: false,
      }));
    }
  };

  if (hasSizes) {
    return (
      <div className="flex w-full">
        <div className="w-1/2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant={variant}
                className={cn(
                  buttonVariants({ size: "default" }),
                  "rounded-r-none w-full"
                )}
                style={{
                  borderColor: "white",
                  color: "white",
                  backgroundColor: primaryColor,
                }}
              >
                <span className="truncate">{selectedSize}</span>
                <ChevronDownIcon
                  className="-me-1 opacity-60 flex-shrink-0"
                  size={16}
                  aria-hidden="true"
                />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start">
              {sizes.map((size) => (
                <DropdownMenuItem
                  key={size}
                  onSelect={() => {
                    setSelectedSize(size);
                    if (onSizeChange) {
                      onSizeChange(size);
                    }
                    if (!(size in variantCounts)) {
                      setVariantCounts((prev) => ({
                        ...prev,
                        [size]: 1,
                      }));
                    }
                    if (!(size in variantAdded)) {
                      setVariantAdded((prev) => ({
                        ...prev,
                        [size]: false,
                      }));
                    }
                  }}
                  style={{ color: primaryColor }}
                  className="hover:bg-gray-50"
                >
                  {size}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
        <div className="w-1/2">
          {!isAdded ? (
            <Button
              onClick={handleAddToCart}
              variant={variant}
              className="rounded-l-none w-full text-xs px-1 "
              style={{
                backgroundColor: "white",
                color: primaryColor,
                whiteSpace: "nowrap",
                overflow: "hidden",
                textOverflow: "ellipsis",
              }}
            >
              {buttonAction.type === "buildYourOwnBox"
                ? "BUILD"
                : "ADD TO CART"}
            </Button>
          ) : (
            <div
              className="flex items-center h-9 rounded-l-none rounded-r-[4px]"
              style={{
                backgroundColor: "white",
              }}
            >
              <Button
                variant={variant}
                size="icon"
                className="h-full rounded-none px-2 flex-1"
                style={{
                  color: primaryColor,
                  backgroundColor: "transparent",
                  border: "none",
                }}
                onClick={handleDecrement}
              >
                <MinusIcon size={16} />
              </Button>
              <div
                className="flex items-center justify-center h-full px-2 font-medium flex-1"
                style={{
                  color: primaryColor,
                }}
              >
                {currentCount}
              </div>
              <Button
                variant={variant}
                size="icon"
                className="h-full rounded-none px-2 flex-1"
                style={{
                  color: primaryColor,
                  backgroundColor: "transparent",
                  border: "none",
                }}
                onClick={handleIncrement}
              >
                <PlusIcon size={16} />
              </Button>
            </div>
          )}
        </div>
      </div>
    );
  } else {
    // Without variants: Full width button
    return (
      <div className="w-full">
        {!isAdded ? (
          <Button
            onClick={handleAddToCart}
            variant={variant}
            className="w-full text-xs px-2"
            style={{
              backgroundColor: "white",
              color: primaryColor,
              borderColor: primaryColor,
            }}
          >
            {buttonAction.type === "buildYourOwnBox"
              ? "BUILD YOUR OWN BOX"
              : "ADD TO CART"}
          </Button>
        ) : (
          <div
            className="flex items-center h-9 rounded-[5px] w-full"
            style={{
              backgroundColor: "white",
              border: `1px solid ${primaryColor}`,
            }}
          >
            <Button
              variant={variant}
              size="icon"
              className="h-full rounded-none px-3 flex-1"
              style={{
                color: primaryColor,
                backgroundColor: "transparent",
                border: "none",
              }}
              onClick={handleDecrement}
            >
              <MinusIcon size={16} />
            </Button>
            <div
              className="flex items-center justify-center h-full px-3 font-medium flex-1"
              style={{
                color: primaryColor,
              }}
            >
              {currentCount}
            </div>
            <Button
              variant={variant}
              size="icon"
              className="h-full rounded-none px-3 flex-1"
              style={{
                color: primaryColor,
                backgroundColor: "transparent",
                border: "none",
              }}
              onClick={handleIncrement}
            >
              <PlusIcon size={16} />
            </Button>
          </div>
        )}
      </div>
    );
  }
}

export default AccentProductAddToCart;
