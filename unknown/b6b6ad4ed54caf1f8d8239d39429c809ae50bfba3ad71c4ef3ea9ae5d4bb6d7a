import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const getStarFillPercentage = (rating: number, starPosition: number) => {
  const fullStars = Math.floor(rating);
  const decimal = rating - fullStars;

  if (starPosition < fullStars) return 100;
  if (starPosition === fullStars) return decimal * 100;
  return 0;
};