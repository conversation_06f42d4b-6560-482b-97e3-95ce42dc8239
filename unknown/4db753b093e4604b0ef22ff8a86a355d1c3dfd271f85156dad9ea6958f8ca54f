"use client";

import { useQuery } from "@tanstack/react-query";
import { getProductReviewStats } from "@/services/medusa/reviews";
import type { UseReviewStatsOptions, UseReviewStatsReturn } from "./types";

export const useReviewStats = ({
  productId,
  enabled = true,
}: UseReviewStatsOptions): UseReviewStatsReturn => {
  const {
    data,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey: ["product-review-stats", productId],
    queryFn: () => getProductReviewStats(productId),
    enabled: enabled && !!productId,
    staleTime: 10 * 60 * 1000, // 10 minutes - stats don't change as frequently
    gcTime: 30 * 60 * 1000, // 30 minutes (formerly cacheTime)
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  return {
    data,
    isLoading,
    isError,
    error: error as Error | null,
  };
};
