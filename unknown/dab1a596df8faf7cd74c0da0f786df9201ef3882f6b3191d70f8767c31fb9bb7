import * as React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

const MobileMenuChevron = ({ color = "#000", ...props }: IconProps) => (
  <svg
    {...props}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    fill="none"
    viewBox="0 0 20 20"
  >
    <path
      fill={color}
      d="m16.581 7.831-6.25 6.25a.47.47 0 0 1-.662 0l-6.25-6.25a.469.469 0 0 1 .662-.662L10 13.087l5.919-5.918a.469.469 0 0 1 .662.662"
    ></path>
  </svg>
);

export default MobileMenuChevron;
