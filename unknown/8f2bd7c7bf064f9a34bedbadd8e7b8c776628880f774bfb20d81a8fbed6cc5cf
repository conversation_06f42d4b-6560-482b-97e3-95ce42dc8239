// Interface automatically generated by schemas-to-ts

export interface BannersAspectRatio {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  width?: string;
  height?: string;
}
export interface BannersAspectRatio_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  width?: string;
  height?: string;
}

export interface BannersAspectRatio_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  width?: string;
  height?: string;
}

export interface BannersAspectRatio_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  width?: string;
  height?: string;
}
