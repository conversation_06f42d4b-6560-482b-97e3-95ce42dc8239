"use client";
import FeatureHighlightSection from "@/components/blocks/FeatureHighlightSection";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import { Product } from "@/components/Common/ProductCard";
import SingleBanner from "@/components/Common/SingleBanner";
import ColouredProductCarouselSection from "@/components/Sections/ColouredProductCarouselSection";
import MindfulIndulgenceSection from "@/components/Sections/MindfulIndulgenceSection";
import React from "react";

const page = () => {
  const bannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/heroes/dark_chocholate_mobile.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: null,
      url: "/images/heroes/dark_chocholate_desktop.png",
      mime: "image/png",
    },
  };

  const accentCardProducts: Product[] = [
    {
      id: "1",
      title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
      image: "/images/products/dark_chocolate/1.png",
      price: 4777,
      originalPrice: 4499, // 1% discount
      isStartingPrice: false,
      rating: 2.5,
      weight: "8 x 27 g",
      primaryColor: "#FF8300", // matches double cocoa
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "2",
      title: "Everyone Party - Pack of 16 Mini Protein Bars",
      image: "/images/products/dark_chocolate/2.png",
      price: 960,
      isStartingPrice: false,
      rating: 4.1,
      weight: "16 x 27 g",
      primaryColor: "#7841B3",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "3",
      title: "Personalised Box - Pack of 24 Mini Protein Bars",
      image: "/images/products/dark_chocolate/3.png",
      price: 1440,
      isStartingPrice: true,
      rating: 4.7,
      weight: "24 x 27 g",
      primaryColor: "#DC8A20",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "4",
      title: "Personalised Box - Pack of 48 Mini Protein Bars",
      image: "/images/products/dark_chocolate/3.png",
      price: 2880,
      isStartingPrice: true,
      rating: 4.2,
      weight: "48 x 27 g",
      primaryColor: "#009ba7",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "5",
      title: "Double Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/dark_chocolate/5.png",
      price: 720,
      isStartingPrice: false,
      rating: 4.5,
      weight: "12 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "6",
      title: "Cranberry Mini Protein Bars - Box of 12",
      image: "/images/products/dark_chocolate/6.png",
      price: 720,
      originalPrice: 800, // 10% discount
      isStartingPrice: false,
      rating: 2.4,
      weight: "12 x 27 g",
      primaryColor: "#D23C47", // red tone
      enableProductBg: true, // Enable background color
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "7",
      title: "Peanut Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/dark_chocolate/7.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#F05C1D", // orange tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "8",
      title: "Coffee Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/dark_chocolate/8.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#59382B", // brown tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
  ];
  return (
    <>

      <SingleBanner image={bannerImage} />

      <ColouredProductCarouselSection
        title="Shop The Range"
        products={accentCardProducts}
        backgroundColor="#ECE3F4"
        titleColor="#0B1698"
        hideScrollbar={true}
        enableProductBg={false}
      />

      <FeatureHighlightSection
        title="TWT Chocolate Factory"
        textColor="text-[#F6381C]"
        subtitle="Bean to Bar. All in house"
        subtitleClassName="text-[#F6381C]"
        images={[
          "/images/products/dark_chocolate/features/1.webp",
          "/images/products/dark_chocolate/features/2.webp",
          "/images/products/dark_chocolate/features/3.webp",
          "/images/products/dark_chocolate/features/4.webp",
          "/images/products/dark_chocolate/features/5.webp",
          "/images/products/dark_chocolate/features/6.webp",
        ]}
      />

      <FeatureHighlightSection
        title="No Added Sugar"
        textColor="text-[#F6381C]"
        subtitle="Or any sugar substitute"
        subtitleClassName="text-[#F6381C]"
        images={[
          "/images/products/dark_chocolate/more_features/1.webp",
          "/images/products/dark_chocolate/more_features/2.webp",
          "/images/products/dark_chocolate/more_features/3.webp",
        ]}
      />

      <MindfulIndulgenceSection
        heading="Indulge"
        subheading="But mindfully"
        imageUrl="/images/products/dark_chocolate/full_banner/1.webp"
        bgColor="#FFB7E1"
        headingColor="#ed1898"
        subheadingColor="#ed1898"
        mobileImages={[
          "/images/products/dark_chocolate/Indulge/1.webp",
          "/images/products/dark_chocolate/Indulge/2.webp",
          "/images/products/dark_chocolate/Indulge/3.webp",
          "/images/products/dark_chocolate/Indulge/4.webp",
          "/images/products/dark_chocolate/Indulge/5.webp",
        ]}
      />

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default page;
