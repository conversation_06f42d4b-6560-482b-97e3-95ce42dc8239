"use client";

import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";

interface VideoFeatureSectionProps {
  videoSrc: string;
  backgroundColor?: string;
  className?: string;
  width?: string | number;
  allowFullscreen?: boolean;
  ribbonImagePath?: string;
}

const VideoFeatureSection: React.FC<VideoFeatureSectionProps> = ({
  videoSrc,
  backgroundColor = "#313131",
  className,
  width = "100%",
  allowFullscreen = true,
  ribbonImagePath = "/ribbon.png",
}) => {
  // Extract YouTube video ID from URL if full URL is provided
  const getYouTubeId = (url: string): string => {
    // If it's already just an ID (no slashes or dots), return it
    if (!/[\/.]/.test(url)) {
      return url;
    }

    // Try to extract ID from various YouTube URL formats
    const regExp =
      /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return match && match[2].length === 11 ? match[2] : url;
  };

  const videoId = getYouTubeId(videoSrc);
  const embedUrl = `https://www.youtube.com/embed/${videoId}`;

  return (
    <div
      className={cn("relative w-full pt-8 pb-12", className)}
      style={{
        backgroundColor: backgroundColor,
      }}
    >
      <div
        className="w-full max-w-4xl mx-auto"
        style={{
          maxHeight: "400px",
        }}
      >
        {/* Banner ribbon */}
        <div className="absolute z-10 top-[-25px] left-1/2 transform -translate-x-1/2">
          <div className="relative">
            {/* Ribbon image */}
            <Image
              src={ribbonImagePath}
              alt="Ribbon"
              width={170}
              height={40}
              className="w-[170]"
            />
          </div>
        </div>

        {/* Video embed with fixed height */}
        <div className="w-full h-[400px] overflow-hidden">
          <iframe
            src={embedUrl}
            title="Featured video"
            className="w-full h-full"
            style={{
              width: width,
              height: "400px",
            }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen={allowFullscreen}
          />
        </div>
      </div>
    </div>
  );
};

export default VideoFeatureSection;
