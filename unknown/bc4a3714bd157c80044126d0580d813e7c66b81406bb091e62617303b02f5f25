"use server";

import {
  fetchFooterData,
  fetchHeaderData,
} from "@/services/fetch/layout-fetch";
import { fetchPageData } from "@/services/fetch/page-fetch";
import { fetchProductData } from "@/services/fetch/product-fetch";
import { FooterType } from "@/types/Collections/Footer";
import { HeaderType } from "@/types/Collections/Header";
import { GlobalSettingType } from "@/types/Collections/GlobalSettings";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";
import { PageType } from "@/types/Page";
import { fetchGlobalSettings } from "@/services/fetch/global-settings-fetch";
import { ExtendedMedusaProductWithStrapiProduct } from "@/types/Medusa/Product";

export const getFooterDetails = async (): Promise<FooterType | null> => {
  try {
    const response = await fetchFooterData();
    const { success, data, error } = response;

    if (!success) {
      console.error("Error fetching footer data:", JSON.stringify(error));
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching footer data:", error);
    return null;
  }
};

export const getHeaderDetails = async (): Promise<HeaderType | null> => {
  try {
    const response = await fetchHeaderData();
    const { success, data, error } = response;

    if (!success) {
      console.error("Error fetching header data:", JSON.stringify(error));
      return null;
    }

    return data;
  } catch (error) {
    console.error("Error fetching header data:", error);
    return null;
  }
};

export const getPageDetails = async (props: {
  pageHandle?: string;
  collectionHandle?: string;
  productHandle?: string;
}): Promise<PageType | null> => {
  try {
    const response = await fetchPageData(props);

    const { success, data, error } = response;

    if (!success) {
      console.error(
        `Error fetching Page data for ${
          props.pageHandle || props.collectionHandle || props.productHandle
        }:`,
        JSON.stringify(error)
      );
      return null;
    }

    return data;
  } catch (error) {
    console.error(
      `Error fetching Page data for ${
        props.pageHandle || props.collectionHandle || props.productHandle
      }:`,
      error
    );
    return null;
  }
};

// Base interface for all product fetch results
interface BaseProductFetchResult {
  strapiProduct: ProductDetailsType;
  medusaProduct: ExtendedMedusaProductWithStrapiProduct;
}

// BYOB product fetch result
interface BYOBProductFetchResult extends BaseProductFetchResult {
  bundleVariants: ProductDetailsType[];
  productType: "BYOB";
}

// Regular product fetch result
interface RegularProductFetchResult extends BaseProductFetchResult {
  productType: "VARIANT";
}

// Union type for all product fetch results
export type ProductFetchResult =
  | BYOBProductFetchResult
  | RegularProductFetchResult;

export const getProductDetails = async (props: {
  productHandle?: string;
}): Promise<ProductFetchResult | null> => {
  try {
    const response = await fetchProductData(props);

    const { success, data, error } = response;

    if (!success) {
      console.error(
        `Error fetching Page data for ${props.productHandle}:`,
        JSON.stringify(error)
      );
      return null;
    }

    // Return the data with proper typing based on product type
    return data as ProductFetchResult;
  } catch (error) {
    console.error(
      `Error fetching Page data for ${props.productHandle}:`,
      error
    );
    return null;
  }
};

export const getGlobalSettings =
  async (): Promise<GlobalSettingType | null> => {
    try {
      const response = await fetchGlobalSettings();
      const { success, data, error } = response;

      if (!success) {
        console.error("Error fetching global settings:", JSON.stringify(error));
        return null;
      }

      return data;
    } catch (error) {
      console.error("Error fetching global settings:", error);
      return null;
    }
  };
