// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface BorderlessProductListing {
  category?: { data: Category };
  title?: string;
  bg_color?: any;
}
export interface BorderlessProductListing_Plain {
  category?: Category_Plain;
  title?: string;
  bg_color?: any;
}

export interface BorderlessProductListing_NoRelations {
  category?: number;
  title?: string;
  bg_color?: any;
}

