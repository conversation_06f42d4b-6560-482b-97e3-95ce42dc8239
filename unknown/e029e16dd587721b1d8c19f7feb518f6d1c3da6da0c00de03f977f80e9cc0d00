import type { Schema, Struct } from '@strapi/strapi';

export interface BannerBannerCarousel extends Struct.ComponentSchema {
  collectionName: 'components_banner_banner_carousels';
  info: {
    description: '';
    displayName: 'Banner Carousel';
  };
  attributes: {
    images: Schema.Attribute.Component<'banner.single-banner', true>;
  };
}

export interface BannerBannerWithTitle extends Struct.ComponentSchema {
  collectionName: 'components_banner_banner_with_titles';
  info: {
    description: '';
    displayName: 'Banner with Title';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    bg_image: Schema.Attribute.Media<'images'>;
    image: Schema.Attribute.Component<'banner.single-banner', false>;
    show_on: Schema.Attribute.Enumeration<['WEB', 'MOBILE', 'BOTH']> &
      Schema.Attribute.DefaultTo<'BOTH'>;
    subtitle: Schema.Attribute.Text;
    subtitle_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
    title_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface BannerProteinBanner extends Struct.ComponentSchema {
  collectionName: 'components_banner_protein_banners';
  info: {
    description: '';
    displayName: 'Protein Banner';
  };
  attributes: {
    bg_image: Schema.Attribute.Component<'media.image', false>;
    image: Schema.Attribute.Component<'media.image', false>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface BannerSingleBanner extends Struct.ComponentSchema {
  collectionName: 'components_banner_single_banners';
  info: {
    description: '';
    displayName: 'Single Banner';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    mobile: Schema.Attribute.Media<'images' | 'files'>;
    mobile_ratio: Schema.Attribute.Relation<
      'oneToOne',
      'api::banners-aspect-ratio.banners-aspect-ratio'
    >;
    web: Schema.Attribute.Media<'images' | 'files' | 'videos'>;
    web_ratio: Schema.Attribute.Relation<
      'oneToOne',
      'api::banners-aspect-ratio.banners-aspect-ratio'
    >;
  };
}

export interface CareerEmployeeImages extends Struct.ComponentSchema {
  collectionName: 'components_career_employee_images';
  info: {
    displayName: 'Employee Images';
  };
  attributes: {
    image: Schema.Attribute.Media<'images'>;
  };
}

export interface CareerEmployeePhotoGrid extends Struct.ComponentSchema {
  collectionName: 'components_career_employee_photo_grids';
  info: {
    description: '';
    displayName: 'Employee Photo Grid';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    description: Schema.Attribute.Text;
    images: Schema.Attribute.Component<'career.employee-images', true>;
    title: Schema.Attribute.String;
  };
}

export interface CareerHiringProcess extends Struct.ComponentSchema {
  collectionName: 'components_career_hiring_processes';
  info: {
    description: '';
    displayName: 'Hiring Process';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    image: Schema.Attribute.Media<'images'>;
    title: Schema.Attribute.Text;
  };
}

export interface CareerJobPositions extends Struct.ComponentSchema {
  collectionName: 'components_career_job_positions';
  info: {
    description: '';
    displayName: 'Job Positions';
  };
  attributes: {};
}

export interface CareerPeopleLivingValueItems extends Struct.ComponentSchema {
  collectionName: 'components_career_people_living_value_items';
  info: {
    description: '';
    displayName: 'People Living Value Items';
  };
  attributes: {
    description: Schema.Attribute.Text;
    footer_icon: Schema.Attribute.Media<'images'>;
    footer_text: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    people_image: Schema.Attribute.Media<'images'>;
  };
}

export interface CareerPeopleLivingValues extends Struct.ComponentSchema {
  collectionName: 'components_career_people_living_values';
  info: {
    description: '';
    displayName: 'People Living Values';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    people_value_items: Schema.Attribute.Component<
      'career.people-living-value-items',
      true
    >;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface CareerStopTheLies extends Struct.ComponentSchema {
  collectionName: 'components_career_stop_the_lies';
  info: {
    displayName: 'Stop the Lies';
  };
  attributes: {
    media_images: Schema.Attribute.Component<'media.icon', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface CareerValueItems extends Struct.ComponentSchema {
  collectionName: 'components_career_value_items';
  info: {
    description: '';
    displayName: 'Value Items';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    description: Schema.Attribute.Text;
    footer_icon: Schema.Attribute.Media<'images'>;
    footer_text: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    image: Schema.Attribute.Media<'images'>;
    primary_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface CareerValues extends Struct.ComponentSchema {
  collectionName: 'components_career_values';
  info: {
    description: '';
    displayName: 'Values';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    value_items: Schema.Attribute.Component<'career.value-items', true>;
  };
}

export interface CareerWholeGang extends Struct.ComponentSchema {
  collectionName: 'components_career_whole_gangs';
  info: {
    description: '';
    displayName: 'Whole Gang';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    items: Schema.Attribute.Component<'common.flippable-card-items', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface CareerYoutubeBanner extends Struct.ComponentSchema {
  collectionName: 'components_career_youtube_banners';
  info: {
    description: '';
    displayName: 'Youtube Banner';
  };
  attributes: {
    auto_play: Schema.Attribute.Boolean & Schema.Attribute.DefaultTo<true>;
    bg_image: Schema.Attribute.Media<'images'>;
    button_text: Schema.Attribute.String;
    description: Schema.Attribute.Text;
    thumbnail_image: Schema.Attribute.Media<'images'>;
    title: Schema.Attribute.Text;
    youtube_link: Schema.Attribute.Text;
  };
}

export interface CertificatesCertificateItems extends Struct.ComponentSchema {
  collectionName: 'components_certificates_certificate_items';
  info: {
    description: '';
    displayName: 'Certificate Items';
  };
  attributes: {
    image: Schema.Attribute.Component<'media.image', false>;
    redirect_link: Schema.Attribute.Text;
  };
}

export interface CertificatesCertificateManager extends Struct.ComponentSchema {
  collectionName: 'components_certificates_certificate_managers';
  info: {
    description: '';
    displayName: 'Certificate Manager';
  };
  attributes: {
    bg_image: Schema.Attribute.Component<'media.image', false>;
    certificate_items: Schema.Attribute.Component<'banner.single-banner', true>;
  };
}

export interface CommonAnnouncementBar extends Struct.ComponentSchema {
  collectionName: 'components_common_announcement_bars';
  info: {
    displayName: 'Announcement Bar';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    titles: Schema.Attribute.Component<'elements.link-text', true>;
  };
}

export interface CommonCashback extends Struct.ComponentSchema {
  collectionName: 'components_common_cashbacks';
  info: {
    description: '';
    displayName: 'Cashback';
  };
  attributes: {
    description: Schema.Attribute.Text;
    icon: Schema.Attribute.Media<'images'>;
    tooltip: Schema.Attribute.Text;
  };
}

export interface CommonFlippableCard extends Struct.ComponentSchema {
  collectionName: 'components_common_flippable_cards';
  info: {
    description: '';
    displayName: 'Flippable Card';
  };
  attributes: {
    flippable_card_items: Schema.Attribute.Component<
      'common.flippable-card-items',
      true
    >;
  };
}

export interface CommonFlippableCardItems extends Struct.ComponentSchema {
  collectionName: 'components_common_flippable_card_items';
  info: {
    displayName: 'Flippable Card Items';
  };
  attributes: {
    back: Schema.Attribute.Component<'media.image', false>;
    front: Schema.Attribute.Component<'media.image', false>;
  };
}

export interface CommonMarquee extends Struct.ComponentSchema {
  collectionName: 'components_common_marquees';
  info: {
    displayName: 'Marquee';
  };
  attributes: {
    titles: Schema.Attribute.Component<'common.titles', true>;
  };
}

export interface CommonMenu extends Struct.ComponentSchema {
  collectionName: 'components_common_menus';
  info: {
    description: '';
    displayName: 'Menu';
  };
  attributes: {
    menu_items: Schema.Attribute.Component<'layout.footer-menu', true>;
  };
}

export interface CommonPressLogoMarquee extends Struct.ComponentSchema {
  collectionName: 'components_common_press_logo_marquees';
  info: {
    displayName: 'Press Logo Marquee';
  };
  attributes: {
    images: Schema.Attribute.Component<'media.icon', true>;
  };
}

export interface CommonSocialMedia extends Struct.ComponentSchema {
  collectionName: 'components_common_social_medias';
  info: {
    description: '';
    displayName: 'Social Media';
  };
  attributes: {
    action_link: Schema.Attribute.String;
    image: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface CommonTitles extends Struct.ComponentSchema {
  collectionName: 'components_common_titles';
  info: {
    displayName: 'titles';
  };
  attributes: {
    title: Schema.Attribute.String;
  };
}

export interface ContactUsChatButton extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_chat_buttons';
  info: {
    displayName: 'Chat Button';
  };
  attributes: {
    icon: Schema.Attribute.Media<'images'>;
    link: Schema.Attribute.Text;
    text: Schema.Attribute.String;
  };
}

export interface ContactUsContactInfo extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_infos';
  info: {
    displayName: 'Contact Info';
  };
  attributes: {
    contact_info_items: Schema.Attribute.Component<
      'contact-us.contact-info-items',
      true
    >;
  };
}

export interface ContactUsContactInfoItems extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_info_items';
  info: {
    displayName: 'Contact Info Items';
  };
  attributes: {
    description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    icon: Schema.Attribute.Media<'images'>;
  };
}

export interface ContactUsContactUs extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_uses';
  info: {
    description: '';
    displayName: 'Contact Us';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    bg_icon: Schema.Attribute.Media<'images'>;
    chat_button: Schema.Attribute.Component<'contact-us.chat-button', false>;
    description: Schema.Attribute.Text;
    primary_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
  };
}

export interface ContactUsContactUsPage extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_contact_us_pages';
  info: {
    description: '';
    displayName: 'Contact Us Page';
  };
  attributes: {
    contact_us: Schema.Attribute.Component<'contact-us.contact-us', false>;
    faqs: Schema.Attribute.Component<'contact-us.fa-qs', false>;
  };
}

export interface ContactUsFaQs extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_fa_qs';
  info: {
    description: '';
    displayName: 'FAQs';
  };
  attributes: {
    contact_info: Schema.Attribute.Component<'contact-us.contact-info', false>;
    description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    faq_section: Schema.Attribute.Component<'contact-us.faq-section', false>;
    title: Schema.Attribute.Text;
    working_hours: Schema.Attribute.Component<
      'contact-us.working-hours',
      false
    >;
  };
}

export interface ContactUsFaqSection extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_faq_sections';
  info: {
    description: '';
    displayName: 'FAQ Section';
  };
  attributes: {
    faq_section_items: Schema.Attribute.Component<
      'contact-us.faq-section-items',
      true
    >;
  };
}

export interface ContactUsFaqSectionItems extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_faq_section_items';
  info: {
    description: '';
    displayName: 'FAQ Section Items';
  };
  attributes: {
    faq_items: Schema.Attribute.Component<'faqs.faq-items', true>;
    header: Schema.Attribute.String;
  };
}

export interface ContactUsWorkingHours extends Struct.ComponentSchema {
  collectionName: 'components_contact_us_working_hours';
  info: {
    description: '';
    displayName: 'Working Hours';
  };
  attributes: {
    comment: Schema.Attribute.Text;
    description: Schema.Attribute.Text;
    title: Schema.Attribute.String;
  };
}

export interface ElementsButton extends Struct.ComponentSchema {
  collectionName: 'components_elements_buttons';
  info: {
    description: '';
    displayName: 'Button';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
    title_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface ElementsHeader extends Struct.ComponentSchema {
  collectionName: 'components_elements_headers';
  info: {
    displayName: 'Header';
  };
  attributes: {
    title: Schema.Attribute.Text;
  };
}

export interface ElementsHtmlEditor extends Struct.ComponentSchema {
  collectionName: 'components_elements_html_editors';
  info: {
    displayName: 'HTML Editor';
  };
  attributes: {
    html_editor: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ElementsKeyValue extends Struct.ComponentSchema {
  collectionName: 'components_elements_key_values';
  info: {
    description: '';
    displayName: 'Key-Value';
  };
  attributes: {
    key: Schema.Attribute.Text;
    value: Schema.Attribute.Text;
  };
}

export interface ElementsLinkText extends Struct.ComponentSchema {
  collectionName: 'components_elements_link_texts';
  info: {
    description: '';
    displayName: 'Link Text';
  };
  attributes: {
    action_link: Schema.Attribute.String;
    text: Schema.Attribute.Text;
  };
}

export interface ElementsTitle extends Struct.ComponentSchema {
  collectionName: 'components_elements_titles';
  info: {
    description: '';
    displayName: 'Section Heading';
  };
  attributes: {
    color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    icon: Schema.Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    iconColor: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    iconPosition: Schema.Attribute.Enumeration<['left', 'right', 'top']>;
    title: Schema.Attribute.String;
  };
}

export interface FaqsFaqItems extends Struct.ComponentSchema {
  collectionName: 'components_fa_qs_faq_items';
  info: {
    displayName: 'FAQ Items';
  };
  attributes: {
    answer: Schema.Attribute.Text;
    question: Schema.Attribute.Text;
  };
}

export interface FaqsFaqTemplate extends Struct.ComponentSchema {
  collectionName: 'components_fa_qs_faq_templates';
  info: {
    description: '';
    displayName: 'FAQ Template';
  };
  attributes: {};
}

export interface FaqsFaqs extends Struct.ComponentSchema {
  collectionName: 'components_fa_qs_fa_qs';
  info: {
    description: '';
    displayName: 'FAQs';
  };
  attributes: {
    faq_items: Schema.Attribute.Component<'faqs.faq-items', true>;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface HeaderExternalLinks extends Struct.ComponentSchema {
  collectionName: 'components_header_external_links';
  info: {
    description: '';
    displayName: 'External Links';
  };
  attributes: {
    external_link_items: Schema.Attribute.Component<'elements.link-text', true>;
  };
}

export interface HeaderHeaderMenu extends Struct.ComponentSchema {
  collectionName: 'components_header_header_menus';
  info: {
    description: '';
    displayName: 'Header Menu';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    image: Schema.Attribute.Component<'media.image', false>;
    menu_items: Schema.Attribute.Component<'header.menu-items', true>;
    tag: Schema.Attribute.Text;
    title: Schema.Attribute.Text & Schema.Attribute.Required;
  };
}

export interface HeaderMenuItems extends Struct.ComponentSchema {
  collectionName: 'components_header_menu_items';
  info: {
    displayName: 'Menu Items';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    image: Schema.Attribute.Component<'media.image', false>;
    title: Schema.Attribute.Text;
  };
}

export interface HomepageBigFoodDidItem extends Struct.ComponentSchema {
  collectionName: 'components_homepage_big_food_did_items';
  info: {
    description: '';
    displayName: 'Big Food Did Item';
  };
  attributes: {
    comment: Schema.Attribute.Text;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    youtube_link: Schema.Attribute.Component<
      'media.external-media-embed',
      false
    >;
  };
}

export interface HomepageFixingFood extends Struct.ComponentSchema {
  collectionName: 'components_homepage_fixing_foods';
  info: {
    description: '';
    displayName: 'Fixing Food';
  };
  attributes: {
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Component<'media.image', false>;
    title: Schema.Attribute.Text;
  };
}

export interface HomepageGoTruthSeekers extends Struct.ComponentSchema {
  collectionName: 'components_homepage_go_truth_seekers';
  info: {
    description: '';
    displayName: 'Go Truth Seekers';
  };
  attributes: {
    instagram_community: Schema.Attribute.Component<'elements.button', false>;
    linkedin_community: Schema.Attribute.Component<'elements.button', false>;
    title: Schema.Attribute.Text;
    title_image: Schema.Attribute.Component<'media.image-or-video', false>;
    video: Schema.Attribute.Component<'media.image-or-video', false>;
    youtube_community: Schema.Attribute.Component<'elements.button', false>;
  };
}

export interface HomepageHeadToInfiniteLove extends Struct.ComponentSchema {
  collectionName: 'components_homepage_head_to_infinite_loves';
  info: {
    displayName: 'Head to Infinite Love';
  };
  attributes: {
    image: Schema.Attribute.Component<'media.image-or-video', false>;
    infinite_love_button_text: Schema.Attribute.Component<
      'elements.button',
      false
    >;
  };
}

export interface HomepagePressQuoteItems extends Struct.ComponentSchema {
  collectionName: 'components_homepage_press_quote_items';
  info: {
    description: '';
    displayName: 'Press Quote Items';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    press_logo: Schema.Attribute.Component<'media.icon', false>;
    press_name: Schema.Attribute.String;
    quote: Schema.Attribute.Text;
    subtitle: Schema.Attribute.String;
  };
}

export interface HomepagePyaarSecretIngredient extends Struct.ComponentSchema {
  collectionName: 'components_homepage_pyaar_secret_ingredients';
  info: {
    description: '';
    displayName: 'Pyaar: Secret Ingredient';
    icon: '';
  };
  attributes: {
    button_image: Schema.Attribute.Component<'media.icon', false>;
    secret_ingredient_item: Schema.Attribute.Component<
      'homepage.big-food-did-item',
      false
    >;
  };
}

export interface HomepageRealFood extends Struct.ComponentSchema {
  collectionName: 'components_homepage_real_foods';
  info: {
    displayName: 'Real Food';
  };
  attributes: {
    image: Schema.Attribute.Component<'media.image', false>;
    long_description: Schema.Attribute.Text;
    short_description: Schema.Attribute.Text;
  };
}

export interface HomepageRealFoodCategories extends Struct.ComponentSchema {
  collectionName: 'components_homepage_real_food_categories';
  info: {
    description: '';
    displayName: 'Real Food Categories';
  };
  attributes: {
    multi_category_items: Schema.Attribute.Component<
      'plp.multi-category-items',
      true
    >;
    subtitle: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    title: Schema.Attribute.Text;
  };
}

export interface HomepageRealFoodIsFlawed extends Struct.ComponentSchema {
  collectionName: 'components_homepage_real_food_is_flaweds';
  info: {
    description: '';
    displayName: 'Real Food is Flawed';
  };
  attributes: {
    description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    videos: Schema.Attribute.Component<'media.video', true>;
  };
}

export interface HomepageRealPeopleLove extends Struct.ComponentSchema {
  collectionName: 'components_homepage_real_people_loves';
  info: {
    description: '';
    displayName: 'Real People Love';
  };
  attributes: {
    likes_count: Schema.Attribute.String;
    real_people_comments: Schema.Attribute.Component<
      'common.flippable-card-items',
      true
    >;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface HomepageRebuildingTheTrust extends Struct.ComponentSchema {
  collectionName: 'components_homepage_rebuilding_the_trusts';
  info: {
    description: '';
    displayName: 'Rebuilding the Trust';
  };
  attributes: {
    description: Schema.Attribute.Text;
    food_items: Schema.Attribute.Component<
      'homepage.trust-building-food-items',
      true
    >;
    short_story_button_text: Schema.Attribute.Text;
    short_story_description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface HomepageStarsLoveUs extends Struct.ComponentSchema {
  collectionName: 'components_homepage_stars_love_uses';
  info: {
    description: '';
    displayName: 'Stars Love Us';
  };
  attributes: {
    image: Schema.Attribute.Component<'media.image-or-video', false>;
    star_comments: Schema.Attribute.Component<'reviews.stars', true>;
    subtitle: Schema.Attribute.Text;
  };
}

export interface HomepageStopThePress extends Struct.ComponentSchema {
  collectionName: 'components_homepage_stop_the_presses';
  info: {
    description: '';
    displayName: 'Stop The Press';
  };
  attributes: {
    press_quote_items: Schema.Attribute.Component<
      'homepage.press-quote-items',
      true
    >;
    title_image: Schema.Attribute.Component<'media.image', false>;
  };
}

export interface HomepageTrustBuildingFoodItems extends Struct.ComponentSchema {
  collectionName: 'components_homepage_trust_building_food_items';
  info: {
    displayName: 'Trust Building Food Items';
  };
  attributes: {
    category: Schema.Attribute.Enumeration<
      ['POWDER', 'CHOCOLATE', 'BAR', 'PEANUT BUTTER']
    > &
      Schema.Attribute.Required;
    primary_image: Schema.Attribute.Media<'images'>;
    secondary_image: Schema.Attribute.Media<'images'>;
  };
}

export interface HomepageWhoBrokeTrust extends Struct.ComponentSchema {
  collectionName: 'components_homepage_who_broke_trusts';
  info: {
    description: '';
    displayName: 'Who Broke Trust';
  };
  attributes: {
    big_food_did: Schema.Attribute.Component<
      'homepage.big-food-did-item',
      false
    >;
    read_story_button: Schema.Attribute.Component<'elements.button', false>;
  };
}

export interface LayoutFooterMenu extends Struct.ComponentSchema {
  collectionName: 'components_layout_footer_menus';
  info: {
    description: '';
    displayName: 'Footer Menu';
  };
  attributes: {
    items: Schema.Attribute.Component<'elements.link-text', true>;
  };
}

export interface MediaExternalMediaEmbed extends Struct.ComponentSchema {
  collectionName: 'components_media_external_media_embeds';
  info: {
    description: '';
    displayName: 'External Media Embed';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    thumbnail_image: Schema.Attribute.Media<'images'>;
  };
}

export interface MediaIcon extends Struct.ComponentSchema {
  collectionName: 'components_media_icons';
  info: {
    displayName: 'Icon';
  };
  attributes: {
    image: Schema.Attribute.Media<'images'>;
  };
}

export interface MediaImage extends Struct.ComponentSchema {
  collectionName: 'components_media_images';
  info: {
    description: '';
    displayName: 'Image';
  };
  attributes: {
    mobile: Schema.Attribute.Media<'images' | 'files'>;
    mobile_ratio: Schema.Attribute.Relation<
      'oneToOne',
      'api::banners-aspect-ratio.banners-aspect-ratio'
    >;
    web: Schema.Attribute.Media<'images' | 'files'>;
    web_ratio: Schema.Attribute.Relation<
      'oneToOne',
      'api::banners-aspect-ratio.banners-aspect-ratio'
    >;
  };
}

export interface MediaImageOrVideo extends Struct.ComponentSchema {
  collectionName: 'components_media_image_or_videos';
  info: {
    description: '';
    displayName: 'Image Or Video';
  };
  attributes: {
    mobile: Schema.Attribute.Media<'images' | 'videos'>;
    web: Schema.Attribute.Media<'images' | 'videos'>;
  };
}

export interface MediaVideo extends Struct.ComponentSchema {
  collectionName: 'components_media_videos';
  info: {
    displayName: 'Video';
  };
  attributes: {
    video: Schema.Attribute.Media<'videos'>;
  };
}

export interface PdpTemplatesCertificateBannerTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_certificate_banner_templates';
  info: {
    description: '';
    displayName: 'Certificate Banner Template';
  };
  attributes: {};
}

export interface PdpTemplatesGotAQuestionTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_got_a_question_templates';
  info: {
    description: '';
    displayName: 'Got A Question Template';
  };
  attributes: {};
}

export interface PdpTemplatesIngredientDetailsTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_ingredient_details_templates';
  info: {
    description: '';
    displayName: 'Ingredient Details Template';
  };
  attributes: {};
}

export interface PdpTemplatesKeyFeaturesTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_key_features_templates';
  info: {
    description: '';
    displayName: 'Key Features Template';
  };
  attributes: {};
}

export interface PdpTemplatesNutritionalFactsTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_nutritional_facts_templates';
  info: {
    description: '';
    displayName: 'Nutritional Facts Template';
  };
  attributes: {};
}

export interface PdpTemplatesProductAdditionalDescriptionTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_product_additional_description_templates';
  info: {
    description: '';
    displayName: 'Product Additional Description Template';
  };
  attributes: {};
}

export interface PdpTemplatesProductDetailsExtraTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_product_details_extra_templates';
  info: {
    description: '';
    displayName: 'Product Details Extra Template';
  };
  attributes: {};
}

export interface PdpTemplatesQuicklyAddedTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_quickly_added_templates';
  info: {
    description: '';
    displayName: 'Quickly Added Template';
  };
  attributes: {};
}

export interface PdpTemplatesReviewTemplate extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_review_templates';
  info: {
    description: '';
    displayName: 'Review Template';
  };
  attributes: {};
}

export interface PdpTemplatesWhatsInsideTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_whats_inside_templates';
  info: {
    description: '';
    displayName: 'Whats Inside Template';
  };
  attributes: {};
}

export interface PdpTemplatesWhySectionTemplate extends Struct.ComponentSchema {
  collectionName: 'components_pdp_templates_why_section_templates';
  info: {
    description: '';
    displayName: 'Why Section Template';
  };
  attributes: {};
}

export interface PdpDeliveryOptions extends Struct.ComponentSchema {
  collectionName: 'components_pdp_delivery_options';
  info: {
    description: '';
    displayName: 'Delivery Options';
  };
  attributes: {
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    title: Schema.Attribute.Text;
  };
}

export interface PdpIngredientDetails extends Struct.ComponentSchema {
  collectionName: 'components_pdp_ingredient_details';
  info: {
    description: '';
    displayName: 'Ingredient Details';
  };
  attributes: {
    description: Schema.Attribute.String;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
  };
}

export interface PdpKeyFeatures extends Struct.ComponentSchema {
  collectionName: 'components_pdp_key_features';
  info: {
    description: '';
    displayName: 'Key Features';
  };
  attributes: {
    key_feature_items: Schema.Attribute.Component<
      'common.flippable-card',
      false
    >;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    title: Schema.Attribute.Text;
  };
}

export interface PdpNutritionalFactDetails extends Struct.ComponentSchema {
  collectionName: 'components_pdp_nutritional_fact_details';
  info: {
    description: '';
    displayName: 'Nutritional Fact Details';
  };
  attributes: {
    nutritional_fact_items: Schema.Attribute.Component<
      'pdp.nutritional-fact-items',
      true
    >;
  };
}

export interface PdpNutritionalFactItems extends Struct.ComponentSchema {
  collectionName: 'components_pdp_nutritional_fact_items';
  info: {
    description: '';
    displayName: 'Nutritional Fact Items';
  };
  attributes: {
    key: Schema.Attribute.Text;
    nutritional_fact_sub_items: Schema.Attribute.Component<
      'pdp.nutritional-fact-sub-items',
      true
    >;
    value: Schema.Attribute.Text;
  };
}

export interface PdpNutritionalFactSubItems extends Struct.ComponentSchema {
  collectionName: 'components_pdp_nutritional_fact_sub_items';
  info: {
    description: '';
    displayName: 'Nutritional Fact Sub Items';
  };
  attributes: {
    key: Schema.Attribute.Text;
    value: Schema.Attribute.Text;
  };
}

export interface PdpNutritionalFacts extends Struct.ComponentSchema {
  collectionName: 'components_pdp_nutritional_facts';
  info: {
    description: '';
    displayName: 'Nutritional Facts';
  };
  attributes: {
    description: Schema.Attribute.Text;
    nutritional_fact_details: Schema.Attribute.Component<
      'pdp.nutritional-fact-details',
      true
    >;
    show_component: Schema.Attribute.Boolean;
  };
}

export interface PdpOffer extends Struct.ComponentSchema {
  collectionName: 'components_pdp_offers';
  info: {
    description: '';
    displayName: 'Offer';
  };
  attributes: {
    code: Schema.Attribute.String;
    description: Schema.Attribute.Text;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
  };
}

export interface PdpProductAdditionalDescription
  extends Struct.ComponentSchema {
  collectionName: 'components_pdp_product_additional_descriptions';
  info: {
    description: '';
    displayName: 'Product Additional Description';
  };
  attributes: {
    description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    show_component: Schema.Attribute.Boolean;
  };
}

export interface PdpProductDetailsExtra extends Struct.ComponentSchema {
  collectionName: 'components_pdp_product_details_extras';
  info: {
    description: '';
    displayName: 'Product Details Extra';
  };
  attributes: {
    product_detail_extra_items: Schema.Attribute.Component<
      'elements.key-value',
      true
    >;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    title: Schema.Attribute.String;
  };
}

export interface PdpWhatsInside extends Struct.ComponentSchema {
  collectionName: 'components_pdp_whats_insides';
  info: {
    description: '';
    displayName: 'Whats Inside';
  };
  attributes: {
    show_component: Schema.Attribute.Boolean;
    title: Schema.Attribute.Text;
    whats_inside_details: Schema.Attribute.Component<
      'pdp.whats-inside-details',
      true
    >;
  };
}

export interface PdpWhatsInsideDetails extends Struct.ComponentSchema {
  collectionName: 'components_pdp_whats_inside_details';
  info: {
    description: '';
    displayName: 'Whats Inside Details';
  };
  attributes: {
    details: Schema.Attribute.Component<'elements.key-value', true>;
    image: Schema.Attribute.Component<'media.image', false>;
    title: Schema.Attribute.Text;
  };
}

export interface PdpWhySection extends Struct.ComponentSchema {
  collectionName: 'components_pdp_why_sections';
  info: {
    description: '';
    displayName: 'Why Section';
  };
  attributes: {
    description: Schema.Attribute.Text;
    image_carousels: Schema.Attribute.Component<
      'banner.banner-carousel',
      false
    >;
    primary_image: Schema.Attribute.Component<'media.image', false>;
    show_component: Schema.Attribute.Boolean &
      Schema.Attribute.Required &
      Schema.Attribute.DefaultTo<true>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpBadaamProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_badaam_product_listings';
  info: {
    displayName: 'Badaam Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpBorderlessProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_borderless_product_listings';
  info: {
    description: '';
    displayName: 'Borderless Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.String;
  };
}

export interface PlpDarkChocolateProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_dark_chocolate_product_listings';
  info: {
    description: '';
    displayName: 'Dark Chocolate Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpFlavourIngredientItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_flavour_ingredient_items';
  info: {
    displayName: 'Flavour Ingredient Items';
  };
  attributes: {
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Component<'media.icon', false>;
    percent: Schema.Attribute.Decimal;
    title: Schema.Attribute.Text;
  };
}

export interface PlpFlavourItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_flavour_items';
  info: {
    description: '';
    displayName: 'Flavour Items';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    button: Schema.Attribute.Component<'elements.button', false>;
    flavour_product_item: Schema.Attribute.Component<
      'plp.flavour-product-item',
      false
    >;
    ingredient_items: Schema.Attribute.Component<
      'plp.flavour-ingredient-items',
      true
    >;
    title: Schema.Attribute.Text;
    title_icon: Schema.Attribute.Component<'media.icon', false>;
  };
}

export interface PlpFlavourProductItem extends Struct.ComponentSchema {
  collectionName: 'components_plp_flavour_product_items';
  info: {
    description: '';
    displayName: 'Flavour Product Item';
  };
  attributes: {
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Component<'media.icon', false>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface PlpHashtagComment extends Struct.ComponentSchema {
  collectionName: 'components_plp_hashtag_comments';
  info: {
    displayName: 'Hashtag Comment';
  };
  attributes: {
    text: Schema.Attribute.Text;
  };
}

export interface PlpIndulgentFlavours extends Struct.ComponentSchema {
  collectionName: 'components_plp_indulgent_flavours';
  info: {
    description: '';
    displayName: 'Indulgent Flavours';
  };
  attributes: {
    flavour_items: Schema.Attribute.Component<'plp.flavour-items', true>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface PlpJustIngredientItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_just_ingredient_items';
  info: {
    description: '';
    displayName: 'Just Ingredient Items';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    image: Schema.Attribute.Component<'media.icon', false>;
    ingredient_items: Schema.Attribute.Component<'elements.key-value', true>;
    primary_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpJustIngredients extends Struct.ComponentSchema {
  collectionName: 'components_plp_just_ingredients';
  info: {
    description: '';
    displayName: 'Just Ingredients';
  };
  attributes: {
    just_ingredient_items: Schema.Attribute.Component<
      'plp.just-ingredient-items',
      true
    >;
    title: Schema.Attribute.Text;
  };
}

export interface PlpLongDescription extends Struct.ComponentSchema {
  collectionName: 'components_plp_long_descriptions';
  info: {
    description: '';
    displayName: 'Long Description';
  };
  attributes: {
    description: Schema.Attribute.Text;
  };
}

export interface PlpMilkChocolateProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_milk_chocolate_product_listings';
  info: {
    description: '';
    displayName: 'Milk Chocolate Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpMultiCategories extends Struct.ComponentSchema {
  collectionName: 'components_plp_multi_categories';
  info: {
    description: '';
    displayName: 'Multi Categories';
  };
  attributes: {
    multi_category_items: Schema.Attribute.Component<
      'plp.multi-category-items',
      true
    >;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface PlpMultiCategoryItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_multi_category_items';
  info: {
    description: '';
    displayName: 'Multi Category Items';
  };
  attributes: {
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    image: Schema.Attribute.Component<'media.icon', false>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface PlpNothingToHide extends Struct.ComponentSchema {
  collectionName: 'components_plp_nothing_to_hides';
  info: {
    description: '';
    displayName: 'Nothing To Hide';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
    youtube_link: Schema.Attribute.Component<
      'media.external-media-embed',
      false
    >;
  };
}

export interface PlpPickYourProtein extends Struct.ComponentSchema {
  collectionName: 'components_plp_pick_your_proteins';
  info: {
    displayName: 'Pick Your Protein';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    protein_items: Schema.Attribute.Component<
      'plp.pick-your-protein-items',
      true
    >;
  };
}

export interface PlpPickYourProteinItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_pick_your_protein_items';
  info: {
    displayName: 'Pick Your Protein Items';
  };
  attributes: {
    action_link: Schema.Attribute.Text;
    image: Schema.Attribute.Component<'media.image', false>;
  };
}

export interface PlpProcessImages extends Struct.ComponentSchema {
  collectionName: 'components_plp_process_images';
  info: {
    description: '';
    displayName: 'Process Images';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    images: Schema.Attribute.Component<'media.icon', true>;
    subtitle: Schema.Attribute.Text;
    subtitle_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    title_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
  };
}

export interface PlpProteinBarProProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_protein_bar_pro_product_listings';
  info: {
    description: '';
    displayName: 'Protein Bar Pro Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpProteinPowderProductListing extends Struct.ComponentSchema {
  collectionName: 'components_plp_protein_powder_product_listings';
  info: {
    description: '';
    displayName: 'Protein Powder Product Listing';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpRealPeopleRealReviewComments
  extends Struct.ComponentSchema {
  collectionName: 'components_plp_real_people_real_review_comments';
  info: {
    description: '';
    displayName: 'Real People Real Review Comments';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    comment_items: Schema.Attribute.Component<'plp.review-comment-items', true>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpReviewCommentItems extends Struct.ComponentSchema {
  collectionName: 'components_plp_review_comment_items';
  info: {
    displayName: 'Review Comment Items';
  };
  attributes: {
    comment: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    name: Schema.Attribute.String;
    ratings: Schema.Attribute.Decimal;
  };
}

export interface PlpSachetBanner extends Struct.ComponentSchema {
  collectionName: 'components_plp_sachet_banners';
  info: {
    description: '';
    displayName: 'Sachet Banner';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    description: Schema.Attribute.Text;
    image: Schema.Attribute.Media<'images'>;
    product: Schema.Attribute.Relation<'oneToOne', 'api::product.product'>;
    title: Schema.Attribute.Text;
  };
}

export interface PlpShopChocolate extends Struct.ComponentSchema {
  collectionName: 'components_plp_shop_chocolates';
  info: {
    description: '';
    displayName: 'Shop Chocolate';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    button: Schema.Attribute.Component<'elements.button', false>;
    images: Schema.Attribute.Component<'media.icon', true>;
    primary_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    subtitle: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
  };
}

export interface PlpTwoSidedProductBanner extends Struct.ComponentSchema {
  collectionName: 'components_plp_two_sided_product_banners';
  info: {
    displayName: 'Two Sided Product Banner';
  };
  attributes: {
    banner_1: Schema.Attribute.Component<'banner.single-banner', false>;
    banner_2: Schema.Attribute.Component<'banner.single-banner', false>;
  };
}

export interface ReferReferAndEarn extends Struct.ComponentSchema {
  collectionName: 'components_refer_refer_and_earns';
  info: {
    description: '';
    displayName: 'Refer & Earn';
  };
  attributes: {
    form: Schema.Attribute.Component<'refer.refer-and-earn-form', false>;
    on_submit: Schema.Attribute.Component<
      'refer.refer-and-earn-thank-you-page',
      false
    >;
  };
}

export interface ReferReferAndEarnForm extends Struct.ComponentSchema {
  collectionName: 'components_refer_refer_and_earn_forms';
  info: {
    displayName: 'Refer & Earn Form';
  };
  attributes: {
    image: Schema.Attribute.Media<'images'>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReferReferAndEarnThankYouPage extends Struct.ComponentSchema {
  collectionName: 'components_refer_refer_and_earn_thank_you_pages';
  info: {
    description: '';
    displayName: 'Refer & Earn Thank you Page';
  };
  attributes: {
    description: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
    image: Schema.Attribute.Media<'images'>;
    link: Schema.Attribute.Text;
  };
}

export interface ReviewTemplatesRealPeopleReviewsTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_review_templates_real_people_reviews_templates';
  info: {
    description: '';
    displayName: 'Real People Reviews Template';
  };
  attributes: {};
}

export interface ReviewTemplatesVerifiedReviewsTemplate
  extends Struct.ComponentSchema {
  collectionName: 'components_review_templates_verified_reviews_templates';
  info: {
    description: '';
    displayName: 'Verified Reviews Template';
  };
  attributes: {};
}

export interface ReviewsCelebrityStars extends Struct.ComponentSchema {
  collectionName: 'components_reviews_celebrity_stars';
  info: {
    description: '';
    displayName: 'Celebrity Stars';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    stars: Schema.Attribute.Component<'reviews.stars', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsCommunityCommentItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_community_comment_items';
  info: {
    description: '';
    displayName: 'Community Comment Items';
  };
  attributes: {
    comment: Schema.Attribute.Text;
    comment_platform: Schema.Attribute.Enumeration<
      ['INSTAGRAM', 'TWITTER', 'LINKEDIN']
    >;
  };
}

export interface ReviewsCommunityPostItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_community_post_items';
  info: {
    description: '';
    displayName: 'Community Post Items';
  };
  attributes: {
    comment_items: Schema.Attribute.Component<
      'reviews.community-comment-items',
      true
    >;
    comments_count: Schema.Attribute.Integer;
    likes_count: Schema.Attribute.Integer;
    shared_count: Schema.Attribute.Integer;
    thumbnail_image: Schema.Attribute.Media<'images'>;
  };
}

export interface ReviewsContentCommentItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_content_comment_items';
  info: {
    displayName: 'Content Comment Items';
  };
  attributes: {
    comment: Schema.Attribute.Text;
    username: Schema.Attribute.String;
  };
}

export interface ReviewsContentItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_content_items';
  info: {
    description: '';
    displayName: 'Content Items';
  };
  attributes: {
    comments: Schema.Attribute.Component<'reviews.content-comment-items', true>;
    ratings: Schema.Attribute.Text;
    read_more_comment_link: Schema.Attribute.Text;
    title: Schema.Attribute.Text;
    youtube_link: Schema.Attribute.Text;
  };
}

export interface ReviewsFamCommentItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_fam_comment_items';
  info: {
    description: '';
    displayName: 'Fam Comment Items';
  };
  attributes: {
    company_logo: Schema.Attribute.Media<'images'>;
    fam_comment: Schema.Attribute.Text;
    fam_designation: Schema.Attribute.String;
    fam_image: Schema.Attribute.Media<'images'>;
    fam_name: Schema.Attribute.String;
  };
}

export interface ReviewsGangItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_gang_items';
  info: {
    description: '';
    displayName: 'Gang Items';
  };
  attributes: {
    platform_name: Schema.Attribute.String;
    thumbnail_image: Schema.Attribute.Media<'images'>;
    total_followers: Schema.Attribute.String;
  };
}

export interface ReviewsInfluencerTruth extends Struct.ComponentSchema {
  collectionName: 'components_reviews_influencer_truths';
  info: {
    description: '';
    displayName: 'Influencer Truth';
  };
  attributes: {
    description: Schema.Attribute.Text;
    mobile_bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    mobile_description_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    mobile_text_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    title: Schema.Attribute.Text;
    web_bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    web_description_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    web_title_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    youtube_link: Schema.Attribute.Component<'common.social-media', false>;
  };
}

export interface ReviewsJoinTheGang extends Struct.ComponentSchema {
  collectionName: 'components_reviews_join_the_gangs';
  info: {
    description: '';
    displayName: 'Join The Gang';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    items: Schema.Attribute.Component<'reviews.gang-items', true>;
    title: Schema.Attribute.String;
  };
}

export interface ReviewsLoveForContent extends Struct.ComponentSchema {
  collectionName: 'components_reviews_love_for_contents';
  info: {
    description: '';
    displayName: 'Love For Content';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    content_items: Schema.Attribute.Component<'reviews.content-items', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsLoveWall extends Struct.ComponentSchema {
  collectionName: 'components_reviews_love_walls';
  info: {
    description: '';
    displayName: 'Love Wall';
  };
  attributes: {
    bg_image: Schema.Attribute.Media<'images'>;
    image: Schema.Attribute.Media<'images'>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsOurCommunity extends Struct.ComponentSchema {
  collectionName: 'components_reviews_our_communities';
  info: {
    description: '';
    displayName: 'Our Community';
  };
  attributes: {
    bg_image: Schema.Attribute.Media<'images'>;
    community_post_items: Schema.Attribute.Component<
      'reviews.community-post-items',
      true
    >;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsPressQuoteItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_press_quote_items';
  info: {
    displayName: 'Press Quote Items';
  };
  attributes: {
    press_logo: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    quote: Schema.Attribute.Text;
  };
}

export interface ReviewsRealPeopleReviews extends Struct.ComponentSchema {
  collectionName: 'components_reviews_real_people_reviews';
  info: {
    description: '';
    displayName: 'Real People Reviews';
  };
  attributes: {
    review_items: Schema.Attribute.Component<'common.flippable-card', false>;
    title: Schema.Attribute.Text;
  };
}

export interface ReviewsStars extends Struct.ComponentSchema {
  collectionName: 'components_reviews_stars';
  info: {
    description: '';
    displayName: 'Star Items';
  };
  attributes: {
    star_image: Schema.Attribute.Media<
      'images' | 'files' | 'videos' | 'audios'
    >;
    star_name: Schema.Attribute.String;
    star_post: Schema.Attribute.Media<'images'>;
  };
}

export interface ReviewsStartupFam extends Struct.ComponentSchema {
  collectionName: 'components_reviews_startup_fams';
  info: {
    description: '';
    displayName: 'Startup Fam';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    fam_comment_items: Schema.Attribute.Component<
      'reviews.fam-comment-items',
      true
    >;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsStopThePress extends Struct.ComponentSchema {
  collectionName: 'components_reviews_stop_the_presses';
  info: {
    description: '';
    displayName: 'Stop the Press';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    bottom_icon: Schema.Attribute.Media<'images'>;
    press_quotes: Schema.Attribute.Component<'reviews.press-quote-items', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface ReviewsTruthSayerItems extends Struct.ComponentSchema {
  collectionName: 'components_reviews_truth_sayer_items';
  info: {
    displayName: 'Truth Sayer Items';
  };
  attributes: {
    image: Schema.Attribute.Media<'images'>;
    name: Schema.Attribute.String;
    username: Schema.Attribute.String;
  };
}

export interface ReviewsTruthSayers extends Struct.ComponentSchema {
  collectionName: 'components_reviews_truth_sayers';
  info: {
    description: '';
    displayName: 'Truth Sayers';
  };
  attributes: {
    bg_color: Schema.Attribute.String &
      Schema.Attribute.CustomField<'plugin::color-picker.color'>;
    items: Schema.Attribute.Component<'reviews.truth-sayer-items', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface SearchSearchKeywords extends Struct.ComponentSchema {
  collectionName: 'components_search_search_keywords';
  info: {
    description: '';
    displayName: 'Search Keywords';
  };
  attributes: {
    keyword_items: Schema.Attribute.Component<'common.titles', true>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

export interface SearchSearchUpsellCollections extends Struct.ComponentSchema {
  collectionName: 'components_search_search_upsell_collections';
  info: {
    description: '';
    displayName: 'Search Upsell Collections';
  };
  attributes: {
    category: Schema.Attribute.Relation<'oneToOne', 'api::category.category'>;
    title: Schema.Attribute.RichText &
      Schema.Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'defaultHtml';
        }
      >;
  };
}

declare module '@strapi/strapi' {
  export module Public {
    export interface ComponentSchemas {
      'banner.banner-carousel': BannerBannerCarousel;
      'banner.banner-with-title': BannerBannerWithTitle;
      'banner.protein-banner': BannerProteinBanner;
      'banner.single-banner': BannerSingleBanner;
      'career.employee-images': CareerEmployeeImages;
      'career.employee-photo-grid': CareerEmployeePhotoGrid;
      'career.hiring-process': CareerHiringProcess;
      'career.job-positions': CareerJobPositions;
      'career.people-living-value-items': CareerPeopleLivingValueItems;
      'career.people-living-values': CareerPeopleLivingValues;
      'career.stop-the-lies': CareerStopTheLies;
      'career.value-items': CareerValueItems;
      'career.values': CareerValues;
      'career.whole-gang': CareerWholeGang;
      'career.youtube-banner': CareerYoutubeBanner;
      'certificates.certificate-items': CertificatesCertificateItems;
      'certificates.certificate-manager': CertificatesCertificateManager;
      'common.announcement-bar': CommonAnnouncementBar;
      'common.cashback': CommonCashback;
      'common.flippable-card': CommonFlippableCard;
      'common.flippable-card-items': CommonFlippableCardItems;
      'common.marquee': CommonMarquee;
      'common.menu': CommonMenu;
      'common.press-logo-marquee': CommonPressLogoMarquee;
      'common.social-media': CommonSocialMedia;
      'common.titles': CommonTitles;
      'contact-us.chat-button': ContactUsChatButton;
      'contact-us.contact-info': ContactUsContactInfo;
      'contact-us.contact-info-items': ContactUsContactInfoItems;
      'contact-us.contact-us': ContactUsContactUs;
      'contact-us.contact-us-page': ContactUsContactUsPage;
      'contact-us.fa-qs': ContactUsFaQs;
      'contact-us.faq-section': ContactUsFaqSection;
      'contact-us.faq-section-items': ContactUsFaqSectionItems;
      'contact-us.working-hours': ContactUsWorkingHours;
      'elements.button': ElementsButton;
      'elements.header': ElementsHeader;
      'elements.html-editor': ElementsHtmlEditor;
      'elements.key-value': ElementsKeyValue;
      'elements.link-text': ElementsLinkText;
      'elements.title': ElementsTitle;
      'faqs.faq-items': FaqsFaqItems;
      'faqs.faq-template': FaqsFaqTemplate;
      'faqs.faqs': FaqsFaqs;
      'header.external-links': HeaderExternalLinks;
      'header.header-menu': HeaderHeaderMenu;
      'header.menu-items': HeaderMenuItems;
      'homepage.big-food-did-item': HomepageBigFoodDidItem;
      'homepage.fixing-food': HomepageFixingFood;
      'homepage.go-truth-seekers': HomepageGoTruthSeekers;
      'homepage.head-to-infinite-love': HomepageHeadToInfiniteLove;
      'homepage.press-quote-items': HomepagePressQuoteItems;
      'homepage.pyaar-secret-ingredient': HomepagePyaarSecretIngredient;
      'homepage.real-food': HomepageRealFood;
      'homepage.real-food-categories': HomepageRealFoodCategories;
      'homepage.real-food-is-flawed': HomepageRealFoodIsFlawed;
      'homepage.real-people-love': HomepageRealPeopleLove;
      'homepage.rebuilding-the-trust': HomepageRebuildingTheTrust;
      'homepage.stars-love-us': HomepageStarsLoveUs;
      'homepage.stop-the-press': HomepageStopThePress;
      'homepage.trust-building-food-items': HomepageTrustBuildingFoodItems;
      'homepage.who-broke-trust': HomepageWhoBrokeTrust;
      'layout.footer-menu': LayoutFooterMenu;
      'media.external-media-embed': MediaExternalMediaEmbed;
      'media.icon': MediaIcon;
      'media.image': MediaImage;
      'media.image-or-video': MediaImageOrVideo;
      'media.video': MediaVideo;
      'pdp-templates.certificate-banner-template': PdpTemplatesCertificateBannerTemplate;
      'pdp-templates.got-a-question-template': PdpTemplatesGotAQuestionTemplate;
      'pdp-templates.ingredient-details-template': PdpTemplatesIngredientDetailsTemplate;
      'pdp-templates.key-features-template': PdpTemplatesKeyFeaturesTemplate;
      'pdp-templates.nutritional-facts-template': PdpTemplatesNutritionalFactsTemplate;
      'pdp-templates.product-additional-description-template': PdpTemplatesProductAdditionalDescriptionTemplate;
      'pdp-templates.product-details-extra-template': PdpTemplatesProductDetailsExtraTemplate;
      'pdp-templates.quickly-added-template': PdpTemplatesQuicklyAddedTemplate;
      'pdp-templates.review-template': PdpTemplatesReviewTemplate;
      'pdp-templates.whats-inside-template': PdpTemplatesWhatsInsideTemplate;
      'pdp-templates.why-section-template': PdpTemplatesWhySectionTemplate;
      'pdp.delivery-options': PdpDeliveryOptions;
      'pdp.ingredient-details': PdpIngredientDetails;
      'pdp.key-features': PdpKeyFeatures;
      'pdp.nutritional-fact-details': PdpNutritionalFactDetails;
      'pdp.nutritional-fact-items': PdpNutritionalFactItems;
      'pdp.nutritional-fact-sub-items': PdpNutritionalFactSubItems;
      'pdp.nutritional-facts': PdpNutritionalFacts;
      'pdp.offer': PdpOffer;
      'pdp.product-additional-description': PdpProductAdditionalDescription;
      'pdp.product-details-extra': PdpProductDetailsExtra;
      'pdp.whats-inside': PdpWhatsInside;
      'pdp.whats-inside-details': PdpWhatsInsideDetails;
      'pdp.why-section': PdpWhySection;
      'plp.badaam-product-listing': PlpBadaamProductListing;
      'plp.borderless-product-listing': PlpBorderlessProductListing;
      'plp.dark-chocolate-product-listing': PlpDarkChocolateProductListing;
      'plp.flavour-ingredient-items': PlpFlavourIngredientItems;
      'plp.flavour-items': PlpFlavourItems;
      'plp.flavour-product-item': PlpFlavourProductItem;
      'plp.hashtag-comment': PlpHashtagComment;
      'plp.indulgent-flavours': PlpIndulgentFlavours;
      'plp.just-ingredient-items': PlpJustIngredientItems;
      'plp.just-ingredients': PlpJustIngredients;
      'plp.long-description': PlpLongDescription;
      'plp.milk-chocolate-product-listing': PlpMilkChocolateProductListing;
      'plp.multi-categories': PlpMultiCategories;
      'plp.multi-category-items': PlpMultiCategoryItems;
      'plp.nothing-to-hide': PlpNothingToHide;
      'plp.pick-your-protein': PlpPickYourProtein;
      'plp.pick-your-protein-items': PlpPickYourProteinItems;
      'plp.process-images': PlpProcessImages;
      'plp.protein-bar-pro-product-listing': PlpProteinBarProProductListing;
      'plp.protein-powder-product-listing': PlpProteinPowderProductListing;
      'plp.real-people-real-review-comments': PlpRealPeopleRealReviewComments;
      'plp.review-comment-items': PlpReviewCommentItems;
      'plp.sachet-banner': PlpSachetBanner;
      'plp.shop-chocolate': PlpShopChocolate;
      'plp.two-sided-product-banner': PlpTwoSidedProductBanner;
      'refer.refer-and-earn': ReferReferAndEarn;
      'refer.refer-and-earn-form': ReferReferAndEarnForm;
      'refer.refer-and-earn-thank-you-page': ReferReferAndEarnThankYouPage;
      'review-templates.real-people-reviews-template': ReviewTemplatesRealPeopleReviewsTemplate;
      'review-templates.verified-reviews-template': ReviewTemplatesVerifiedReviewsTemplate;
      'reviews.celebrity-stars': ReviewsCelebrityStars;
      'reviews.community-comment-items': ReviewsCommunityCommentItems;
      'reviews.community-post-items': ReviewsCommunityPostItems;
      'reviews.content-comment-items': ReviewsContentCommentItems;
      'reviews.content-items': ReviewsContentItems;
      'reviews.fam-comment-items': ReviewsFamCommentItems;
      'reviews.gang-items': ReviewsGangItems;
      'reviews.influencer-truth': ReviewsInfluencerTruth;
      'reviews.join-the-gang': ReviewsJoinTheGang;
      'reviews.love-for-content': ReviewsLoveForContent;
      'reviews.love-wall': ReviewsLoveWall;
      'reviews.our-community': ReviewsOurCommunity;
      'reviews.press-quote-items': ReviewsPressQuoteItems;
      'reviews.real-people-reviews': ReviewsRealPeopleReviews;
      'reviews.stars': ReviewsStars;
      'reviews.startup-fam': ReviewsStartupFam;
      'reviews.stop-the-press': ReviewsStopThePress;
      'reviews.truth-sayer-items': ReviewsTruthSayerItems;
      'reviews.truth-sayers': ReviewsTruthSayers;
      'search.search-keywords': SearchSearchKeywords;
      'search.search-upsell-collections': SearchSearchUpsellCollections;
    }
  }
}
