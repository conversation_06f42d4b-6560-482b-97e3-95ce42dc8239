"use client";
import { AboutUs } from "@/assets/icons/AboutUs";
import { Categories } from "@/assets/icons/Categories";
import { ContactUs } from "@/assets/icons/ContactUs";
import { LoveWall } from "@/assets/icons/LoveWall";
import { Learn } from "@/assets/icons/Learn";
import Link from "next/link";
import React, { useState } from "react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Shop } from "@/assets/icons/Shop";

const shopMenuItems = [
  {
    icon: LoveWall,
    label: "LOVEWALL",
    href: "/lovewall",
  },
  {
    icon: AboutUs,
    label: "ABOUT US",
    href: "/about",
  },
  {
    icon: ContactUs,
    label: "CONTACT",
    href: "/contact",
  },
  {
    icon: AboutUs,
    label: "CART",
    href: "/cart",
  },
];

const learnMenuItems = [
  {
    icon: Categories,
    label: "COURSES",
    href: "/courses",
  },
  {
    icon: LoveWall,
    label: "RESOURCES",
    href: "/resources",
  },
  {
    icon: AboutUs,
    label: "FAQ",
    href: "/faq",
  },
  {
    icon: ContactUs,
    label: "SUPPORT",
    href: "/support",
  },
];

const FooterNav = () => {
  const [activeTab, setActiveTab] = useState("shop");

  return (
    <div className="fixed bottom-0 left-0 right-0 md:hidden z-60">
      <Tabs
        defaultValue="shop"
        className="w-full gap-0"
        onValueChange={(value) => setActiveTab(value)}
      >
        <div className="px-6">
          <TabsList className="bg-pink-100 w-full grid grid-cols-2 h-10 rounded-none p-0 rounded-t-md">
            <TabsTrigger
              value="shop"
              className={`text-xs rounded-b-none data-[state=active]:bg-[#93385d] data-[state=active]:text-white font-bold h-full flex items-center justify-center gap-2 ${
                activeTab === "shop" ? "bg-[#93385d] text-white" : "bg-pink-100"
              }`}
            >
              <div>
                <Shop color={activeTab === "shop" ? "#FFFFFF" : "#000000"} />
              </div>
              SHOP
            </TabsTrigger>
            <TabsTrigger
              value="learn"
              className={`text-xs rounded-b-none !data-[state=active]:rounded-tl-none data-[state=active]:bg-[#93385d] data-[state=active]:text-white font-bold h-full flex items-center justify-center gap-2 ${
                activeTab === "learn"
                  ? "bg-[#93385d] text-white"
                  : "bg-pink-100"
              }`}
            >
              <Learn color={activeTab === "learn" ? "#FFFFFF" : "#000000"} />
              LEARN
            </TabsTrigger>
          </TabsList>
        </div>
        <TabsContent value="shop" className="m-0">
          <div className="flex justify-between bg-white py-3 px-4 border-t">
            {shopMenuItems.map((item, index) => (
              <Link
                href={item.href}
                key={index}
                className="flex items-center flex-col text-center"
              >
                <item.icon />
                <span className="mt-1 text-[#93385d] text-xs font-medium">
                  {item.label}
                </span>
              </Link>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="learn" className="m-0">
          <div className="flex justify-between bg-white py-3 px-4 border-t">
            {learnMenuItems.map((item, index) => (
              <Link
                href={item.href}
                key={index}
                className="flex items-center flex-col text-center"
              >
                <item.icon />
                <span className="mt-1 text-[#93385d] text-xs font-medium">
                  {item.label}
                </span>
              </Link>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FooterNav;
