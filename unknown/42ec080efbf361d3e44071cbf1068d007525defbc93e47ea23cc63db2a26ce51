"use client";
import React from "react";
import { ReviewStar } from "@/assets/icons/ReviewStar";
import VerifiedBadge from "@/assets/icons/VerifiedBadge";
import { Previous } from "@/assets/icons/Previous";
import { Next } from "@/assets/icons/Next";
import { cn, getStarFillPercentage } from "@/libs/utils";
import ReviewStats from "./_components/ReviewStats";
import { useReviews, type ProductReview } from "./hooks";
import { ProductDetailsType } from "@/types/Collections/ProductDetails";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
}) => {
  const getPageNumbers = () => {
    const pages: number[] = [];
    const maxVisiblePages = 5;
    let start = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const end = Math.min(start + maxVisiblePages - 1, totalPages);

    // Adjust start if we're near the end
    if (end - start + 1 < maxVisiblePages) {
      start = Math.max(1, end - maxVisiblePages + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }
    return pages;
  };

  const handlePageClick = (page: number) => {
    onPageChange(page);
  };

  const pages = getPageNumbers();

  return (
    <div className="mt-2 pb-8.5 border-b border-b-[#44426a80]">
      <div className="flex items-center justify-between">
        {currentPage > 1 && (
          <button
            className="flex items-center group cursor-pointer"
            onClick={() => handlePageClick(currentPage - 1)}
          >
            <Previous />
            <p className="font-obviously text-sm group-hover:underline">
              Previous
            </p>
          </button>
        )}
        {currentPage === 1 && <div />}

        <div className="flex gap-2">
          {pages.map((page) => (
            <button
              key={page}
              onClick={() => handlePageClick(page)}
              className={cn(
                "font-obviously w-[26px] h-[26px] text-sm font-normal leading-none rounded-full flex items-center justify-center cursor-pointer hover:underline",
                {
                  "bg-[#44426a] text-white": page === currentPage,
                  "text-pdp-primary bg-transparent": page !== currentPage,
                }
              )}
            >
              {page}
            </button>
          ))}
        </div>

        {currentPage < totalPages ? (
          <button
            className="flex items-center gap-1 group cursor-pointer"
            onClick={() => handlePageClick(currentPage + 1)}
          >
            <p className="font-obviously text-sm group-hover:underline">Next</p>
            <Next />
          </button>
        ) : (
          <div />
        )}
      </div>
    </div>
  );
};

interface VerifiedReviewsProps {
  productId?: string;
  productData: ProductDetailsType;
}

export const VerifiedReviews: React.FC<VerifiedReviewsProps> = ({
  productId = "prod_01JWB88PWETQJBSPG29JAZZAGE",
  productData,
}) => {
  const {
    data: reviews,
    currentPage,
    totalPages,
    isLoading,
    isError,
    goToPage,
  } = useReviews({
    productId,
    limit: 5,
    enabled: !!productId,
  });

  console.log(`reviews`, reviews);

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col gap-4 max-w-[840px] mx-auto px-6 lg:mb-14">
        <h2 className="text-[38px] leading-10.5 font-semibold font-narrow text-black text-center mb-8 md:mb-16">
          Verified Reviews
        </h2>
        <ReviewStats productId={productId} />
        <div className="flex flex-col">
          {Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="pb-6 mb-6 space-y-1.5 border-b border-b-[#44426a80] animate-pulse"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="h-8 w-16 bg-gray-200 rounded"></div>
                  <div className="h-6 w-6 bg-gray-200 rounded"></div>
                </div>
                <div className="h-6 w-20 bg-gray-200 rounded"></div>
              </div>
              <div className="flex items-center gap-1">
                {Array.from({ length: 5 }).map((_, starIndex) => (
                  <div
                    key={starIndex}
                    className="h-4 w-4 bg-gray-200 rounded"
                  ></div>
                ))}
              </div>
              <div className="h-16 w-full bg-gray-200 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  // Show error state
  if (isError) {
    return (
      <div className="flex flex-col gap-4 max-w-[840px] mx-auto px-6 lg:mb-14">
        <h2 className="text-[38px] leading-10.5 font-semibold font-narrow text-black text-center mb-8 md:mb-16">
          Verified Reviews
        </h2>
        <ReviewStats productId={productId} />
        <div className="text-center py-8">
          <p className="text-lg text-gray-600">
            Unable to load reviews at this time. Please try again later.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4 max-w-[840px] mx-auto px-6 lg:mb-14">
      <h2 className="text-[38px] leading-10.5 font-semibold font-narrow text-black text-center mb-8 md:mb-16">
        Verified Reviews
      </h2>

      <ReviewStats
        productId={productId}
        primaryColor={productData.primary_color}
      />

      <div className="flex flex-col">
        {reviews.length > 0 ? (
          reviews.map((review: ProductReview) => (
            <div
              key={review.id}
              className="pb-6 mb-6 space-y-1.5 border-b border-b-[#44426a80]"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <p className="capitalize text-[#1a181e] font-narrow text-3xl font-semibold leading-12">
                    {review.name}
                  </p>
                  {review.is_admin_verified && (
                    <VerifiedBadge className="mt-1" />
                  )}
                </div>
                <p className="text-lg leading-7 font-obviously text-[#00000080]">
                  {formatDate(review.created_at)}
                </p>
              </div>

              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <ReviewStar
                    key={star}
                    color="#FFC83A"
                    fillPercentage={getStarFillPercentage(
                      review.rating,
                      star - 1
                    )}
                  />
                ))}
              </div>

              <p className="mt-1 whitespace-pre-line break-words text-sm leading-5 font-obviously text-[#1a181e] font-normal">
                {review.description}
              </p>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <p className="text-lg text-gray-600">
              No reviews available for this product yet.
            </p>
          </div>
        )}
      </div>

      {totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={goToPage}
        />
      )}

      {/* Write a review button */}
      <button className="mb-4 h-10.5 text-center uppercase text-[#44426a] border-[#44426a] border w-full cursor-pointer rounded-[6px] px-5 py-2 font-obviously font-semibold text-[16px]">
        Write a review
      </button>
    </div>
  );
};

export default VerifiedReviews;
