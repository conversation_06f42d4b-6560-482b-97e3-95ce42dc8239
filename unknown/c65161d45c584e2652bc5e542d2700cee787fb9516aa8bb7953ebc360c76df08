// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface ProteinBarProProductListing {
  title?: string;
  category?: { data: Category };
  bg_color?: any;
}
export interface ProteinBarProProductListing_Plain {
  title?: string;
  category?: Category_Plain;
  bg_color?: any;
}

export interface ProteinBarProProductListing_NoRelations {
  title?: string;
  category?: number;
  bg_color?: any;
}

