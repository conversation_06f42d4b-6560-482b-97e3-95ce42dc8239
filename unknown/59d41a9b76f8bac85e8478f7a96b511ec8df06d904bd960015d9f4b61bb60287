// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface ProteinPowderProductListing {
  bg_color?: any;
  title?: string;
  category?: { data: Category };
}
export interface ProteinPowderProductListing_Plain {
  bg_color?: any;
  title?: string;
  category?: Category_Plain;
}

export interface ProteinPowderProductListing_NoRelations {
  bg_color?: any;
  title?: string;
  category?: number;
}

