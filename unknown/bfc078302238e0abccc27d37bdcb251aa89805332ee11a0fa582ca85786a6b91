"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import MobileMenu from "./MobileMenu";
import MegaMenuComponent from "./MegaMenu";
import UserMenu from "./UserMenu";
import { MobileMenuIcon } from "@/assets/icons/MobileMenu";
import { CloseIcon } from "@/assets/icons/Close";
// import UserIcon from "@/assets/icons/User";
import SearchIcon from "@/assets/icons/Search";
import CartCountWrapper from "@/assets/icons/CartCountWrapper";
import { HeaderType } from "@/types/Collections/Header";
import { HeaderMenuType } from "@/types/Components/Header/HeaderMenu";
import { MenuType } from "@/types/Components/Common/Menu";

const HeaderClient = ({ headerData }: { headerData?: HeaderType | null }) => {
  const transformMenuData = (
    headerMenu: HeaderMenuType[] | undefined
  ): MenuType[] => {
    if (!headerMenu || !Array.isArray(headerMenu)) return [];

    return headerMenu.map((menuItem) => {
      const transformedItem: MenuType = {
        title: menuItem.title,
        action_link: menuItem.action_link,
        show: true,
        tag: menuItem.tag ? { title: menuItem.tag } : undefined,
      };

      // Transform menu_items to sub_menu if they exist
      if (menuItem.menu_items && menuItem.menu_items.length > 0) {
        transformedItem.sub_menu = menuItem.menu_items.map((subItem: any) => {
          // Handle different image structures
          let imageUrl = "";
          if (subItem.image) {
            if (subItem.image.web && subItem.image.web.url) {
              // New structure: image.web.url
              imageUrl = subItem.image.web.url;
            } else if (subItem.image.url) {
              // Old structure: image.url
              imageUrl = subItem.image.url;
            } else if (typeof subItem.image === "string") {
              // Direct string URL
              imageUrl = subItem.image;
            }
          }

          return {
            title: subItem.title || "",
            action_link: subItem.action_link,
            show: true,
            image: imageUrl,
          };
        });
      }

      return transformedItem;
    });
  };

  const menuData = headerData ? transformMenuData(headerData.menu) : [];
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen((prevState) => !prevState);
  };

  const handleMobileMenuClose = () => {
    setIsMobileMenuOpen(false);
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement;
      if (isMobileMenuOpen && !target.closest("[data-mobile-menu]")) {
        handleMobileMenuClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isMobileMenuOpen) {
        handleMobileMenuClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isMobileMenuOpen]);

  return (
    <>
      <header
        id="main-header"
        className="sticky top-0 z-[60] bg-white shadow-sm"
      >
        <div className="px-6 flex items-center justify-between py-3 md:grid md:grid-cols-3 md:items-center">
          {/* Mobile: Left (Menu/Close Icon) */}
          <div className="flex items-center md:hidden flex-1 relative z-[70]">
            <div
              className="p-0 [:_svg]:size-10 cursor-pointer"
              onClick={toggleMobileMenu}
              aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMobileMenuOpen}
              data-mobile-menu
            >
              {isMobileMenuOpen ? <CloseIcon /> : <MobileMenuIcon />}
            </div>
          </div>

          {/* Logo (centered on mobile, left on desktop) */}
          <div className="flex-1 flex justify-center md:justify-start">
            <Link href="/" className="flex-shrink-0">
              <Image
                src="/images/logo.svg"
                alt="Whole The Truth"
                width={102}
                height={54}
                className="md:w-[102px] md:h-[54px] w-auto h-[40px]"
                priority
              />
            </Link>
          </div>

          {/* Mobile: Right (Search/Cart) */}
          <div className="flex items-center gap-5 flex-1 justify-end md:hidden">
            <SearchIcon color="#93385D" />

            <div className="relative">
              <CartCountWrapper color="#93385D" />
              <span className="text-[#93385D] md:text-black absolute top-[48%] left-[48%] -translate-x-1/2 -translate-y-1/2 text-obviously text-xs">
                1
              </span>
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center justify-center col-span-1 w-full">
            {menuData.length > 0 && <MegaMenuComponent menuData={menuData} />}
          </div>

          {/* Desktop: Right Icons */}
          <div className="hidden md:flex items-center justify-end col-span-1 gap-5">
            <UserMenu />
            <SearchIcon />
            <div className="relative">
              <CartCountWrapper />
              <span className="absolute top-[48%] left-[48%] -translate-x-1/2 -translate-y-1/2 text-obviously text-xs">
                1
              </span>
            </div>
          </div>
        </div>
        {/* Mobile Menu */}
        <MobileMenu
          onClose={handleMobileMenuClose}
          isOpen={isMobileMenuOpen}
          menuData={menuData}
        />
      </header>
    </>
  );
};

export default HeaderClient;
