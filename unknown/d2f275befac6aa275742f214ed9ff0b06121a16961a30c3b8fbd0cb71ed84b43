// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { BannerCarousel } from '../../banner/interfaces/BannerCarousel';
import { Image_Plain } from '../../media/interfaces/Image';
import { BannerCarousel_Plain } from '../../banner/interfaces/BannerCarousel';
import { Image_NoRelations } from '../../media/interfaces/Image';
import { BannerCarousel_NoRelations } from '../../banner/interfaces/BannerCarousel';

export interface WhySection {
  primary_image?: Image;
  description?: string;
  title?: string;
  image_carousels?: BannerCarousel;
  show_component: boolean;
}
export interface WhySection_Plain {
  primary_image?: Image_Plain;
  description?: string;
  title?: string;
  image_carousels?: BannerCarousel_Plain;
  show_component: boolean;
}

export interface WhySection_NoRelations {
  primary_image?: Image_NoRelations;
  description?: string;
  title?: string;
  image_carousels?: BannerCarousel_NoRelations;
  show_component: boolean;
}

