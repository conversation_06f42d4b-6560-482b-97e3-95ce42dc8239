import React from "react";
import { cn } from "@/libs/utils";
import IngredientCard from "../IngredientCard";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";

interface IngredientBreakdownProps {
  ingredients: {
    type: string;
    percentage: number;
    image: string;
  }[];
  className?: string;
  containerClassName?: string;
  gapClassName?: string;
  cardClassName?: string;
  hideScrollbar?: boolean;
}

const IngredientBreakdown: React.FC<IngredientBreakdownProps> = ({
  ingredients,
  className,
  containerClassName,
  gapClassName = "gap-[13.5px]",
  cardClassName,
  hideScrollbar = true,
}) => {
  return (
    <div className={cn("mb-8 w-full max-w-[800px] mx-auto", className)}>
      <UniversalCarousel
        useNativeScrollbar={true}
        hideScrollbar={hideScrollbar}
        className={cn("w-full", containerClassName)}
        gapClassName={gapClassName}
      >
        {ingredients.map((ingredient) => (
          <IngredientCard
            key={ingredient.type}
            type={ingredient.type}
            percentage={ingredient.percentage}
            image={ingredient.image}
            className={cn("mx-1", cardClassName)}
          />
        ))}
      </UniversalCarousel>
    </div>
  );
};

export default IngredientBreakdown;
