// Interface automatically generated by schemas-to-ts

import { PeopleLivingValueItems } from './PeopleLivingValueItems';
import { PeopleLivingValueItems_Plain } from './PeopleLivingValueItems';
import { PeopleLivingValueItems_NoRelations } from './PeopleLivingValueItems';

export interface PeopleLivingValues {
  bg_color?: any;
  people_value_items: PeopleLivingValueItems[];
  title?: any;
}
export interface PeopleLivingValues_Plain {
  bg_color?: any;
  people_value_items: PeopleLivingValueItems_Plain[];
  title?: any;
}

export interface PeopleLivingValues_NoRelations {
  bg_color?: any;
  people_value_items: PeopleLivingValueItems_NoRelations[];
  title?: any;
}

