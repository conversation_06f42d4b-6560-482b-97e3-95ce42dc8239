interface IconProps extends React.SVGProps<SVGSVGElement> {
  color?: string;
}

export function Learn({ color = "#FFFFFF", ...props }: IconProps) {
  return (
    <svg
      {...props}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.6165 4.77409C10.7585 4.98494 10.9452 5.23029 11.0811 5.40017C11.1257 5.45585 11.1527 5.52349 11.1587 5.59454L11.9315 14.6744C12.0566 14.6163 12.1947 14.5618 12.3387 14.5106C12.8691 14.3219 13.5726 14.145 14.2589 13.9828C14.4594 13.9355 14.6582 13.8894 14.852 13.8444C15.3313 13.7334 15.7792 13.6295 16.1428 13.5315C16.399 13.4625 16.5955 13.4009 16.7234 13.3474C16.7292 13.3449 16.7348 13.3426 16.74 13.3403V3.51532C16.5757 3.53803 16.3609 3.57383 16.1064 3.62024C15.4757 3.73523 14.6294 3.91004 13.7719 4.09278C13.1489 4.22555 12.5226 4.36195 11.9713 4.48201C11.7643 4.5271 11.5678 4.56988 11.3861 4.6093C11.0819 4.67528 10.8185 4.73197 10.6165 4.77409ZM16.824 13.2975C16.8235 13.2979 16.8227 13.2984 16.8218 13.2991C16.8288 13.2929 16.831 13.2924 16.824 13.2975ZM10.4505 5.76356C10.3044 5.57728 10.1212 5.33234 9.98149 5.1194C9.90316 4.99999 9.82488 4.87032 9.77953 4.76033C9.76165 4.71697 9.72323 4.61909 9.73088 4.50825C9.7353 4.44418 9.75805 4.33849 9.8488 4.25034C9.93342 4.16814 10.0326 4.14427 10.1043 4.14059C10.1059 4.14036 10.1079 4.14007 10.1102 4.13972C10.1242 4.13757 10.1454 4.13393 10.1747 4.12848C10.2328 4.11768 10.3141 4.10145 10.4164 4.08029C10.6206 4.03805 10.9012 3.97775 11.2335 3.90567C11.4142 3.86646 11.61 3.82382 11.8166 3.77883C12.3683 3.65868 12.9971 3.52173 13.6218 3.38859C14.48 3.20571 15.3353 3.02896 15.9772 2.91192C16.2969 2.85363 16.5709 2.80882 16.7679 2.78589C16.8637 2.77473 16.9558 2.76696 17.0301 2.7682C17.0629 2.76874 17.1198 2.77108 17.1787 2.78776C17.207 2.79576 17.2652 2.81493 17.3228 2.86196C17.3903 2.9171 17.46 3.01562 17.46 3.15006V13.5C17.46 13.7135 17.3057 13.8368 17.2517 13.8767C17.1769 13.932 17.0865 13.9759 17.0014 14.0115C16.8269 14.0846 16.5917 14.1562 16.3302 14.2267C15.9535 14.3283 15.4855 14.4367 15.0004 14.5492C14.8096 14.5934 14.6161 14.6383 14.4244 14.6835C13.7357 14.8463 13.0683 15.0152 12.58 15.1889C12.3333 15.2767 12.1546 15.3582 12.0458 15.4292C12.0262 15.4419 12.0107 15.4532 11.9986 15.4629L12.0587 16.1695C12.0673 16.27 12.0333 16.3694 11.9651 16.4436C11.897 16.5178 11.8008 16.5601 11.7 16.5601H7.20001C7.00118 16.5601 6.84 16.3989 6.84 16.2001V15.4968C6.83832 15.4962 6.83661 15.4955 6.83488 15.4949C6.72233 15.4529 6.55642 15.4054 6.34123 15.3543C5.91351 15.2527 5.33191 15.146 4.68494 15.0424C3.39324 14.8355 1.8694 14.645 0.860249 14.5329C0.677933 14.5126 0.540004 14.3585 0.540004 14.1751C0.540004 12.5611 0.390205 6.46244 0.315129 3.60953C0.310405 3.43002 0.438691 3.27448 0.615821 3.24496C0.93521 3.19173 1.4494 3.20677 2.03446 3.25314C2.63255 3.30054 3.34457 3.3845 4.08187 3.48525C5.55707 3.68684 7.14912 3.95785 8.16556 4.14608C8.29701 4.17042 8.40415 4.26561 8.4438 4.39328C8.48346 4.52095 8.4491 4.66009 8.35456 4.75462L7.56001 5.54918V15.8401H11.3081L10.4505 5.76356ZM6.94716 15.5461C6.94698 15.5461 6.94476 15.5448 6.94108 15.5419C6.9455 15.5446 6.94733 15.546 6.94716 15.5461ZM6.84 14.7402C6.73844 14.7109 6.62672 14.6821 6.50769 14.6538C6.05369 14.5459 5.45123 14.436 4.79882 14.3315C3.62216 14.143 2.25982 13.9686 1.25857 13.8533C1.24369 11.9414 1.1165 6.72313 1.04382 3.93202C1.27866 3.92747 1.59435 3.94052 1.97758 3.97089C2.55652 4.01677 3.25357 4.09875 3.98439 4.19862C5.15029 4.35795 6.39222 4.56142 7.35891 4.73204L6.94545 5.1455C6.87793 5.21302 6.84 5.30458 6.84 5.40006V14.7402ZM10.4429 4.48026C10.4412 4.47569 10.4406 4.47358 10.4408 4.47366C10.4409 4.47375 10.4418 4.47604 10.4429 4.48026Z"
        fill={color}
      ></path>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.542 2.69081C9.91553 3.31378 9.51457 4.0947 9.34155 4.61376C9.2955 4.75189 9.17078 4.84869 9.02554 4.85901C8.8803 4.86933 8.74314 4.79115 8.67803 4.66091C8.47199 4.24884 8.03887 3.61323 7.42621 3.08508C6.81479 2.55799 6.05133 2.15991 5.17502 2.15991C3.3519 2.15991 2.37079 3.26337 2.12201 3.76091L1.47803 3.43892C1.82926 2.73646 3.03814 1.43991 5.17502 1.43991C6.27872 1.43991 7.20276 1.94184 7.89633 2.53975C8.32727 2.91125 8.67958 3.32829 8.94301 3.70792C9.19076 3.2124 9.55316 2.65873 10.0343 2.18027C10.7598 1.45886 11.7759 0.890838 13.0776 0.990974C15.5142 1.1784 16.6899 2.32916 16.9809 3.00816L16.3191 3.29177C16.1601 2.92074 15.2659 1.88143 13.0224 1.70885C11.9842 1.62899 11.1628 2.07347 10.542 2.69081Z"
        fill={color}
      ></path>
    </svg>
  );
}
