"use client";
import { Love } from "@/assets/icons/Love";
import ReviewGrid from "@/components/blocks/ReviewGrid";
import PaymentMethodsBar from "@/components/Common/PaymentMethodsBar";
import FlipableCardsSection from "@/components/Sections/FlipableCardsSection";
import ProductCarouselSection from "@/components/Sections/ProductCarouselSection";
import { Product } from "@/components/Common/ProductCard";
import SeactionHeading from "@/components/Common/Section/Heading";
import SubHeading from "@/components/Common/Section/SubHeading";
import SingleBanner from "@/components/Common/SingleBanner";
import TwoBannerSection from "@/components/Common/TwoBannerSection";
import { customerReviews } from "@/data/reviews";
import Image from "next/image";
import React from "react";
import TryBanner from "@/components/Common/TryBanner";
import Link from "next/link";
import ChooseProductSection from "@/components/Sections/ChooseProductSection";

const page = () => {
  const cards = [
    {
      id: 1,
      frontImage: "/images/reviews/review_1.png",
      backImage: "/images/reviews/review_1_message.png",
      frontAlt: "Card 1 Front",
      backAlt: "Card 1 Back",
    },
    {
      id: 2,
      frontImage: "/images/reviews/review_2.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 2 Front",
      backAlt: "Card 2 Back",
    },
    {
      id: 3,
      frontImage: "/images/reviews/review_3.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 3 Front",
      backAlt: "Card 3 Back",
    },
    {
      id: 4,
      frontImage: "/images/reviews/review_4.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 4 Front",
      backAlt: "Card 4 Back",
    },
    {
      id: 5,
      frontImage: "/images/reviews/review_5.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 5 Front",
      backAlt: "Card 5 Back",
    },
    {
      id: 6,
      frontImage: "/images/reviews/review_card_2.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 6 Front",
      backAlt: "Card 6 Back",
    },
    {
      id: 7,
      frontImage: "/images/reviews/review_card_3.png",
      backImage: "/images/reviews/review_1_message.png", // Changed to a different back image
      frontAlt: "Card 7 Front",
      backAlt: "Card 7 Back",
    },
  ];
  const accentCardProducts: Product[] = [
    {
      id: "1",
      title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
      image: "/images/products/double_cocoa.png",
      price: 4777,
      originalPrice: 4499, // 1% discount
      isStartingPrice: false,
      rating: 2.5,
      weight: "8 x 27 g",
      primaryColor: "#00693B", // matches double cocoa
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      variants: [
        {
          id: "1-1",
          size: "pack of 1",
          weight: "250g",
          price: 4777,
          originalPrice: 4499, // ~6% discount
          image: "/images/products/protein_fop.png",
        },
        {
          id: "1-2",
          size: "500g",
          weight: "500g",
          price: 2799,
          originalPrice: 2999, // ~7% discount
          image: "/images/products/double_cocoa.png",
        },
      ],
    },
    {
      id: "2",
      title: "Everyone Party - Pack of 16 Mini Protein Bars",
      image: "/images/products/protein_2.webp",
      price: 960,
      isStartingPrice: false,
      rating: 4.1,
      weight: "16 x 27 g",
      primaryColor: "#44426a",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "3",
      title: "Personalised Box - Pack of 24 Mini Protein Bars",
      image: "/images/products/protein_3.png",
      price: 1440,
      isStartingPrice: true,
      rating: 4.7,
      weight: "24 x 27 g",
      primaryColor: "#DC8A20",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "4",
      title: "Personalised Box - Pack of 48 Mini Protein Bars",
      image: "/images/products/double_cocoa.png",
      price: 2880,
      isStartingPrice: true,
      rating: 4.2,
      weight: "48 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "5",
      title: "Double Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 4.5,
      weight: "12 x 27 g",
      primaryColor: "#93385D",
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "6",
      title: "Cranberry Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      originalPrice: 800, // 10% discount
      isStartingPrice: false,
      rating: 2.4,
      weight: "12 x 27 g",
      primaryColor: "#D23C47", // red tone
      enableProductBg: true, // Enable background color
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
      variants: [
        {
          id: "6-1",
          size: "1 x 350g",
          weight: "350g",
          price: 720,
          originalPrice: 800, // 10% discount
          image: "/images/products/double_cocoa.png",
        },
        {
          id: "6-2",
          size: "2 x 350g",
          weight: "700g",
          price: 1350,
          originalPrice: 1500, // 10% discount
          image: "/images/products/double_cocoa.png",
        },
        {
          id: "6-3",
          size: "3 x 350g",
          weight: "1050g",
          price: 1950,
          originalPrice: 2200, // ~11% discount
          image: "/images/products/double_cocoa.png",
        },
      ],
    },
    {
      id: "7",
      title: "Peanut Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#F05C1D", // orange tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
    {
      id: "8",
      title: "Coffee Cocoa Mini Protein Bars - Box of 12",
      image: "/images/products/double_cocoa.png",
      price: 720,
      isStartingPrice: false,
      rating: 5,
      weight: "12 x 27 g",
      primaryColor: "#59382B", // brown tone
      deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
      sameDayDelivery: true,
    },
  ];

  const bannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/heroes/beginner_hero_mobile.png",
      mime: "image/png",
    },
    web_image: {
      alternativeText: null,
      url: "/images/heroes/beginner_hero_desktop.png",
      mime: "image/png",
    },
  };

  const tryBannerImage = {
    id: "16",
    mweb_image: {
      alternativeText: null,
      url: "/images/banners/need_more_mobile.webp",
      mime: "image/webp",
    },
    web_image: {
      alternativeText: null,
      url: "/images/banners/need_more.webp",
      mime: "image/webp",
    },
  };
  return (
    <>
      <SingleBanner image={bannerImage} />

      <div className="bg-[#762D4A] py-5">
        <div className="max-w-6xl mx-auto px-4">
          <SeactionHeading title="Dear lover of movement" color="#FFFFFF" />
          <SubHeading title="you need protein. we've got you :)" />
          <div className="flex justify-center mt-5 w-full">
            <Image
              src={"/images/banners/beginner_desktop.webp"}
              alt="banner"
              width={1200}
              height={595}
              priority
              className="hidden lg:block"
            />
            <Image
              src={"/images/banners/beginner_mobile.webp"}
              alt="banner"
              width={375}
              height={300}
              priority
              className="block lg:hidden"
            />
          </div>
        </div>
      </div>

      <div className="py-8 min-h-[500px] bg-[url('/images/backgrounds/protein_bg2.png')] bg-cover bg-center bg-no-repeat">
        {/* Content with relative positioning to appear above the background */}
        <div className="relative z-10">
          <div className="max-w-6xl mx-auto px-4">
            <div className="space-y-5">
              <SeactionHeading title="Dear lover of movement" color="#000000" />
              <SubHeading
                title="Movement of any kind needs muscle. Needs protein. But most protein drinks are so full of chemicals that you don't even feel like trying. We hear you. And we made this for you."
                color="#67263E"
                className="font-obviously md:text-xl text-base font-[410]"
              />
            </div>

            <div className="relative max-w-[500px] mx-auto top-14">
              <Image
                src={"/images/banners/benefits.png"}
                alt="banner"
                width={500}
                height={242}
                priority
                className="hidden lg:block w-full h-auto"
              />
            </div>
            <div className="block lg:hidden top-18 relative">
              <Image
                src={"/images/banners/benefits.png"}
                alt="banner"
                width={375}
                height={160}
                priority
                className="w-full h-auto mx-auto"
              />
            </div>
          </div>
        </div>
      </div>

      <ProductCarouselSection
        title=" 15g Protein Range"
        products={accentCardProducts}
        backgroundColor="#762D4A"
        titleColor="#FFFFFF"
        hideScrollbar={false}
        enableProductBg={true}
        scrollbarColor="#FFFFFF"
        scrollBarTrackColor="#D498B0"
      />

      <ProductCarouselSection
        title=" 24g-30g Protein Range"
        products={accentCardProducts}
        backgroundColor="#762D4A"
        titleColor="#FFFFFF"
        hideScrollbar={false}
        enableProductBg={true}
        className="relative"
        scrollbarColor="#FFFFFF"
        scrollBarTrackColor="#D498B0"
      />

      <FlipableCardsSection
        title="Real People. Real Reviews."
        titleColor="#1a181e"
        backgroundColor="#efd8e0"
        backgroundImage="/images/banners/protein_review_banner.png"
        cards={cards.slice(0, 4)}
        icon={<Love />}
        iconColor="#E33F52"
        iconClassName="w-9 h-9.5"
        hideScrollbar={false}
        scrollbarColor="#6f1d46"
        gapClassName="gap-4"
        cardClassName="h-[500px] w-[250px]"
      />

      <div className="bg-[#762D4A] py-8">
        <div className="container mx-auto">
          <SeactionHeading
            title="Real People. Real Reviews."
            color="white"
            icon={<Love />}
            iconClassName="w-9 h-9.5"
          />
          <div className="mt-7.5">
            <ReviewGrid
              reviews={customerReviews}
              useCarousel={true}
              hideScrollbar={true}
            />
          </div>
        </div>
      </div>

      {/* <div className="w-full h-[400px] relative">
        <Image
          src="/images/banners/taste_protein_desktop.webp"
          alt="Getting started with protein"
          fill
          style={{ objectFit: "fill" }}
          priority
          className="hidden lg:block"
        />
        <Image
          src="/images/banners/taste_protein_mobile.webp"
          alt="landing page mobile"
          fill
          style={{ objectFit: "contain" }}
          priority
          className="block lg:hidden"
        />
      </div> */}

      <div className="w-full relative">
        <Link href={"/products/protein-bars"}>
          <div className="aspect-[16/5] relative hidden lg:block">
            <Image
              src="/images/banners/taste_protein_desktop.webp"
              alt="landing page desktop"
              fill
              style={{ objectFit: "cover" }}
              priority
            />
          </div>
          <div className="aspect-[1/1] relative block lg:hidden">
            <Image
              src="/images/banners/taste_protein_mobile.webp"
              alt="landing page mobile"
              fill
              style={{ objectFit: "fill" }}
              priority
            />
          </div>
        </Link>
      </div>

      {/* Product Selection */}
      <ChooseProductSection
        phrase="Pick your"
        erased="poison"
        visible="protein"
        bgColor="#772D4A"
        actionUrl="/products/protein-powder"
        firstProduct={{
          mweb_image: {
            alternativeText: "Choose your product mobile 1",
            url: "/images/banners/choose_your_product_mobile_1.png",
            mime: "image/png",
          },
          web_image: {
            alternativeText: "Choose your product desktop 1",
            url: "/images/banners/choose_your_product_1.png",
            mime: "image/png",
          },
          actionUrl: "/products/protein-powder-15g",
        }}
        secondProduct={{
          mweb_image: {
            alternativeText: "Choose your product mobile 2",
            url: "/images/banners/choose_your_product_mobile_2.png",
            mime: "image/png",
          },
          web_image: {
            alternativeText: "Choose your product desktop 2",
            url: "/images/banners/choose_your_product_2.png",
            mime: "image/png",
          },
          actionUrl: "/products/protein-powder-24g",
        }}
      />

      <TwoBannerSection
        banners={[
          {
            id: "1",
            image: "/images/banners/two_banner_one.png",
            alt: "Protein Powder Banner",
            href: "/categories/protein-powder",
          },
          {
            id: "2",
            image: "/images/banners/two_banner_two.png",
            alt: "Protein Bars Banner",
            href: "/categories/protein-bars",
          },
        ]}
      />

      <TryBanner image={tryBannerImage} href="/products/protein-bars" />

      <PaymentMethodsBar
        paymentMethods={[
          { id: "paytm", name: "Paytm", imageSrc: "/images/payment/paytm.png" },
          { id: "visa", name: "Visa", imageSrc: "/images/payment/visa.png" },
          {
            id: "mastercard",
            name: "Mastercard",
            imageSrc: "/images/payment/mastercard.png",
          },
          {
            id: "maestro",
            name: "Maestro",
            imageSrc: "/images/payment/cred.png",
          },
          {
            id: "american-express",
            name: "American Express",
            imageSrc: "/images/payment/gpay.png",
          },
          {
            id: "phonepe",
            name: "PhonePe",
            imageSrc: "/images/payment/phonepe.png",
          },
        ]}
      />
    </>
  );
};

export default page;
