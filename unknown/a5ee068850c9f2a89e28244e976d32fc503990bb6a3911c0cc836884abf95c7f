"use client";

import React from "react";

/**
 * ProductShortDescription Component
 * 
 * Displays the product's short description with proper styling.
 * Uses the gooddog font family for decorative text display.
 * 
 * @param description - The short description text to display
 */
interface ProductShortDescriptionProps {
  description?: string;
}

const ProductShortDescription: React.FC<ProductShortDescriptionProps> = ({
  description,
}) => {
  // Early return if no description provided
  if (!description) {
    return null;
  }

  return (
    <p className="font-gooddog text-2xl leading-8 text-[#1a181e] break-words">
      {description}
    </p>
  );
};

export default ProductShortDescription;
