/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { ChevronDown } from "lucide-react";
import * as React from "react";

import * as NavigationMenuPrimitive from "@radix-ui/react-navigation-menu";
import { cn } from "@/libs/utils";

const MegaMenuContext = React.createContext({
  activeItemId: null as string | null,
  setActiveItemId: (id: string | null) => {},
});

const MegaMenu = React.forwardRef<
  React.ComponentRef<typeof NavigationMenuPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root> & {
    viewportClassName?: string;
    viewportContainerClassName?: string;
  }
>(
  (
    {
      className,
      viewportClassName,
      viewportContainerClassName,
      children,
      ...props
    },
    ref
  ) => {
    const [activeItemId, setActiveItemId] = React.useState<string | null>(null);

    return (
      <MegaMenuContext.Provider value={{ activeItemId, setActiveItemId }}>
        <NavigationMenuPrimitive.Root
          ref={ref}
          className={cn("relative", className)}
          {...props}
        >
          {children}
          <MegaMenuViewport
            className={cn("", viewportClassName)}
            containerClassName={cn("", viewportContainerClassName)}
          />
        </NavigationMenuPrimitive.Root>
      </MegaMenuContext.Provider>
    );
  }
);
MegaMenu.displayName = NavigationMenuPrimitive.Root.displayName;

const useMegaMenu = () => React.useContext(MegaMenuContext);

const MegaMenuList = React.forwardRef<
  React.ComponentRef<typeof NavigationMenuPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>
>(({ className, ...props }, ref) => (
  <NavigationMenuPrimitive.List
    ref={ref}
    className={cn(
      "group flex flex-1 list-none items-center justify-center space-x-1",
      className
    )}
    {...props}
  />
));
MegaMenuList.displayName = NavigationMenuPrimitive.List.displayName;

const MegaMenuItem = NavigationMenuPrimitive.Item;

const MegaMenuTrigger = React.forwardRef<
  React.ComponentRef<typeof NavigationMenuPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger> & {
    icon?: React.ReactNode;
    itemId?: string;
  }
>(({ className, children, icon, itemId, ...props }, ref) => {
  const { activeItemId, setActiveItemId } = useMegaMenu();

  const handleClick = (e: React.MouseEvent) => {
    // If clicked on a link inside trigger, close menu
    if ((e.target as HTMLElement).closest("a")) {
      setActiveItemId(null);
    }
  };

  const handlePointerEnter = () => {
    if (itemId) {
      setActiveItemId(itemId);
    }
  };

  return (
    <NavigationMenuPrimitive.Trigger
      ref={ref}
      className={cn(
        "group flex flex-1 items-center gap-x-1 transition-all",
        className
      )}
      onClick={handleClick}
      onPointerEnter={handlePointerEnter}
      {...props}
    >
      {children}
      <div className="transition-transform duration-300 group-data-[state=open]:rotate-180">
        {icon ? (
          icon
        ) : (
          <ChevronDown className="h-4 w-4 shrink-0" aria-hidden="true" />
        )}
      </div>
    </NavigationMenuPrimitive.Trigger>
  );
});
MegaMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName;

const MegaMenuContent = React.forwardRef<
  React.ComponentRef<typeof NavigationMenuPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content> & {
    itemId?: string;
  }
>(({ className, children, itemId, ...props }, ref) => {
  const { activeItemId, setActiveItemId } = useMegaMenu();

  const handleClick = (e: React.MouseEvent) => {
    if ((e.target as HTMLElement).closest("a")) {
      setActiveItemId(null);
    }
  };

  return (
    <NavigationMenuPrimitive.Content
      ref={ref}
      className={cn(
        "data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52",
        "data-[active=true]:flex data-[active=false]:hidden",
        className
      )}
      onClick={handleClick}
      data-active={itemId === activeItemId}
      {...props}
    >
      {children}
    </NavigationMenuPrimitive.Content>
  );
});
MegaMenuContent.displayName = NavigationMenuPrimitive.Content.displayName;

const MegaMenuLink = NavigationMenuPrimitive.Link;

const MegaMenuViewport = React.forwardRef<
  React.ComponentRef<typeof NavigationMenuPrimitive.Viewport>,
  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport> & {
    containerClassName?: string;
  }
>(({ className, containerClassName, ...props }, ref) => (
  <div
    className={cn(
      "absolute left-0 right-0 top-full flex justify-center shadow",
      containerClassName
    )}
  >
    <NavigationMenuPrimitive.Viewport
      className={cn(
        "origin-top-center relative z-50 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden bg-popover text-popover-foreground data-[state=closed]:duration-200 data-[state=open]:duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        className
      )}
      ref={ref}
      {...props}
    />
  </div>
));
MegaMenuViewport.displayName = NavigationMenuPrimitive.Viewport.displayName;

const megaMenuTriggerStyle =
  "group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50";

export {
  MegaMenu,
  MegaMenuContent,
  MegaMenuItem,
  MegaMenuLink,
  MegaMenuList,
  MegaMenuTrigger,
  megaMenuTriggerStyle,
  MegaMenuViewport,
};
