// Interface automatically generated by schemas-to-ts

import { ValueItems } from './ValueItems';
import { ValueItems_Plain } from './ValueItems';
import { ValueItems_NoRelations } from './ValueItems';

export interface Values {
  bg_color?: any;
  title?: any;
  value_items: ValueItems[];
}
export interface Values_Plain {
  bg_color?: any;
  title?: any;
  value_items: ValueItems_Plain[];
}

export interface Values_NoRelations {
  bg_color?: any;
  title?: any;
  value_items: ValueItems_NoRelations[];
}

