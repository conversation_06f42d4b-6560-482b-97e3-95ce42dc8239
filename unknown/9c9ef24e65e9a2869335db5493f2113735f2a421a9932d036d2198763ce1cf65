interface ReviewStarProps {
  color?: string;
  fillPercentage?: number;
  className?: string;
}

export const ReviewStar = ({
  color = "#D9D9D9",
  fillPercentage = 100,
  className = "",
}: ReviewStarProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      fill="none"
      className={className}
      viewBox="0 0 24 24"
    >
      <defs>
        <linearGradient
          id={`starFill-${fillPercentage}`}
          x1="0%"
          y1="0%"
          x2="100%"
          y2="0%"
        >
          <stop offset={`${fillPercentage}%`} stopColor={color} />
          <stop offset={`${fillPercentage}%`} stopColor="#D9D9D9" />
        </linearGradient>
      </defs>
      <path
        fill={`url(#starFill-${fillPercentage})`}
        d="M0 7.361a653 653 0 0 1 4.22 4.437c.201.218.155.495.007.678q.05 1.92.108 3.832.046 1.552.09 3.11l2.23-1.805c.388-.303.77-.619 1.153-.935.495-.41.994-.822 1.513-1.213.428-.33.889-.528 1.448-.594.463-.059.95.036 1.419.127l.194.038q1.702.345 3.406.677 1.072.21 2.146.423c-.375-.532-.742-1.064-1.109-1.596-.332-.5-.678-.988-1.02-1.47q-.301-.422-.593-.842c-.23-.364-.46-.727-.526-1.19-.066-.495.066-.925.263-1.354.606-1.41 1.236-2.822 1.869-4.233l-.618.054c-.47.041-.938.083-1.416.116l-.608.05c-.206.016-.412.032-.61.049-.394.033-.79.033-1.151-.165-.292-.176-.507-.456-.712-.725l-.078-.101a12 12 0 0 1-.379-.512c-.123-.174-.246-.347-.378-.512A970 970 0 0 0 8.488.428c-.281.743-.563 1.498-.845 2.252q-.27.694-.527 1.388-.258.694-.527 1.387c-.131.397-.296.793-.625 1.09-.362.331-.79.43-1.25.463-.637.066-1.273.103-1.91.14a83 83 0 0 0-.954.058A65 65 0 0 1 0 7.361"
      />
      <path
        fill={`url(#starFill-${fillPercentage})`}
        d="M9.737 2.143q.57.786 1.131 1.562c.132.165.255.338.378.512a12 12 0 0 0 .457.613c.205.269.42.549.712.725.362.198.757.198 1.152.165l.609-.05.608-.05q.188-.012.375-.027c.095.114.208.208.35.226.215.048.483.009.74-.03q.145-.021.28-.036l.16-.014a278 278 0 0 0-1.74 3.945c-.197.43-.33.859-.263 1.354.066.463.296.826.526 1.19q.292.42.593.842c.342.482.688.97 1.02 1.47.367.532.734 1.064 1.109 1.596q-1.073-.213-2.146-.423c-1.135-.223-2.27-.446-3.406-.677l-.194-.038c-.469-.09-.956-.185-1.419-.127-.56.066-1.02.264-1.448.594-.52.391-1.018.803-1.513 1.213-.383.316-.765.632-1.153.934l-.844.684-.028-.93c-.04-1.373-.082-2.746-.115-4.127a710 710 0 0 1-4.64-4.876c-.297-.33-.066-.826.361-.86.527-.032 1.054-.074 1.58-.115.527-.041 1.053-.083 1.58-.116l1.382-.099q.227-.015.453-.025c.307-.015.61-.03.897-.074.296-.033.526-.132.69-.396.175-.292.298-.635.415-.962l.047-.128c.329-.876.658-1.743.987-2.61z"
      />
      <path
        fill={`url(#starFill-${fillPercentage})`}
        d="M5.848 19.571a556 556 0 0 0 3.775-3.1c.296-.24.6-.488.982-.576.382-.089.816-.02 1.21.044l.14.022q.815.171 1.645.33c.55.107 1.102.215 1.645.33q.916.176 1.843.364c.617.124 1.234.248 1.843.363.198.033.395 0 .527-.165.099-.132.164-.396.033-.562-.51-.726-1.012-1.445-1.514-2.163-.502-.719-1.004-1.437-1.514-2.164q-.093-.131-.195-.266c-.184-.245-.376-.5-.496-.791-.165-.33-.099-.628.033-.958.177-.476.39-.952.604-1.428.142-.317.284-.634.416-.95.197-.463.403-.926.609-1.388l.384-.868 1.064-.09 1.168-.1c.165-.033.362.1.428.232.099.165.066.33 0 .495-.197.463-.403.925-.609 1.388s-.411.925-.609 1.387c-.131.317-.273.635-.416.952-.213.475-.426.951-.604 1.427-.131.33-.197.627-.033.958.121.29.313.546.497.792q.102.133.194.265l1.514 2.163.001.002c.502.718 1.003 1.436 1.513 2.163.132.165.066.43-.033.561-.131.165-.329.198-.526.165-.609-.115-1.226-.239-1.843-.363s-1.235-.248-1.843-.363a96 96 0 0 0-1.646-.33 97 97 0 0 1-1.646-.331l-.138-.022c-.394-.063-.829-.133-1.21-.044-.383.088-.687.335-.982.576l-.105.085a720 720 0 0 1-5.2 4.26c-.329.265-.856.1-.856-.362q-.023-.972-.05-1.94"
      />
    </svg>
  );
};
