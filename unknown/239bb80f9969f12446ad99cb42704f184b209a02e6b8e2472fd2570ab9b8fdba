"use client";

import QuickAddIcon from "@/assets/icons/QuickAdd";
import React from "react";

interface QuickAddIconButtonProps {
  productId: string;
  primaryColor: string;
  variantId?: string;
}

const QuickAddIconButton: React.FC<QuickAddIconButtonProps> = ({
  productId,
  primaryColor,
  variantId,
}) => {
  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent link navigation
    e.stopPropagation(); // Stop event bubbling

    try {
      // TODO: add to cart logic
      console.log("Adding to cart:", { productId, variantId });
    } catch (error) {
      console.error("Error adding to cart:", error);
    }
  };

  return (
    <div
      className="absolute top-0 left-0 text-white px-2 py-2 text-[11px] font-semibold rounded-br-[10.45px] w-10.5 h-10.5 flex items-center justify-center z-10 shadow-[0_2.35px_3.53px_rgba(0,152,151,0.4)] cursor-pointer"
      style={{ backgroundColor: primaryColor }}
      onClick={handleAddToCart}
    >
      <QuickAddIcon />
    </div>
  );
};

export default QuickAddIconButton;
