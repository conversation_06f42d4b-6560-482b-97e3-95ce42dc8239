type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const MoveArrow = ({ color = "#FFF", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="26"
    height="26"
    fill="none"
    viewBox="0 0 26 26"
    {...props}
  >
    <circle
      cx="12.859"
      cy="12.859"
      r="11.859"
      fill="#fff"
      stroke={color}
      strokeWidth="1.52"
    ></circle>
    <path
      stroke={color}
      strokeLinecap="square"
      strokeWidth="1.52"
      d="m14.836 8.906-3.953 3.954 3.953 3.953"
    ></path>
  </svg>
);

export default MoveArrow;
