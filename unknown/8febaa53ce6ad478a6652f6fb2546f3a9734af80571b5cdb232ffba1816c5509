{"kind": "singleType", "collectionName": "carts", "info": {"singularName": "cart", "pluralName": "carts", "displayName": "<PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"title": {"type": "text"}, "description": {"type": "text"}, "icon": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "upsell_categories": {"type": "component", "repeatable": true, "component": "search.search-upsell-collections"}, "button": {"displayName": "<PERSON><PERSON>", "type": "component", "repeatable": false, "component": "elements.button"}}}