"use client";

import useMediaQuery from "@/hooks/useMediaQuery";
import { ComponentProps } from "@/types/Common";
import { BannerImageCarouselType } from "@/types/Components/Banner/BannerImageCarousel";
import { cn } from "@/libs/utils";
import { FC, useEffect, useMemo } from "react";
import Img from "@/components/Elements/img";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselDots,
} from "@/components/ui/carousel";
import Link from "next/link";

const ImageCarousel: FC<ComponentProps<BannerImageCarouselType>> = ({
  block,
  collectionData,
  // pageType is available but not currently used
}) => {
  const isMobile = useMediaQuery("(max-width: 576px)");
  useEffect(() => {
    if (!(block?.endless_banner && block?.position === 0 && isMobile)) {
      return;
    }

    const header = document.getElementById("header");
    if (header) {
      header.style.position = "absolute";
      header.style.top = "0px";
      header.style.left = "0px";
      header.style.right = "0px";
    }

    // Cleanup function to reset header styles when component unmounts
    return () => {
      if (!header) return;

      header.style.position = "";
      header.style.top = "";
      header.style.left = "";
      header.style.right = "";
    };
  }, [block?.endless_banner, block?.position, isMobile]);

  useEffect(() => {
    const header = document.getElementById("header");
    if (!header) return;
    const bannerImages = collectionData?.strapi?.banner_image_carousel;
    const hasBannerImages =
      Array.isArray(bannerImages) && bannerImages.length > 0;
    const banner = hasBannerImages ? bannerImages[0] : null;

    const shouldApplyStyles =
      (banner?.endless_banner &&
        banner?.position === 0 &&
        !collectionData?.strapi?.breadcrumb?.breadcrumb_items) ||
      (block?.endless_banner &&
        block?.position === 0 &&
        block?.images?.[0]?.desktop?.media &&
        block?.images?.[0]?.mobile?.media);

    if (shouldApplyStyles && isMobile) {
      Object.assign(header.style, {
        position: "absolute",
        top: "0px",
        left: "0px",
        right: "0px",
      });
    }

    // Cleanup function to reset header styles when component unmounts
    return () => {
      // Reset all styles that were applied
      header.style.position = "";
      header.style.top = "";
      header.style.left = "";
      header.style.right = "";
    };
  }, [
    block,
    block?.endless_banner,
    block?.position,
    collectionData?.strapi?.banner_image,
    collectionData?.strapi?.banner_image_carousel,
    collectionData?.strapi?.breadcrumb?.breadcrumb_items,
    isMobile,
  ]);

  const banners = useMemo(() => {
    const bannerImages = collectionData?.strapi?.banner_image_carousel;

    if (bannerImages?.length) {
      return bannerImages?.[block?.position || 0]?.images || [];
    }
    return block?.images || [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const checkIsFullBanner = useMemo(() => {
    if ((block?.position || 0) > 0) {
      return true;
    }
    return false;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!block.show) return null;

  return (
    <div className={cn("", checkIsFullBanner ? "my-10 md:mx-[122px]" : "")}>
      <Carousel
        autoPlayOpts={block.auto_play ? { delay: 3000 } : undefined}
        className="w-full"
      >
        <div className="relative">
          <CarouselContent className="cursor-grab">
            {banners.map((image: BannerImageCarouselType['images'][0], index: number) => {
              return (
                <CarouselItem
                  className={cn("relative flex items-center justify-center")}
                  key={index}
                >
                  {/* Mobile image - visible on small screens, hidden on larger screens */}
                  <Link
                    href={image?.mobile?.action_link || ""}
                    className="block w-full sm:hidden"
                  >
                    <div className="aspect-[3/4] w-full relative">
                      <Img
                        src={image?.mobile?.media?.url || ""}
                        alt="Romita"
                        fill
                        className="object-cover"
                      />
                    </div>
                  </Link>

                  {/* Desktop image - hidden on small screens, visible on larger screens */}
                  <Link
                    href={image?.desktop?.action_link || ""}
                    className="hidden w-full sm:block"
                  >
                    <div className="aspect-[3/1] w-full relative">
                      <Img
                        src={image?.desktop?.media?.url || ""}
                        alt={
                          collectionData?.strapi?.title ||
                          collectionData?.title ||
                          ""
                        }
                        fill
                        className="object-cover"
                      />
                    </div>
                  </Link>
                </CarouselItem>
              );
            })}
          </CarouselContent>

          {/* Carousel dots for navigation - only show when there are multiple images */}
          {banners.length > 1 && (
            <div className="absolute bottom-4 left-0 right-0 z-10">
              <CarouselDots
                activeClassName="bg-white w-6 h-2 rounded-full"
                inactiveClassName="bg-white/50 w-2 h-2 rounded-full"
                baseClassName="transition-all duration-300 mx-1"
                className="gap-1"
              />
            </div>
          )}
        </div>
      </Carousel>
    </div>
  );
};

export default ImageCarousel;
