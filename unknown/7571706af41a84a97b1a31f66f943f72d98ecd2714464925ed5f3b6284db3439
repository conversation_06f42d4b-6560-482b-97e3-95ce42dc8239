// Interface automatically generated by schemas-to-ts

import { NutritionalFactDetails } from './NutritionalFactDetails';
import { NutritionalFactDetails_Plain } from './NutritionalFactDetails';
import { NutritionalFactDetails_NoRelations } from './NutritionalFactDetails';

export interface NutritionalFacts {
  show_component?: boolean;
  nutritional_fact_details: NutritionalFactDetails[];
  description?: string;
}
export interface NutritionalFacts_Plain {
  show_component?: boolean;
  nutritional_fact_details: NutritionalFactDetails_Plain[];
  description?: string;
}

export interface NutritionalFacts_NoRelations {
  show_component?: boolean;
  nutritional_fact_details: NutritionalFactDetails_NoRelations[];
  description?: string;
}

