import React from "react";
import { cn } from "@/libs/utils";
import { FlavorType } from "../../FlavorShowcase";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { HazlenutIcon } from "../HazlenutIcon";

interface FlavorSelectorProps {
  flavors: FlavorType[];
  selectedFlavorId: string;
  onSelectFlavor: (id: string) => void;
  className?: string;
}

const FlavorSelector: React.FC<FlavorSelectorProps> = ({
  flavors,
  selectedFlavorId,
  onSelectFlavor,
  className,
}) => {
  return (
    <div className={cn("mb-8 w-full", className)}>
      <Tabs
        value={selectedFlavorId}
        onValueChange={onSelectFlavor}
        className="w-full"
      >
        <UniversalCarousel
          useNativeScrollbar={true}
          hideScrollbar={true}
          itemClassName="px-1"
          className="w-full"
        >
          <TabsList className="bg-transparent h-auto p-0 w-auto flex gap-2 mb-2 mt-1">
            {flavors.map((flavor) => (
              <TabsTrigger
                key={flavor.id}
                value={flavor.id}
                className={cn(
                  "text-obviously rounded-lg text-xs font-semibold transition-all whitespace-nowrap border flex items-center justify-center gap-1 min-w-[180px] md:min-w-[202px] h-[38px]",
                  "border-[rgba(255,243,241,1)] text-white cursor-pointer",
                  "transition-colors duration-300 shadow-[-4.22px_5.06px_0px_0px_rgba(0,0,0,0.4)] relative z-[1]",
                  "data-[state=active]:bg-white data-[state=active]:text-black data-[state=active]:shadow-[-4.22px_5.06px_0px_0px_rgba(0,0,0,0.4)]",
                  "data-[state=inactive]:bg-transparent",
                  "hover:data-[state=inactive]:bg-white/10"
                )}
              >
                {flavor.id === "hazelnut-cocoa" && (
                  <span className="w-4 h-4 flex-shrink-0">
                    <HazlenutIcon />
                  </span>
                )}
                {flavor.id === "double-cocoa" && (
                  <span className="w-4 h-4 flex-shrink-0">
                    <HazlenutIcon />
                  </span>
                )}
                {flavor.id === "coffee-cocoa" && (
                  <span
                    className={`w-4 h-4 flex-shrink-0 ${
                      selectedFlavorId === flavor.id
                        ? "text-black"
                        : "text-white"
                    }`}
                  >
                    ☕
                  </span>
                )}
                {flavor.id === "peanut-cocoa" && (
                  <span
                    className={`w-4 h-4 flex-shrink-0 ${
                      selectedFlavorId === flavor.id
                        ? "text-black"
                        : "text-white"
                    }`}
                  >
                    🥜
                  </span>
                )}
                {flavor.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </UniversalCarousel>
      </Tabs>
    </div>
  );
};

export default FlavorSelector;
