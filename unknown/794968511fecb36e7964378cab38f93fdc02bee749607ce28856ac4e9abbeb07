"use client";

import type { AutoplayOptionsType } from "embla-carousel-autoplay";
import Autoplay from "embla-carousel-autoplay";
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from "embla-carousel-react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import * as React from "react";
import { cn } from "@/libs/utils";
import { Button } from "@/components/ui/button";

type CarouselApi = UseEmblaCarouselType[1];
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>;
type CarouselOptions = UseCarouselParameters[0];
type CarouselPlugin = UseCarouselParameters[1];

type CarouselProps = {
  opts?: CarouselOptions;
  plugins?: CarouselPlugin;
  orientation?: "horizontal" | "vertical";
  setApi?: (api: CarouselApi) => void;
  autoPlayOpts?: AutoplayOptionsType;
  onSlideChange?: (index: number) => void; // New prop
};

type CarouselContextProps = {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0];
  api: ReturnType<typeof useEmblaCarousel>[1];
  scrollPrev: () => void;
  scrollNext: () => void;
  canScrollPrev: boolean;
  canScrollNext: boolean;
} & CarouselProps;

export const CarouselContext = React.createContext<CarouselContextProps | null>(
  null
);

export function useCarousel() {
  const context = React.useContext(CarouselContext);

  if (!context) {
    throw new Error("useCarousel must be used within a <Carousel />");
  }

  return context;
}

const Carousel = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & CarouselProps
>(
  (
    {
      orientation = "horizontal",
      opts,
      setApi,
      plugins,
      className,
      children,
      autoPlayOpts,
      onSlideChange, // New prop
      ...props
    },
    ref
  ) => {
    if (autoPlayOpts) {
      plugins = plugins || [];
      plugins.push(Autoplay(autoPlayOpts));
    }

    const [carouselRef, api] = useEmblaCarousel(
      {
        ...opts,
        axis: orientation === "horizontal" ? "x" : "y",
      },
      plugins
    );
    const [canScrollPrev, setCanScrollPrev] = React.useState(false);
    const [canScrollNext, setCanScrollNext] = React.useState(false);

    const onSelect = React.useCallback(
      (api: CarouselApi) => {
        if (!api) {
          return;
        }

        setCanScrollPrev(api.canScrollPrev());
        setCanScrollNext(api.canScrollNext());

        if (onSlideChange) {
          onSlideChange(api.selectedScrollSnap());
        }
      },
      [onSlideChange]
    );

    const scrollPrev = React.useCallback(() => {
      api?.scrollPrev();
    }, [api]);

    const scrollNext = React.useCallback(() => {
      api?.scrollNext();
    }, [api]);

    const handleKeyDown = React.useCallback(
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.key === "ArrowLeft") {
          event.preventDefault();
          scrollPrev();
        } else if (event.key === "ArrowRight") {
          event.preventDefault();
          scrollNext();
        }
      },
      [scrollPrev, scrollNext]
    );

    React.useEffect(() => {
      if (!api || !setApi) {
        return;
      }

      setApi(api);
    }, [api, setApi]);

    React.useEffect(() => {
      if (!api) {
        return;
      }

      onSelect(api);
      api.on("reInit", onSelect);
      api.on("select", onSelect);

      return () => {
        api?.off("select", onSelect);
      };
    }, [api, onSelect]);

    return (
      <CarouselContext.Provider
        value={{
          carouselRef,
          api,
          opts,
          orientation:
            orientation || (opts?.axis === "y" ? "vertical" : "horizontal"),
          scrollPrev,
          scrollNext,
          canScrollPrev,
          canScrollNext,
        }}
      >
        <div
          ref={ref}
          onKeyDownCapture={handleKeyDown}
          className={cn("relative", className)}
          role="region"
          aria-roledescription="carousel"
          {...props}
        >
          {children}
        </div>
      </CarouselContext.Provider>
    );
  }
);

Carousel.displayName = "Carousel";

const CarouselContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { carouselRef, orientation } = useCarousel();

  return (
    <div ref={carouselRef} className="overflow-hidden">
      <div
        ref={ref}
        className={cn(
          "flex",
          orientation === "horizontal" ? "-ml-4" : "-mt-4 flex-col",
          className
        )}
        {...props}
      />
    </div>
  );
});
CarouselContent.displayName = "CarouselContent";

const CarouselItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { orientation } = useCarousel();

  return (
    <div
      ref={ref}
      role="group"
      aria-roledescription="slide"
      className={cn(
        "min-w-0 shrink-0 grow-0 basis-full",
        orientation === "horizontal" ? "pl-4" : "pt-4",
        className
      )}
      {...props}
    />
  );
});
CarouselItem.displayName = "CarouselItem";

const CarouselPrevious = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button> & {
    icon?: React.ReactNode;
  }
>(({ className, variant = "outline", size = "icon", icon, ...props }, ref) => {
  const { orientation, scrollPrev, canScrollPrev } = useCarousel();

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        "h-8 w-8 rounded-full",
        orientation === "horizontal"
          ? "-left-12 top-1/2 -translate-y-1/2"
          : "-top-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollPrev}
      onClick={scrollPrev}
      {...props}
    >
      {icon || (
        <ChevronLeft className="h-4 w-4" />
      )}
      <span className="sr-only">Previous slide</span>
    </Button>
  );
});
CarouselPrevious.displayName = "CarouselPrevious";

const CarouselNext = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button> & {
    icon?: React.ReactNode;
  }
>(({ className, variant = "outline", size = "icon", icon, ...props }, ref) => {
  const { orientation, scrollNext, canScrollNext } = useCarousel();

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        "h-8 w-8 rounded-full",
        orientation === "horizontal"
          ? "-right-12 top-1/2 -translate-y-1/2"
          : "-bottom-12 left-1/2 -translate-x-1/2 rotate-90",
        className
      )}
      disabled={!canScrollNext}
      onClick={scrollNext}
      {...props}
    >
      {icon || (
        <ChevronRight className="h-4 w-4" />
      )}
      <span className="sr-only">Next slide</span>
    </Button>
  );
});
CarouselNext.displayName = "CarouselNext";

const CarouselDots = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    baseClassName?: string;
    activeClassName?: string;
    inactiveClassName?: string;
  }
>(
  (
    {
      className,
      baseClassName,
      activeClassName = "bg-primary",
      inactiveClassName = "bg-primary/20",
      ...props
    },
    ref
  ) => {
    const { api } = useCarousel();
    const [selectedIndex, setSelectedIndex] = React.useState(0);

    React.useEffect(() => {
      if (!api) return;

      const onSelect = () => {
        setSelectedIndex(api.selectedScrollSnap());
      };

      api.on("select", onSelect);

      return () => {
        api.off("select", onSelect);
      };
    }, [api]);

    const length = api?.scrollSnapList().length || 0;

    return (
      <div
        ref={ref}
        className={cn("flex justify-center gap-2", className)}
        {...props}
      >
        {Array.from({ length }, (_, i) => (
          <button
            key={i}
            aria-label={`Go to slide ${i + 1}`}
            onClick={() => api?.scrollTo(i)}
            className={cn(
              "aspect-square w-3 rounded-full transition-colors",
              baseClassName,
              i === selectedIndex ? activeClassName : inactiveClassName
            )}
          />
        ))}
      </div>
    );
  }
);
CarouselDots.displayName = "CarouselDots";

const CarouselCurrentSlide = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    format?: "numeric" | "percentage";
    className?: string;
  }
>(({ format = "numeric", className, ...props }, ref) => {
  const { api } = useCarousel();
  const [currentSlide, setCurrentSlide] = React.useState(1);
  const [totalSlides, setTotalSlides] = React.useState(0);

  React.useEffect(() => {
    if (!api) return;

    // Ensure total slides is set
    const slides = api.scrollSnapList();
    setTotalSlides(slides.length);

    // Initial slide selection
    const updateSlide = () => {
      const selectedIndex = api.selectedScrollSnap();
      setCurrentSlide(selectedIndex + 1);
    };

    // Set up initial state and listeners
    updateSlide();
    api.on("select", updateSlide);
    api.on("reInit", updateSlide);

    // Cleanup
    return () => {
      api.off("select", updateSlide);
      api.off("reInit", updateSlide);
    };
  }, [api]);

  const renderContent = () => {
    switch (format) {
      case "numeric":
        return `${currentSlide}`;

      case "percentage":
        return `${Math.round((currentSlide / totalSlides) * 100)}%`;
    }
  };

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center text-sm text-muted-foreground",
        className
      )}
      {...props}
    >
      {renderContent()}
    </div>
  );
});
CarouselCurrentSlide.displayName = "CarouselCurrentSlide";

const CarouselTotalSlides = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    className?: string;
  }
>(({ className, ...props }, ref) => {
  const { api } = useCarousel();
  const [totalSlides, setTotalSlides] = React.useState(0);

  React.useEffect(() => {
    if (!api) return;

    // Ensure total slides is set
    const slides = api.scrollSnapList();
    setTotalSlides(slides.length);
  }, [api]);

  return (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-center text-sm text-muted-foreground",
        className
      )}
      {...props}
    >
      {totalSlides}
    </div>
  );
});
CarouselTotalSlides.displayName = "CarouselTotalSlides";

export {
  Carousel,
  CarouselContent,
  CarouselCurrentSlide,
  CarouselDots,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  CarouselTotalSlides,
  type CarouselApi,
};
