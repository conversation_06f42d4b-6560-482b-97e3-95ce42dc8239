"use client";

import React, { useState } from "react";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { sendOtp, verifyOtp } from "@/redux/actions/auth.actions";
import {
  Dialog,
  DialogContent,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { UserIcon } from "@/assets/icons/User";
import { CloseIcon } from "@/assets/icons/Close";

interface AuthDialogProps {
  trigger?: React.ReactNode;
}

const AuthDialog: React.FC<AuthDialogProps> = ({ trigger }) => {
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState("");
  const [open, setOpen] = useState(false);

  const dispatch = useAppDispatch();
  const { loading, otpSent, error } = useAppSelector((state) => state.auth);

  const handleSendOtp = () => {
    if (phone.length === 10) {
      dispatch(sendOtp({ phone }));
    }
  };

  const handleVerifyOtp = () => {
    if (otp.length === 6) {
      dispatch(verifyOtp({ phone, otp }));
    }
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 10) {
      setPhone(value);
    }
  };

  const handleOtpChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/\D/g, "");
    if (value.length <= 6) {
      setOtp(value);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <button className="flex items-center justify-center">
            <UserIcon />
          </button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-md p-0 border-none rounded-lg overflow-hidden">
        <div className="bg-white p-6 relative">
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <CloseIcon />
            <span className="sr-only">Close</span>
          </DialogClose>

          <div className="flex flex-col items-center mb-6">
            <div className="bg-yellow-400 px-4 py-1 rounded-md mb-4">
              <h2 className="text-black font-bold text-xl">Login / Signup</h2>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Input
                type="tel"
                placeholder="Mobile number"
                value={phone}
                onChange={handlePhoneChange}
                className="w-full border-gray-300 focus:border-primary"
                disabled={otpSent}
              />
            </div>

            {otpSent && (
              <div>
                <Input
                  type="text"
                  placeholder="Enter OTP"
                  value={otp}
                  onChange={handleOtpChange}
                  className="w-full border-gray-300 focus:border-primary"
                />
              </div>
            )}

            {error && <p className="text-red-500 text-sm">{error}</p>}

            <div className="text-center text-sm text-gray-600">
              By continuing, you agree to our companys
              <div className="flex justify-center gap-1">
                <a href="#" className="text-primary hover:underline">
                  Terms and Conditions
                </a>
                <span>and</span>
                <a href="#" className="text-primary hover:underline">
                  Privacy Policy
                </a>
              </div>
            </div>

            <Button
              onClick={otpSent ? handleVerifyOtp : handleSendOtp}
              className="w-full bg-[#93385D] hover:bg-[#7D2D4E] text-white py-2 rounded-md"
              disabled={
                loading ||
                (phone.length !== 10 && !otpSent) ||
                (otp.length !== 6 && otpSent)
              }
            >
              {loading ? "Processing..." : otpSent ? "VERIFY OTP" : "SEND OTP"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AuthDialog;
