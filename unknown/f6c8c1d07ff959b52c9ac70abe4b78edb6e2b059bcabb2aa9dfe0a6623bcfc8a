// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface Cashback {
  description?: string;
  tooltip?: string;
  icon?: { data: Media };
}
export interface Cashback_Plain {
  description?: string;
  tooltip?: string;
  icon?: Media_Plain;
}

export interface Cashback_NoRelations {
  description?: string;
  tooltip?: string;
  icon?: number;
}

