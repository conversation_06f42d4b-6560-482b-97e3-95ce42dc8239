// Interface automatically generated by schemas-to-ts

import { KeyValue } from '../../elements/interfaces/KeyValue';
import { KeyValue_Plain } from '../../elements/interfaces/KeyValue';
import { KeyValue_NoRelations } from '../../elements/interfaces/KeyValue';

export interface ProductDetailsExtra {
  product_detail_extra_items: KeyValue[];
  title?: string;
  show_component: boolean;
}
export interface ProductDetailsExtra_Plain {
  product_detail_extra_items: KeyValue_Plain[];
  title?: string;
  show_component: boolean;
}

export interface ProductDetailsExtra_NoRelations {
  product_detail_extra_items: KeyValue_NoRelations[];
  title?: string;
  show_component: boolean;
}

