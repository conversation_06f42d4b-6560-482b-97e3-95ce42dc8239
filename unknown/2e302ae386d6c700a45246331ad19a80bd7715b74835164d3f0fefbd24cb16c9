interface IconProps extends React.SVGProps<SVGSVGElement> {
    color?: string;
}

export function ContactUs({ color = "#93385d", ...props }: IconProps) {
    return (
        <svg
            xmlnsXlink="http://www.w3.org/1999/xlink"
            className="mb-1"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            {...props}
        >
            <g clipPath="url(#clip0_250_174)" fill="none">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M12 1.67997C6.30045 1.67997 1.68003 6.30039 1.68003 12C1.68003 13.5431 2.05297 14.3629 2.71362 15.7541L2.72435 15.7766L3.49866 17.9705L2.77987 21.1651C2.68091 21.605 3.08422 21.9927 3.51983 21.8766L7.35966 20.8526L7.51423 20.921C7.60872 20.9628 7.70484 21.0056 7.80242 21.049C8.40382 21.3168 9.06097 21.6093 9.73811 21.8489C10.5267 22.1279 11.3031 22.32 12 22.32C14.8099 22.32 17.39 20.8334 19.2825 18.7636C21.1806 16.6877 22.32 14.0981 22.32 12C22.32 9.90181 21.1806 7.31222 19.2825 5.23637C17.39 3.16657 14.8099 1.67997 12 1.67997ZM0.720032 12C0.720032 5.7702 5.77026 0.719971 12 0.719971C15.1549 0.719971 17.9747 2.38337 19.991 4.58857C22.0018 6.78772 23.28 9.59812 23.28 12C23.28 14.4018 22.0018 17.2122 19.991 19.4114C17.9747 21.6166 15.1549 23.28 12 23.28C11.1435 23.28 10.2473 23.0473 9.41794 22.7539C8.702 22.5006 8.0066 22.1909 7.40701 21.9239C7.36421 21.9048 7.3219 21.886 7.2801 21.8674L2.23521 23.2127C1.7996 23.3289 1.39628 22.9411 1.49524 22.5012L2.50141 18.0294L1.83571 16.1433C1.17013 14.7414 0.720032 13.7593 0.720032 12Z"
                    fill={color}
                />
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M9.05088 5.55766C9.92446 4.9566 11.1199 5.17751 11.721 6.05109L12.5372 7.23741C12.872 7.72394 12.8674 8.39237 12.7619 8.87171C12.7067 9.12234 12.6135 9.37336 12.4794 9.57268C12.3632 9.74556 12.1192 10.0153 11.7432 9.99121C11.7432 9.99119 11.7432 9.99123 11.7432 9.99121C11.7432 9.99197 11.7076 9.996 11.6277 10.0378C11.5391 10.0841 11.4383 10.1547 11.3368 10.2371C11.2373 10.3179 11.1481 10.4011 11.0833 10.4649C11.0512 10.4965 11.0257 10.5226 11.0088 10.5403C11.0003 10.5491 10.9941 10.5558 10.9902 10.56L10.9862 10.5643L10.9857 10.5647L10.9488 10.6052L10.9034 10.6365C10.7144 10.7665 10.6316 10.8825 10.5938 10.9729C10.5567 11.0618 10.5454 11.1686 10.5738 11.313C10.6369 11.6336 10.8688 12.0264 11.214 12.5282C11.497 12.9395 11.8754 13.2127 12.2142 13.3469C12.3838 13.414 12.5303 13.4412 12.6394 13.4421C12.7493 13.4429 12.7893 13.4178 12.7913 13.4166C12.7913 13.4165 12.7913 13.4165 12.7913 13.4166C13.0307 13.2363 13.3318 13.0443 13.565 12.9016C13.6833 12.8292 13.7873 12.7676 13.8618 12.7239C13.8991 12.7021 13.9292 12.6847 13.9501 12.6727L13.966 12.6635C14.838 12.0775 16.0214 12.3013 16.6184 13.169L17.4347 14.3553C18.0357 15.2289 17.8148 16.4243 16.9413 17.0254C15.435 18.0618 14.0875 18.5794 12.6909 18.1335C11.3455 17.704 10.0958 16.4158 8.64315 14.2959C7.1909 12.1767 6.43321 10.5466 6.5077 9.13601C6.58512 7.66998 7.54322 6.59501 9.05088 5.55766ZM14.4572 13.4886L14.4559 13.4893L14.4506 13.4923L14.429 13.5047C14.4099 13.5157 14.3818 13.5319 14.3467 13.5525C14.2763 13.5937 14.1779 13.652 14.0662 13.7204C13.8394 13.8591 13.5703 14.0317 13.3688 14.1834C13.1476 14.35 12.8782 14.4039 12.632 14.402C12.3814 14.4001 12.1169 14.3409 11.8608 14.2394C11.3482 14.0364 10.8163 13.6437 10.4231 13.0723C10.0922 12.5914 9.73905 12.0427 9.63188 11.4984C9.57517 11.2104 9.58235 10.9039 9.70791 10.6031C9.82668 10.3186 10.035 10.0785 10.3151 9.87668C10.315 9.87673 10.3151 9.87663 10.3151 9.87668C10.3378 9.85288 10.3703 9.81962 10.4102 9.78036C10.4895 9.70245 10.6017 9.59737 10.7316 9.49193C10.8594 9.3881 11.0158 9.27444 11.1829 9.18707C11.3133 9.11884 11.4896 9.04553 11.6855 9.03295C11.7325 8.96138 11.7871 8.8342 11.8243 8.66525C11.905 8.2991 11.8624 7.95022 11.7463 7.78157L10.9301 6.59525C10.6296 6.15846 10.0318 6.04801 9.59504 6.34854C8.15437 7.3398 7.51937 8.1829 7.46636 9.18664C7.41043 10.2459 7.98497 11.6372 9.43506 13.7533C10.8847 15.8688 11.976 16.8976 12.9829 17.219C13.9387 17.5241 14.955 17.2267 16.3971 16.2345C16.8339 15.934 16.9443 15.3362 16.6438 14.8995L15.8276 13.7131C15.527 13.2764 14.9293 13.1659 14.4925 13.4664L14.4754 13.4782L14.4572 13.4886ZM11.6512 9.07647C11.6512 9.07606 11.6537 9.07321 11.6588 9.06915C11.6538 9.07484 11.6513 9.07687 11.6512 9.07647Z"
                    fill={color}
                />
            </g>
            <defs>
                <clipPath id="clip0_250_174">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
}