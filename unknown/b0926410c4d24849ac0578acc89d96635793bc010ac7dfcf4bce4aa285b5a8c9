export const getPageDetailsQuery = /* GraphQL */ `
  query GetPageDetails($filters: PageFiltersInput) {
    pages(filters: $filters) {
      handle
      page_type
      blocks {
        __typename
        ... on ComponentBannerSingleBanner {
          id
          web {
            url
            alternativeText
            mime
          }
          mobile {
            url
            alternativeText
            mime
          }
          action_link
        }
        ... on ComponentBannerBannerCarousel {
          __typename
          id
          images {
            id
            action_link
            web {
              url
              alternativeText
              mime
            }
            mobile {
              url
              alternativeText
              mime
            }
          }
        }
        ... on ComponentCommonMarquee {
          id
          titles {
            id
            title
          }
        }
      }
    }
  }
`;

export const getProductDetailsQuery = /* GraphQL */ `
  query Products($filters: ProductFiltersInput) {
    products(filters: $filters) {
      documentId
      title
      systemId
      handle
      productType
      primary_color
      bg_color
      whats_inside {
        id
        title
        show_component
        whats_inside_details {
          id
          title
          details {
            id
            key
            value
          }
          image {
            id
            web {
              url
              alternativeText
              mime
            }
            mobile {
              url
              alternativeText
              mime
            }
          }
        }
      }
      nutritional_facts {
        id
        show_component
        nutritional_fact_details {
          id
          nutritional_fact_items {
            id
            key
            value
            nutritional_fact_sub_items {
              id
              key
              value
            }
          }
        }
        description
      }
      template {
        page_type
        blocks {
          ... on ComponentPdpTemplatesIngredientDetailsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesWhatsInsideTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesReviewTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesProductDetailsExtraTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesProductAdditionalDescriptionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesNutritionalFactsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesGotAQuestionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesCertificateBannerTemplate {
            id
            __typename
          }
          ... on ComponentReviewTemplatesRealPeopleReviewsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesWhySectionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesQuicklyAddedTemplate {
            id
            __typename
          }
          ... on ComponentReviewTemplatesVerifiedReviewsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesKeyFeaturesTemplate {
            id
            __typename
          }
          ... on ComponentFaqsFaqTemplate {
            id
            __typename
          }
          ... on ComponentBannerSingleBanner {
            id
            web {
              alternativeText
              url
              mime
            }
            mobile {
              alternativeText
              url
              mime
            }
            action_link
          }
          ... on ComponentBannerBannerCarousel {
            id
            images {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
              action_link
            }
          }
          ... on Error {
            code
            message
          }
        }
        createdAt
        updatedAt
        publishedAt
      }
      show_certificates
      show_real_people_reviews
      why_section {
        id
        primary_image {
          web {
            url
            alternativeText
            mime
          }
          mobile {
            alternativeText
            url
            mime
          }
          id
        }
        description
        title
        image_carousels {
          id
          images {
            id
            web {
              alternativeText
              url
              mime
            }
            mobile {
              alternativeText
              url
              mime
            }
            action_link
          }
        }
        show_component
      }
      faqs {
        id
        faq_items {
          id
          question
          answer
        }
        title
        subtitle
        show_component
      }
      quickly_added_category {
        __typename
        documentId
        systemId
        title
        handle
        createdAt
        updatedAt
        publishedAt
      }
      show_navigation_chips
      key_features {
        id
        key_feature_items {
          id
          flippable_card_items {
            back {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
            }
            front {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
            }
            id
          }
        }
        title
        show_component
      }
      show_review_navigation
      short_description
      product_detail_extra {
        id
        product_detail_extra_items {
          id
          key
          value
        }
        title
        show_component
      }
      additional_description {
        id
        show_component
        description
      }
      show_delievery_option
      offer {
        id
        code
        description
        show_component
      }
      banner_images {
        id
        web {
          alternativeText
          mime
          url
        }
        mobile {
          alternativeText
          mime
          url
        }
        action_link
      }
      createdAt
      updatedAt
      publishedAt
      variants {
        documentId
        title
        systemId
        sku
        tag
        createdAt
        updatedAt
        publishedAt
      }
    }
  }
`;

export const getBYOBProductDetailsQuery = /* GraphQL */ `
  query GetBundleAndMainProduct(
    $bundleFilters: ProductFiltersInput
    $mainProductFilters: ProductFiltersInput
  ) {
    mainProduct: products(filters: $mainProductFilters) {
      title
      systemId
      bg_color
      primary_color
      template {
        page_type
        blocks {
          ... on ComponentPdpTemplatesIngredientDetailsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesWhatsInsideTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesReviewTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesProductDetailsExtraTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesProductAdditionalDescriptionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesNutritionalFactsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesGotAQuestionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesCertificateBannerTemplate {
            id
            __typename
          }
          ... on ComponentReviewTemplatesRealPeopleReviewsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesWhySectionTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesQuicklyAddedTemplate {
            id
            __typename
          }
          ... on ComponentReviewTemplatesVerifiedReviewsTemplate {
            id
            __typename
          }
          ... on ComponentPdpTemplatesKeyFeaturesTemplate {
            id
            __typename
          }
          ... on ComponentFaqsFaqTemplate {
            id
            __typename
          }
          ... on ComponentBannerSingleBanner {
            id
            web {
              alternativeText
              url
              mime
            }
            mobile {
              alternativeText
              url
              mime
            }
            action_link
          }
          ... on ComponentBannerBannerCarousel {
            id
            images {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
              action_link
            }
          }
          ... on Error {
            code
            message
          }
        }
        createdAt
        updatedAt
        publishedAt
      }
      show_certificates
      show_real_people_reviews
      why_section {
        id
        primary_image {
          web {
            url
            alternativeText
            mime
          }
          mobile {
            alternativeText
            url
            mime
          }
          id
        }
        description
        title
        image_carousels {
          id
          images {
            id
            web {
              alternativeText
              url
              mime
            }
            mobile {
              alternativeText
              url
              mime
            }
            action_link
          }
        }
        show_component
      }
      faqs {
        id
        faq_items {
          id
          question
          answer
        }
        title
        subtitle
        show_component
      }
      quickly_added_category {
        __typename
        documentId
        systemId
        title
        handle
        createdAt
        updatedAt
        publishedAt
      }
      show_navigation_chips
      key_features {
        id
        key_feature_items {
          id
          flippable_card_items {
            back {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
            }
            front {
              id
              web {
                alternativeText
                url
                mime
              }
              mobile {
                alternativeText
                url
                mime
              }
            }
            id
          }
        }
        title
        show_component
      }
      show_review_navigation
      short_description
      product_detail_extra {
        id
        product_detail_extra_items {
          id
          key
          value
        }
        title
        show_component
      }
      additional_description {
        id
        show_component
        description
      }
      show_delievery_option
      offer {
        id
        code
        description
        show_component
      }
      banner_images {
        id
        web {
          alternativeText
          mime
          url
        }
        mobile {
          alternativeText
          mime
          url
        }
        action_link
      }
      createdAt
      updatedAt
      publishedAt
      variants {
        documentId
        title
        systemId
        sku
        tag
        createdAt
        updatedAt
        publishedAt
      }
    }
    bundle: products(filters: $bundleFilters) {
      title
      systemId
      bg_color
      primary_color
      whats_inside {
        id
        title
        show_component
        whats_inside_details {
          id
          title
          details {
            id
            key
            value
          }
          image {
            id
            web {
              url
              alternativeText
              mime
            }
            mobile {
              url
              alternativeText
              mime
            }
          }
        }
      }
      nutritional_facts {
        id
        show_component
        nutritional_fact_details {
          id
          nutritional_fact_items {
            id
            key
            value
            nutritional_fact_sub_items {
              id
              key
              value
            }
          }
        }
        description
      }
    }
  }
`;