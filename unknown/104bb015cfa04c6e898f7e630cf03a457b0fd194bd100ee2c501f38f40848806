// Interface automatically generated by schemas-to-ts

import { MultiCategoryItems } from '../../plp/interfaces/MultiCategoryItems';
import { MultiCategoryItems_Plain } from '../../plp/interfaces/MultiCategoryItems';
import { MultiCategoryItems_NoRelations } from '../../plp/interfaces/MultiCategoryItems';

export interface RealFoodCategories {
  title?: string;
  subtitle?: any;
  multi_category_items: MultiCategoryItems[];
}
export interface RealFoodCategories_Plain {
  title?: string;
  subtitle?: any;
  multi_category_items: MultiCategoryItems_Plain[];
}

export interface RealFoodCategories_NoRelations {
  title?: string;
  subtitle?: any;
  multi_category_items: MultiCategoryItems_NoRelations[];
}

