/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import { MenuType } from "@/types/Components/Common/Menu";
import Image from "next/image";
import Link from "next/link";
import { getStrapiUrl } from "@/utils/strapiUrl";

interface SubMenuContentProps {
  item: MenuType;
  activeSubMenus: { [key: string]: string | null };
  handleSubMenuEnter: (menuId: string | null, subMenuId: string | null) => void;
}

const SubMenuContent: React.FC<SubMenuContentProps> = ({
  item,
  activeSubMenus,
  handleSubMenuEnter,
}) => {
  return (
    <div className=" w-full">
      <div className="max-w-[840px] w-full mx-auto px-6 box-border">
        <div className="grid grid-cols-3 mx-auto p-6">
          {item.sub_menu?.map((subItem, index) => (
            <Link
              key={index}
              href={subItem.action_link ?? "#"}
              className="bg-white flex items-center justify-between gap-2 px-4 py-3 rounded-2xl m-[10px] shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.12)] hover:shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.2)] transition-shadow duration-200"
              onMouseEnter={() => handleSubMenuEnter(item.title, subItem.title)}
            >
              <p className="font-narrow text-[#1a181e] text-lg font-semibold mx-2">
                {subItem.title}
              </p>
              {subItem.image && (
                <div className="relative aspect-video flex items-center justify-center w-16 h-16">
                  <Image
                    src={getStrapiUrl(subItem.image)}
                    alt={subItem.title}
                    fill
                    className="object-contain"
                  />
                </div>
              )}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
};

export default SubMenuContent;
