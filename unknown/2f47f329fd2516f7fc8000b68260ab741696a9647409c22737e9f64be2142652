interface IconProps extends React.SVGProps<SVGSVGElement> {
  strikethroughColor?: string;
}

export function MiniStrikeThrough({ ...props }: IconProps) {
  return (
    <svg
      xmlnsXlink="http://www.w3.org/1999/xlink"
      className="w-100"
      color="#be259a"
      xmlns="http://www.w3.org/2000/svg"
      width="59"
      height="17"
      viewBox="0 0 59 17"
      fill="none"
      {...props}
    >
      <path
        opacity="0.75"
        d="M4.36191 5.3316C20.1378 3.02844 52.8198 -0.886931 57.3407 1.87686C62.9918 5.3316 11.4257 12.9322 2.94914 15.005C-5.52747 17.0778 14.9577 15.005 38.9747 15.005C58.1884 15.005 59.2244 15.005 57.3407 15.005"
        stroke="#BE259A"
        strokeWidth="1.5px"
        strokeLinecap="round"
        strokeLinejoin="round"
      ></path>
    </svg>
  );
}
