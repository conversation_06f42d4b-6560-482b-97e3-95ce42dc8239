import { CollectionType } from "../Collections/Collection";
import { ProductType } from "../Collections/Product";
import { PageTypeEnum } from "../Page";
import { MediaType } from "./Media";

export interface Title {
  id: string;
  title: string;
  title_color: string;
}

export interface Image {
  web?: MediaType;
  mobile?: MediaType;
}

export interface RedirectLink {
  id: string;
  title: string;
  title_color: string;
  action_link: string;
}

export interface MediaNode {
  mediaContentType: string;
  alt: string;
  image: {
    url: string;
    altText: string | null;
  };
}

export interface SEO {
  description: string | null;
  title: string | null;
}

// Path: src/types/Common/index.ts
export interface SearchParams {
  [key: string]: string | string[] | undefined;
}

export interface PageParams {
  [key: string]: string;
}

export interface PageProps {
  params: Promise<PageParams>;
  searchParams?: Promise<SearchParams>;
}

export interface ComponentProps<T> {
  block: T;
  page: string;
  pageType: PageTypeEnum;
  priority: boolean;
  collectionData?: CollectionType;
  productData?: ProductType;
  pageData?: any;
}

export interface FetchResult {
  success: boolean;
  data?: any;
  error?: string;
}
