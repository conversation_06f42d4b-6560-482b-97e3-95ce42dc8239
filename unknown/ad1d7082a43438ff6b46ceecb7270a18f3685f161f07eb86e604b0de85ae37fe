/**
 * Calculates the discount percentage between the original price and the discounted price.
 *
 * @param originalPrice - The original (full) price of the product.
 * @param discountedPrice - The discounted (current) price of the product.
 * @returns The discount percentage as a whole number (e.g., 20 for 20%). Returns 0 if invalid input.
 */
export function calculateDiscount(
  originalPrice?: number,
  discountedPrice?: number
): number {
  if (
    typeof originalPrice !== "number" ||
    typeof discountedPrice !== "number" ||
    originalPrice <= 0 ||
    discountedPrice < 0 ||
    discountedPrice > originalPrice
  ) {
    return 0;
  }

  const discount = ((originalPrice - discountedPrice) / originalPrice) * 100;
  return Math.round(discount);
}
