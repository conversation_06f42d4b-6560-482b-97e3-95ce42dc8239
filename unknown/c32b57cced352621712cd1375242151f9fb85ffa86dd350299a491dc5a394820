import * as React from "react";
interface ChevronLeftProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}
const ChevronLeft = ({
  width = 16,
  height = 31,
  color = "#FFF",
  className = "",
}: ChevronLeftProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 16 31"
    className={className}
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="5"
      d="M13 3 3 15.5 13 28"
    ></path>
  </svg>
);

export default ChevronLeft;
