import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";

interface MainIngredientCardProps {
  ingredient: {
    name: string;
    percentage: number;
    description: string;
    image: string;
  };
  className?: string;
}

const MainIngredientCard: React.FC<MainIngredientCardProps> = ({
  ingredient,
  className,
}) => {
  return (
    <div
      className={cn(
        "border border-black bg-white rounded-lg px-[27.5px] pt-4 pb-9 mb-6 flex flex-col md:flex-row items-center text-black max-w-[800px] shadow-[-4.22px_5.06px_0px_0px_rgba(0,0,0,0.4)] relative",
        className
      )}
    >
      <div className="flex-1">
        <div className="text-base font-semibold font-obviously flex justify-between items-center mb-2.5">
          <h4>{ingredient.name}</h4>
          <span>{ingredient.percentage}%</span>
        </div>
        <div className="ingredients-divider bg-gradient-to-r from-[rgb(196,124,90)] to-transparent h-[2.5px] w-full rounded-full mb-3.5" />
        <p className="text-sm font-obviously w-[60%]">
          {ingredient.description}
        </p>
      </div>
      <div className="absolute bottom-0 right-0 w-[200px] h-[100px]">
        <Image
          src={ingredient.image}
          alt={ingredient.name}
          fill
          className="object-contain"
        />
      </div>
    </div>
  );
};

export default MainIngredientCard;
