import React from "react";

interface HazlenutIconProps {
  className?: string;
}

export const HazlenutIcon: React.FC<HazlenutIconProps> = ({ className }) => {
  return (
    <svg
      width="20"
      height="17"
      viewBox="0 0 20 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <g clipPath="url(#clip0_8_3032)">
        <path
          d="M10.5 1.5C10.5 1.5 11.5 2 13 2C14.5 2 16 1.5 16 1.5C16 1.5 16.5 3.5 16.5 5.5C16.5 7.5 15.5 9 15.5 9C15.5 9 14 10 12 10C10 10 8.5 9 8.5 9C8.5 9 7.5 7.5 7.5 5.5C7.5 3.5 8 1.5 8 1.5C8 1.5 9.5 2 10.5 1.5Z"
          fill="white"
        />
        <path
          d="M2.78087 9.41431C2.51123 9.20736 2.50141 8.95358 2.5354 8.68696C2.65927 7.72093 3.01275 6.83119 3.64493 6.10762C3.89418 5.82287 4.13663 5.46259 4.53996 5.30776C4.70537 5.24431 4.83377 5.23525 4.94707 5.35685C5.07547 5.49582 5.07245 5.66123 4.93876 5.79492C4.72879 6.00338 4.4954 6.18843 4.28769 6.39916C3.6404 7.05627 3.2839 7.85311 3.19931 8.76928C3.17287 9.05932 3.10263 9.30706 2.78087 9.41431Z"
          fill="white"
        />
        <path
          d="M7.22159 4.03501C7.22839 4.12489 7.17703 4.20722 7.08715 4.26613C6.83488 4.43003 6.58563 4.59846 6.32732 4.75254C6.16116 4.85224 6.00405 4.80768 5.90133 4.64906C5.79635 4.48743 5.8326 4.32882 5.99121 4.22157C6.25104 4.04558 6.51161 3.86809 6.7873 3.72005C6.99727 3.60751 7.22461 3.77518 7.22159 4.03501Z"
          fill="white"
        />
        <path
          d="M12.5 5.5C12.5 5.5 13 6.5 13 7.5C13 8.5 12.5 9 12.5 9C12.5 9 12 8.5 12 7.5C12 6.5 12.5 5.5 12.5 5.5Z"
          fill="white"
          stroke="white"
          strokeWidth="0.205564"
        />
      </g>
      <defs>
        <clipPath id="clip0_8_3032">
          <rect
            width="18.5826"
            height="16.4451"
            fill="white"
            transform="translate(0.495117 0.253296)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
