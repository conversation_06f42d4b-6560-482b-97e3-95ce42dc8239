import React from "react";

interface ChevronUpProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

export const ChevronUp: React.FC<ChevronUpProps> = ({
  width = 24,
  height = 24,
  color = "#BE259A",
  className = "",
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19.7071 14.7071C19.3166 15.0976 18.6834 15.0976 18.2929 14.7071L12 8.41421L5.70711 14.7071C5.31658 15.0976 4.68342 15.0976 4.29289 14.7071C3.90237 14.3166 3.90237 13.6834 4.29289 13.2929L11.2929 6.29289C11.6834 5.90237 12.3166 5.90237 12.7071 6.29289L19.7071 13.2929C20.0976 13.6834 20.0976 14.3166 19.7071 14.7071Z"
        fill={color}
      />
    </svg>
  );
};

export default ChevronUp;
