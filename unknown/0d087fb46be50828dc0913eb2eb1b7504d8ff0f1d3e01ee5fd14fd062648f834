{"kind": "collectionType", "collectionName": "categories", "info": {"singularName": "category", "pluralName": "categories", "displayName": "Category", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"systemId": {"type": "string"}, "title": {"type": "string"}, "handle": {"type": "string", "required": true, "unique": true}, "cross_selling_products": {"type": "relation", "relation": "manyToMany", "target": "api::product.product", "mappedBy": "cross_selling_categories"}, "bg_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "primary_color": {"type": "customField", "regex": "^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$", "customField": "plugin::color-picker.color"}, "template": {"type": "relation", "relation": "manyToOne", "target": "api::page.page", "inversedBy": "categories"}}}