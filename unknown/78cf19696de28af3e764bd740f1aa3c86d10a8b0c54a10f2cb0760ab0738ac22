// Interface automatically generated by schemas-to-ts

import { Button } from '../../elements/interfaces/Button';
import { BigFoodDidItem } from './BigFoodDidItem';
import { Button_Plain } from '../../elements/interfaces/Button';
import { BigFoodDidItem_Plain } from './BigFoodDidItem';
import { Button_NoRelations } from '../../elements/interfaces/Button';
import { BigFoodDidItem_NoRelations } from './BigFoodDidItem';

export interface WhoBrokeTrust {
  read_story_button?: Button;
  big_food_did?: BigFoodDidItem;
}
export interface WhoBrokeTrust_Plain {
  read_story_button?: Button_Plain;
  big_food_did?: BigFoodDidItem_Plain;
}

export interface WhoBrokeTrust_NoRelations {
  read_story_button?: Button_NoRelations;
  big_food_did?: BigFoodDidItem_NoRelations;
}

