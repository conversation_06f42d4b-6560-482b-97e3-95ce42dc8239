import React from "react";
import NutritionalFactsAccordion from "../../NutritionalFacts";
import ProductDetailsAccordion from "../../ProductDetailsAccordion";
import { ProductDetailsProps } from "../../types";
import ProductAdditionalDescription from "../../ProductAdditionalDescription";

/**
 * ProductDetails Component
 *
 * Contains product detail accordions:
 * - Nutritional facts
 * - Product details
 */
export const ProductDetails: React.FC<ProductDetailsProps> = ({
  strapiProduct,
  medusaProduct, // eslint-disable-line @typescript-eslint/no-unused-vars
  productType = "VARIANT",
  bundleVariants = [],
}) => {
  const primaryColor = strapiProduct?.primary_color || "#036A38";

  console.log("🔍 ProductDetails Component Debug:", {
    productType,
    bundleVariantsLength: bundleVariants?.length || 0,
    strapiProductTitle: strapiProduct?.title,
  });

  return (
    <>
      {/* Nutritional Facts */}
      <div className="mt-4">
        <NutritionalFactsAccordion
          data={strapiProduct?.nutritional_facts}
          borderColor={primaryColor}
          productType={productType}
          bundleVariants={bundleVariants}
        />
      </div>

      {/* Product Details */}
      <ProductDetailsAccordion
        data={strapiProduct?.product_detail_extra}
        borderColor={primaryColor}
      />

      {/* Additional Description */}
      {strapiProduct && (
        <ProductAdditionalDescription productData={strapiProduct} />
      )}
    </>
  );
};
