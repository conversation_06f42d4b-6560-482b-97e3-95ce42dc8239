type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

export function InfoIcon({ color = "#44426A", ...props }: IconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="currentColor"
      viewBox="0 0 25 24"
      {...props}
    >
      <path
        fill={color}
        fillRule="evenodd"
        d="M12.5 2.8a9.2 9.2 0 1 0 0 18.4 9.2 9.2 0 0 0 0-18.4M1.7 12c0-5.965 4.836-10.8 10.8-10.8S23.3 6.035 23.3 12s-4.835 10.8-10.8 10.8C6.536 22.8 1.7 17.965 1.7 12"
        clipRule="evenodd"
      ></path>
      <path
        fill={color}
        fillRule="evenodd"
        d="M12.5 11.355a.8.8 0 0 1 .8.8v4.038a.8.8 0 1 1-1.6 0v-4.038a.8.8 0 0 1 .8-.8m.905-3.198a.905.905 0 1 1-1.81 0 .905.905 0 0 1 1.81 0"
        clipRule="evenodd"
      ></path>
    </svg>
  );
}

export default InfoIcon;
