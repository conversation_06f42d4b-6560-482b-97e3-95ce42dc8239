type IconProps = React.SVGProps<SVGSVGElement> & {
  color?: string;
};

const Minus = ({ color = "#FFF", ...props }: IconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    fill="none"
    viewBox="0 0 24 24"
    {...props}
  >
    <path
      fill={color}
      fillRule="evenodd"
      d="M3.273 11.995c0-.558.488-1.01 1.09-1.01h15.273c.603 0 1.091.452 1.091 1.01s-.488 1.01-1.09 1.01H4.363c-.603 0-1.091-.452-1.091-1.01"
      clipRule="evenodd"
    ></path>
  </svg>
);

export default Minus;
