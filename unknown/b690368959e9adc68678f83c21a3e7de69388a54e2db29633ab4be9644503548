type IconProps = React.SVGProps<SVGSVGElement>;

export function YoutubeIcon(props: IconProps) {
  return (
    <svg 
      width="28" 
      height="20" 
      viewBox="0 0 28 20" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path 
        d="M13.9229 0.0126953C13.9729 0.0127005 22.3673 0.0145071 24.4658 0.591797C25.627 0.910436 26.5422 1.84972 26.8525 3.04199C27.4135 5.19194 27.416 9.66526 27.416 9.71094C27.416 9.71094 27.4165 14.2198 26.8525 16.3809C26.5422 17.5731 25.627 18.5123 24.4658 18.8311C22.3673 19.4082 13.9729 19.4102 13.9229 19.4102C13.9229 19.4102 5.48342 19.41 3.37891 18.8311C2.21776 18.5122 1.30353 17.5731 0.993164 16.3809C0.429354 14.2198 0.429688 9.71094 0.429688 9.71094C0.429702 9.66525 0.432282 5.19188 0.993164 3.04199C1.3035 1.84975 2.21775 0.91046 3.37891 0.591797C5.48342 0.0127092 13.9229 0.0126953 13.9229 0.0126953ZM11.3926 14.3486L18.1396 10.1318L11.3926 5.91504V14.3486Z" 
        fill="currentColor"
      />
    </svg>
  );
}

export default YoutubeIcon;
