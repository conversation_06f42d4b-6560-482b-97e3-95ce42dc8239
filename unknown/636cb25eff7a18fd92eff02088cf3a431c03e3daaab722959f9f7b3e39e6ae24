import Alink from '@/components/Elements/Alink';
import { MegaMenuContent, MegaMenuItem, MegaMenuTrigger } from '@/components/Features/MegaMenu';
import React from 'react';
import { MenuType } from '@/types/Components/Common/Menu';
import SubMenuContent from '../SubMenuContent';
import HeaderChevron from '@/assets/icons/HeaderChevron';

interface SubMenuContentProps {
  data: MenuType;
  activeSubMenus: { [key: string]: string | null };
  handleSubMenuEnter: (menuId: string | null, subMenuId: string | null) => void;
  index: number;
}

const MenuItem = ({
  data: item,
  activeSubMenus,
  handleSubMenuEnter,
  index,
}: SubMenuContentProps) => {
  return (
    <MegaMenuItem className="z-50 w-full">
      {(item?.sub_menu &&
        Array.isArray(item.sub_menu) &&
        item.sub_menu.length > 0) ||
      (item?.products &&
        Array.isArray(item.products) &&
        item.products.length > 0) ? (
        <>
          <MegaMenuTrigger
            className="flex !justify-center text-16 font-semibold  hover:!text-black"
            itemId={`menu-${item?.title}-${index}`}
            icon={<HeaderChevron />}
          >
            <Alink
              href={item?.action_link ?? "#"}
              target={item?.is_external_link ? "_blank" : "_self"}
              className="font-obviously font-medium text-[13px] text-nowrap hover:!text-black"
            >
              {item?.title ?? ""}
            </Alink>
            {item?.is_external_link && <span className="ml-1 text-xs">↗</span>}
            {item?.tag?.title && (
              <span className="ml-1 rounded-full border border-[#34a853] bg-[#e8f5e9] px-2 py-[4px] text-12 font-semibold uppercase leading-none text-[#34a853]">
                {item?.tag?.title ?? ""}
              </span>
            )}
          </MegaMenuTrigger>
          <MegaMenuContent
            itemId={`menu-${item.title}-${index}`}
            className="bg-[#fdf3f1] mx-auto flex w-screen max-w-1200 flex-col px-4 py-4 xl:px-0 2xl:max-w-full 2xl:px-[224px]"
          >
            <SubMenuContent
              item={item}
              activeSubMenus={activeSubMenus}
              handleSubMenuEnter={handleSubMenuEnter}
            />
          </MegaMenuContent>
        </>
      ) : (
        <div className="flex items-center">
          <Alink
            href={item?.action_link ?? "#"}
            className="font-obviously font-medium text-[13px] text-nowrap  hover:!text-black"
          >
            {item?.title ?? ""}
          </Alink>
          {item.tag?.title && (
            <span className="ml-2 rounded-full border border-[#34a853] bg-[#e8f5e9] px-2 py-[4px] text-12 font-semibold uppercase leading-none text-[#34a853]">
              {item.tag?.title ?? ""}
            </span>
          )}
        </div>
      )}
    </MegaMenuItem>
  );
};

export default MenuItem;
