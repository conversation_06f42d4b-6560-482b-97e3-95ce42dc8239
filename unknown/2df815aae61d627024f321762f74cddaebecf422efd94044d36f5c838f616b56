"use client";

import { SameDayDelivery } from "@/assets/icons/SameDayDelivery";
import Img from "@/components/Elements/img";
import ProductAddToCart from "@/components/Common/ProductAddToCart";
import StarRating from "@/components/Common/StarRating";
import { cn } from "@/libs/utils";
import React from "react";

// Product data interface
export interface ProductVariant {
  id: string;
  size: string;
  price?: number;
  originalPrice?: number; // Original price before discount
  weight?: string;
  image?: string;
}

export interface Product {
  id: string;
  title: string;
  image: string;
  price: number;
  originalPrice?: number; // Original price before discount
  isStartingPrice?: boolean;
  description?: string; // Product description
  rating: number;
  weight: string;
  primaryColor?: string;
  enableProductBg?: boolean;
  variants?: ProductVariant[];
  deliveryTime?: {
    hours: number;
    minutes: number;
    seconds: number;
  };
  sameDayDelivery?: boolean;
  buttonAction?: {
    type: string;
    redirectSlug?: string;
  };
  cardBgColor?: string;
}

// Sample product data
export const sampleProducts: Product[] = [
  {
    id: "1",
    title: "Mango Milkshake 24g Protein Powder - Pack of 1 KG",
    image: "/product-double-cocoa.png",
    price: 4443,
    // originalPrice: 4499, // 1% discount
    isStartingPrice: false,
    rating: 2.5,
    weight: "8 x 27 g",
    primaryColor: "#00693B", // matches double cocoa
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
    // variants: [
    //   {
    //     id: "1-1",
    //     size: "250g",
    //     weight: "250g",
    //     price: 1499,
    //     // originalPrice: 1599, // ~6% discount
    //     image: "/protein/FOP.png",
    //   },
    //   {
    //     id: "1-2",
    //     size: "500g",
    //     weight: "500g",
    //     price: 2799,
    //     originalPrice: 2999, // ~7% discount
    //     image: "/product-cranberry.png",
    //   },
    //   {
    //     id: "1-3",
    //     size: "1kg",
    //     weight: "1kg",
    //     price: 4443,
    //     originalPrice: 4499, // 1% discount
    //     image: "/product-peanut.png",
    //   },
    // ],
  },
  {
    id: "2",
    title: "Everyone Party - Pack of 16 Mini Protein Bars",
    image: "/product-double-cocoa.png",
    price: 960,
    isStartingPrice: false,
    rating: 4.1,
    weight: "16 x 27 g",
    primaryColor: "#93385D",
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
  {
    id: "3",
    title: "Personalised Box - Pack of 24 Mini Protein Bars",
    image: "/product-double-cocoa.png",
    price: 1440,
    isStartingPrice: true,
    rating: 4.7,
    weight: "24 x 27 g",
    primaryColor: "#93385D",
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
  {
    id: "4",
    title: "Personalised Box - Pack of 48 Mini Protein Bars",
    image: "/product-double-cocoa.png",
    price: 2880,
    isStartingPrice: true,
    rating: 4.2,
    weight: "48 x 27 g",
    primaryColor: "#93385D",
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
  {
    id: "5",
    title: "Double Cocoa Mini Protein Bars - Box of 12",
    image: "/product-double-cocoa.png",
    price: 720,
    isStartingPrice: false,
    rating: 4.5,
    weight: "12 x 27 g",
    primaryColor: "#93385D",
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
  {
    id: "6",
    title: "Cranberry Mini Protein Bars - Box of 12",
    image: "/product-double-cocoa.png",
    price: 720,
    originalPrice: 800, // 10% discount
    isStartingPrice: false,
    rating: 2.4,
    weight: "12 x 27 g",
    primaryColor: "#D23C47", // red tone
    enableProductBg: true, // Enable background color
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
    variants: [
      {
        id: "6-1",
        size: "1 x 350g",
        weight: "350g",
        price: 720,
        originalPrice: 800, // 10% discount
        image: "/product-double-cocoa.png",
      },
      {
        id: "6-2",
        size: "2 x 350g",
        weight: "700g",
        price: 1350,
        originalPrice: 1500, // 10% discount
        image: "/product-cranberry.png",
      },
      {
        id: "6-3",
        size: "3 x 350g",
        weight: "1050g",
        price: 1950,
        originalPrice: 2200, // ~11% discount
        image: "/product-peanut.png",
      },
    ],
  },
  {
    id: "7",
    title: "Peanut Cocoa Mini Protein Bars - Box of 12",
    image: "/product-double-cocoa.png",
    price: 720,
    isStartingPrice: false,
    rating: 5,
    weight: "12 x 27 g",
    primaryColor: "#F05C1D", // orange tone
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
  {
    id: "8",
    title: "Coffee Cocoa Mini Protein Bars - Box of 12",
    image: "/product-double-cocoa.png",
    price: 720,
    isStartingPrice: false,
    rating: 5,
    weight: "12 x 27 g",
    primaryColor: "#59382B", // brown tone
    deliveryTime: { hours: 5, minutes: 57, seconds: 59 },
    sameDayDelivery: true,
  },
];

interface ProductCardProps {
  product: Product;
  className?: string;
}

const DeliveryInfo: React.FC<{
  sameDayDelivery?: boolean;
  deliveryTime?: { hours: number; minutes: number; seconds: number };
  primaryColor?: string;
}> = ({ sameDayDelivery, deliveryTime, primaryColor = "#93385D" }) => {
  if (!sameDayDelivery) return null;

  return (
    <div className="flex items-center gap-[5px] h-[24px]">
      <div style={{ color: primaryColor }}>
        <SameDayDelivery className="h-4.5" />
      </div>

      {deliveryTime && (
        <>
          <p style={{ color: primaryColor }}>|</p>
          <div className="flex flex-col items-start gap-[3px] md:gap-1.5">
            <p className="text-[7px]" style={{ color: primaryColor }}>
              Order Within
            </p>
            <p
              className="text-[9px] font-medium"
              style={{ color: primaryColor }}
            >
              {deliveryTime.hours}hr : {deliveryTime.minutes}m :{" "}
              {deliveryTime.seconds}s
            </p>
          </div>
        </>
      )}
    </div>
  );
};

// Helper function to calculate discount percentage
const calculateDiscount = (
  originalPrice: number | undefined,
  currentPrice: number | undefined
): number => {
  if (!originalPrice || !currentPrice || originalPrice <= currentPrice)
    return 0;
  const discount = ((originalPrice - currentPrice) / originalPrice) * 100;
  return Math.round(discount);
};

export const ColouredProductCard: React.FC<ProductCardProps> = ({
  product,
  className,
}) => {
  // Default color if primaryColor is not provided
  const primaryColor = product.primaryColor || "#93385D";

  // State for selected variant
  const [selectedVariant, setSelectedVariant] = React.useState<
    ProductVariant | undefined
  >(
    product.variants && product.variants.length > 0
      ? product.variants[0]
      : undefined
  );

  // Update selectedVariant when product.variants change or when a new size is selected
  React.useEffect(() => {
    if (product.variants && product.variants.length > 0) {
      // If there's a currently selected variant, try to find the same size in the new variants
      if (selectedVariant) {
        const matchingVariant = product.variants.find(
          (v) => v.size === selectedVariant.size
        );
        if (matchingVariant) {
          setSelectedVariant(matchingVariant);
        } else {
          // If no matching variant is found, default to the first one
          setSelectedVariant(product.variants[0]);
        }
      } else {
        // If no variant is selected, default to the first one
        setSelectedVariant(product.variants[0]);
      }
    } else {
      setSelectedVariant(undefined);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [product.variants, selectedVariant?.id]);

  // The variant selection and image/weight updates are handled directly in the JSX

  return (
    <div
      className={cn(
        "flex flex-col rounded-lg overflow-hidden w-full h-full",
        className
      )}
      style={{
        ...(product.enableProductBg && {
          backgroundColor: primaryColor,
          color: "white",
        }),
      }}
    >
      <div
        className="relative w-full"
        style={{ height: "276px", minHeight: "276px" }}
      >
        {/* Add a key to force re-render when the image changes */}
        <Img
          key={selectedVariant?.id || "default"}
          src={selectedVariant?.image || product.image}
          alt={product.title}
          fill
          quality={100}
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          className="object-cover"
        />
      </div>
      <div
        className="flex flex-col items-start gap-2 text-left min-h-[240px]"
        style={{
          backgroundColor: product.cardBgColor || "transparent",
          ...(product.cardBgColor && { padding: "16px" }),
        }}
      >
        <p
          className="text-[18px] font-narrow font-semibold line-clamp-2 w-full h-[52px]"
          style={{
            color: product.enableProductBg ? "white" : primaryColor,
          }}
        >
          {product.title}
        </p>

        <DeliveryInfo
          sameDayDelivery={product.sameDayDelivery}
          deliveryTime={product.deliveryTime}
          primaryColor={product.enableProductBg ? "white" : primaryColor}
        />
        <div className="flex items-center gap-2 w-full h-[24px]">
          {/* Original price (strikethrough) */}
          {(selectedVariant?.originalPrice || product.originalPrice) && (
            <p
              className="text-sm line-through opacity-70"
              style={{
                color: product.enableProductBg ? "white" : primaryColor,
              }}
            >
              ₹{selectedVariant?.originalPrice || product.originalPrice}
            </p>
          )}

          {/* Current price */}
          <p
            className="text-sm font-medium font-obviously"
            style={{ color: product.enableProductBg ? "white" : primaryColor }}
          >
            {product.isStartingPrice && (
              <span className="text-sm font-medium mr-1">Starting From</span>
            )}
            ₹
            {selectedVariant?.price !== undefined
              ? selectedVariant.price
              : product.price}
          </p>

          {/* Discount percentage */}
          {(selectedVariant?.originalPrice || product.originalPrice) && (
            <p
              className="text-xs font-medium"
              style={{
                color: product.enableProductBg ? "white" : primaryColor,
              }}
            >
              (
              {calculateDiscount(
                selectedVariant?.originalPrice || product.originalPrice,
                selectedVariant?.price || product.price
              )}
              % off)
            </p>
          )}
        </div>

        <div className="flex gap-[8.5px] items-center h-[24px]">
          <StarRating
            rating={product.rating}
            size="md"
            className="gap-[2.5px]"
          />
          <p style={{ color: product.enableProductBg ? "white" : "black" }}>
            |
          </p>
          <p
            className="text-sm"
            style={{ color: product.enableProductBg ? "white" : "black" }}
          >
            {selectedVariant?.size || product.weight}
          </p>
        </div>

        <p
          className="line-clamp-2 text-sm font-obviously h-[40px]"
          style={{ color: product.enableProductBg ? "white" : primaryColor }}
        >
          Orange isn’t a flavor. It’s a feelin’. It’s a fruit that needs a
          peelin’. And when it mixes with that cocoa! Your tongue goes loco
          poco. Oh it’s a strange kinda appealin’. To a heart that needs no
          stealin’.
        </p>

        <div
          style={
            product.enableProductBg
              ? ({
                  "--primary": "white",
                  "--background": primaryColor,
                  "--foreground": "white",
                } as React.CSSProperties)
              : ({ "--primary": primaryColor } as React.CSSProperties)
          }
          className={cn(
            "w-full",
            product.enableProductBg &&
              "bg-transparent [&_button]:bg-transparent [&_button]:text-white [&_button]:border-white [&_button:hover]:bg-white/10"
          )}
        >
          <ProductAddToCart
            onAddToCart={() =>
              console.log(`Added product ${product.id} to cart`)
            }
            variant={product.enableProductBg ? "outline" : "default"}
            sizes={
              product.variants ? product.variants.map((v) => v.size) : undefined
            }
            selectedSize={selectedVariant?.size}
            onSizeChange={(size) => {
              if (product.variants) {
                const newVariant = product.variants.find(
                  (v) => v.size === size
                );
                if (newVariant) {
                  setSelectedVariant(newVariant);
                  console.log(
                    `Selected variant changed to: ${newVariant.size}, image: ${
                      newVariant.image || "default"
                    }`
                  );
                }
              }
            }}
            enableProductBg={product.enableProductBg}
            primaryColor={primaryColor}
            buttonAction={product.buttonAction}
          />
        </div>
      </div>
    </div>
  );
};

export const ProductGrid: React.FC<{
  products?: Product[];
  className?: string;
}> = ({ products = sampleProducts, className }) => {
  return (
    <div
      className={cn(
        "grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",
        className
      )}
    >
      {products.map((product) => (
        <div key={product.id} className="flex justify-center">
          <ColouredProductCard
            product={product}
            className="max-w-[300px] h-full"
          />
        </div>
      ))}
    </div>
  );
};

export default ColouredProductCard;
