"use client";

import React, { useState } from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";
import { Button } from "@/components/ui/button";
import { Star } from "lucide-react";

export interface ProductHighlightCardProps {
  title: string;
  description: string;
  image: string;
  rating: number;
  price: number;
  backgroundColor?: string;
  textColor?: string;
  onAddToCart?: () => void;
  className?: string;
}

const ProductHighlightCard: React.FC<ProductHighlightCardProps> = ({
  title,
  description,
  image,
  rating,
  price,
  backgroundColor = "#93385D", // Default purple color from image
  textColor = "white",
  onAddToCart = () => console.log("Added to cart"),
  className,
}) => {

  console.log("price", price);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const images = [image]; // Could be expanded to support multiple images

  // Star rating component similar to ProductCard
  const StarRating = () => (
    <div className="flex">
      {Array.from({ length: 5 }, (_, i) => (
        <Star
          key={i}
          className="h-4 w-4 fill-current"
          style={{ opacity: i < rating ? 1 : 0.3 }}
        />
      ))}
    </div>
  );

  return (
    <div
      className={cn("rounded-lg overflow-hidden flex flex-row", className)}
      style={{ backgroundColor, color: textColor }}
    >
      <div className="flex-1 p-6 flex flex-col justify-between">
        <div>
          <h2 className="text-2xl font-bold mb-2">{title}</h2>
          <StarRating />
          <p className="mt-4 text-sm">{description}</p>
        </div>
        <Button
          onClick={onAddToCart}
          className="mt-4 bg-white text-black hover:bg-gray-100 w-full"
        >
          ADD TO CART
        </Button>
      </div>

      <div className="relative w-1/2 bg-pink-200">
        <div className="relative h-full">
          <Image
            src={images[currentImageIndex]}
            alt={title}
            fill
            className="object-contain p-4"
          />
        </div>

        {images.length > 1 && (
          <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`h-2 w-2 rounded-full ${
                  index === currentImageIndex ? "bg-white" : "bg-white/50"
                }`}
                onClick={() => setCurrentImageIndex(index)}
                aria-label={`View image ${index + 1}`}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductHighlightCard;
