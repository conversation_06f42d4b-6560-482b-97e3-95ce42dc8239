import { useEffect, useState, useRef, useCallback } from "react";

/**
 * Custom hook to track the visibility of What's Inside section
 *
 * Logic:
 * - Hide sticky cart when What's Inside section reaches the announcement bar (top of viewport)
 * - Show sticky cart when What's Inside section is either:
 *   1. In the middle/bottom of viewport (normal viewing)
 *   2. Completely above the announcement bar (scrolled past)
 */
export const useWhatsInsideVisibility = () => {
  const [shouldHideSticky, setShouldHideSticky] = useState(false);
  const [whatsInsideRef, setWhatsInsideRef] = useState<HTMLElement | null>(
    null
  );
  const observerRef = useRef<IntersectionObserver | null>(null);
  const scrollListenerRef = useRef<(() => void) | null>(null);

  // Function to set the What's Inside element reference
  const setWhatsInsideElement = (element: HTMLElement | null) => {
    setWhatsInsideRef(element);
  };

  // Calculate announcement bar height based on screen size
  const getAnnouncementBarOffset = useCallback(() => {
    // On desktop: header height (78px) + announcement bar height (~40px)
    // On mobile: announcement bar height (64px)
    const isMobile = window.innerWidth < 768;
    return isMobile ? 64 : 118; // 78px header + ~40px announcement bar
  }, []);

  // Scroll handler to check position relative to announcement bar
  const handleScroll = useCallback(() => {
    if (!whatsInsideRef) return;

    const rect = whatsInsideRef.getBoundingClientRect();
    const announcementBarOffset = getAnnouncementBarOffset();

    // Hide sticky when What's Inside section is at or above the announcement bar
    // Show sticky when it's below the announcement bar or completely scrolled past
    const isAtAnnouncementBar =
      rect.top <= announcementBarOffset && rect.bottom > announcementBarOffset;

    setShouldHideSticky(isAtAnnouncementBar);
  }, [whatsInsideRef, getAnnouncementBarOffset]);

  useEffect(() => {
    if (!whatsInsideRef) return;

    // Clean up previous observer and scroll listener
    if (observerRef.current) {
      observerRef.current.disconnect();
    }
    if (scrollListenerRef.current) {
      window.removeEventListener("scroll", scrollListenerRef.current);
    }

    // Set up scroll listener for precise position tracking
    scrollListenerRef.current = handleScroll;
    window.addEventListener("scroll", handleScroll, { passive: true });

    // Initial check
    handleScroll();

    // Set up intersection observer as backup for performance
    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        // Trigger scroll handler when intersection changes
        if (entry.isIntersecting || !entry.isIntersecting) {
          handleScroll();
        }
      },
      {
        threshold: [0, 0.1, 0.5, 1], // Multiple thresholds for better tracking
        rootMargin: "-64px 0px -64px 0px", // Account for announcement bar
      }
    );

    observerRef.current = observer;
    observer.observe(whatsInsideRef);

    return () => {
      observer.disconnect();
      if (scrollListenerRef.current) {
        window.removeEventListener("scroll", scrollListenerRef.current);
      }
    };
  }, [whatsInsideRef, handleScroll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      if (scrollListenerRef.current) {
        window.removeEventListener("scroll", scrollListenerRef.current);
      }
    };
  }, []);

  return {
    shouldHideSticky,
    setWhatsInsideElement,
  };
};
