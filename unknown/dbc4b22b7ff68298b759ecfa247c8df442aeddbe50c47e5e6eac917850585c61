import React from "react";
import Image from "next/image";
import Link from "next/link";
import { cn } from "@/libs/utils";

interface BannerItem {
  id: string;
  image: string;
  alt: string;
  href: string;
}

interface TwoBannerSectionProps {
  banners: BannerItem[];
  className?: string;
}

const TwoBannerSection: React.FC<TwoBannerSectionProps> = ({
  banners = [],
  className,
}) => {
  // Ensure we only use the first two banners
  const displayBanners = banners.slice(0, 2);

  return (
    <div className={cn("w-full", className)}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
        {displayBanners.map((banner) => (
          <Link
            href={banner.href}
            key={banner.id}
            className="block relative w-full aspect-[16/9] md:aspect-[2/1]"
          >
            <Image
              src={banner.image}
              alt={banner.alt}
              fill
              style={{ objectFit: "fill" }}
            />
          </Link>
        ))}
      </div>
    </div>
  );
};

export default TwoBannerSection;
