// Interface automatically generated by schemas-to-ts

import { Category } from '../../../api/category/content-types/category/category';
import { Category_Plain } from '../../../api/category/content-types/category/category';

export interface BadaamProductListing {
  title?: string;
  bg_color?: any;
  category?: { data: Category };
}
export interface BadaamProductListing_Plain {
  title?: string;
  bg_color?: any;
  category?: Category_Plain;
}

export interface BadaamProductListing_NoRelations {
  title?: string;
  bg_color?: any;
  category?: number;
}

