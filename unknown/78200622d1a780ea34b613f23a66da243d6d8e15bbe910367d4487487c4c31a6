import { cn } from "@/libs/utils";
import React from "react";

interface SubHeadingProps {
  title?: string;
  color?: string;
  className?: string;
}

const SubHeading = ({
  title,
  color = "#FFFFFF",
  className,
}: SubHeadingProps) => {
  return (
    <div
      className={cn("text-center md:text-2xl text-xl font-gooddog", className)}
      style={{ color: color }}
    >
      {title}
    </div>
  );
};

export default SubHeading;
