import React from "react";
import Image from "next/image";
import { cn } from "@/libs/utils";

interface ProductVisualProps {
  productImage: string;
  className?: string;
}

const ProductVisual: React.FC<ProductVisualProps> = ({
  productImage,
  className,
}) => {
  return (
    <div
      className={cn(
        "relative w-full h-full aspect-[5/3] overflow-hidden",
        "md:w-1/2",
        className
      )}
    >
      <Image
        src={productImage}
        alt="Product"
        fill
        priority
        className="object-cover w-full h-full "
      />
    </div>
  );
};

export default ProductVisual;
