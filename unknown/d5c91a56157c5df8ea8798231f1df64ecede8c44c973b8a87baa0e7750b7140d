// Interface automatically generated by schemas-to-ts

import { Icon } from '../../media/interfaces/Icon';
import { FlavourProductItem } from './FlavourProductItem';
import { FlavourIngredientItems } from './FlavourIngredientItems';
import { Button } from '../../elements/interfaces/Button';
import { Icon_Plain } from '../../media/interfaces/Icon';
import { FlavourProductItem_Plain } from './FlavourProductItem';
import { FlavourIngredientItems_Plain } from './FlavourIngredientItems';
import { Button_Plain } from '../../elements/interfaces/Button';
import { Icon_NoRelations } from '../../media/interfaces/Icon';
import { FlavourProductItem_NoRelations } from './FlavourProductItem';
import { FlavourIngredientItems_NoRelations } from './FlavourIngredientItems';
import { Button_NoRelations } from '../../elements/interfaces/Button';

export interface FlavourItems {
  title?: string;
  title_icon?: Icon;
  bg_color?: any;
  flavour_product_item?: FlavourProductItem;
  ingredient_items: FlavourIngredientItems[];
  button?: Button;
}
export interface FlavourItems_Plain {
  title?: string;
  title_icon?: Icon_Plain;
  bg_color?: any;
  flavour_product_item?: FlavourProductItem_Plain;
  ingredient_items: FlavourIngredientItems_Plain[];
  button?: Button_Plain;
}

export interface FlavourItems_NoRelations {
  title?: string;
  title_icon?: Icon_NoRelations;
  bg_color?: any;
  flavour_product_item?: FlavourProductItem_NoRelations;
  ingredient_items: FlavourIngredientItems_NoRelations[];
  button?: Button_NoRelations;
}

