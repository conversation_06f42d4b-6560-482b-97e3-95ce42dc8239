"use client";

import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/libs/utils";

interface Message {
  text: string;
}

const announcementBarVariants = cva(
  "w-full text-white text-center flex justify-center md:justify-between items-center text-xs font-obviously px-4 py-2 sticky md:top-[var(--header-height,78px)] top-[var(--announcement-height,64px)] z-60",
  {
    variants: {
      variant: {
        primary: "bg-[#8E3A59]",
        secondary: "bg-[#231F20]",
      },
    },
    defaultVariants: {
      variant: "primary",
    },
  }
);

export interface AnnouncementBarProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof announcementBarVariants> {
  display: boolean;
  messages?: Message[];
}

const AnnouncementBar = React.forwardRef<HTMLDivElement, AnnouncementBarProps>(
  ({ className, variant, display, messages = [], ...props }, ref) => {
    if (!display) {
      return null;
    }

    return (
      <div
        className={cn(announcementBarVariants({ variant, className }))}
        ref={ref}
        data-no-close
        {...props}
      >
        {messages &&
          messages.map((message, index) => (
            <div
              key={index}
              className={cn(
                "font-medium",
                index === 0 ? "block" : "hidden md:block"
              )}
            >
              {message.text}
            </div>
          ))}
      </div>
    );
  }
);

AnnouncementBar.displayName = "AnnouncementBar";

export { AnnouncementBar, announcementBarVariants };
