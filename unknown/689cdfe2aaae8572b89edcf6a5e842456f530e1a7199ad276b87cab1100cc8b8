// Interface automatically generated by schemas-to-ts

import { Media } from '../../../common/schemas-to-ts/Media';
import { Media_Plain } from '../../../common/schemas-to-ts/Media';

export interface PeopleLivingValueItems {
  people_image?: { data: Media };
  description?: string;
  footer_text?: any;
  footer_icon?: { data: Media };
}
export interface PeopleLivingValueItems_Plain {
  people_image?: Media_Plain;
  description?: string;
  footer_text?: any;
  footer_icon?: Media_Plain;
}

export interface PeopleLivingValueItems_NoRelations {
  people_image?: number;
  description?: string;
  footer_text?: any;
  footer_icon?: number;
}

