// Interface automatically generated by schemas-to-ts

import { Image } from '../../media/interfaces/Image';
import { PressQuoteItems } from './PressQuoteItems';
import { Image_Plain } from '../../media/interfaces/Image';
import { PressQuoteItems_Plain } from './PressQuoteItems';
import { Image_NoRelations } from '../../media/interfaces/Image';
import { PressQuoteItems_NoRelations } from './PressQuoteItems';

export interface StopThePress {
  title_image?: Image;
  press_quote_items: PressQuoteItems[];
}
export interface StopThePress_Plain {
  title_image?: Image_Plain;
  press_quote_items: PressQuoteItems_Plain[];
}

export interface StopThePress_NoRelations {
  title_image?: Image_NoRelations;
  press_quote_items: PressQuoteItems_NoRelations[];
}

