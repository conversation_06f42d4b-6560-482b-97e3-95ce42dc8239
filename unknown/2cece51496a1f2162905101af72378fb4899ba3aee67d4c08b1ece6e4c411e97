import Image from "next/image";
import Link from "next/link";
import React from "react";

interface InfoCardProps {
  heading: string;
  subheading: string;
  description: string;
  imageSrc: string;
  imageAlt: string;
  topBgColor: string;
  bottomBgColor: string;
  linkUrl?: string;
}

const InfoCard: React.FC<InfoCardProps> = ({
  heading,
  subheading,
  description,
  imageSrc,
  imageAlt,
  topBgColor,
  bottomBgColor,
  linkUrl,
}) => {
  return (
    <div className="h-full rounded-xl w-full lg:w-[320px] overflow-hidden">
      <div
        className="h-full lg:h-[248px] py-6 lg:pt-10 px-6 "
        style={{ backgroundColor: topBgColor }}
      >
        {linkUrl ? (
          <Link href={linkUrl}>
            <h3 className="text-[32px] text-white font-narrow font-semibold">
              {heading}
            </h3>
          </Link>
        ) : (
          <h3 className="text-[32px] text-white font-narrow font-semibold">
            {heading}
          </h3>
        )}
        <p className="text-white text-sm font-obviously">{subheading}</p>
      </div>

      <div
        className="flex justify-between lg:justify-normal"
        style={{ backgroundColor: bottomBgColor }}
      >
        <div className="w-full lg:w-7/12 pl-6 py-6">
          <p>{description}</p>
        </div>
        <div className="relative lg:w-5/12 pl-1 mt-6 lg:mt-[93px]">
          <Image
            src={imageSrc}
            alt={imageAlt}
            width={168}
            height={115}
            className="object-fill"
          />
        </div>
      </div>
    </div>
  );
};

export default InfoCard;
