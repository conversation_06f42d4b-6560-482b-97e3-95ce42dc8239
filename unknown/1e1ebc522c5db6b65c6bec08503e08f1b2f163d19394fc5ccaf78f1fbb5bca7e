interface NextProps {
  color?: string;
}

export const UnderLine = ({ color = "#1A181E", ...props }: NextProps) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="55"
      height="4"
      fill="none"
      viewBox="0 0 55 4"
      {...props}
    >
      <path
        stroke={color}
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="1.4"
        d="M54 1C36.5 1 17 3 1 3"
      ></path>
    </svg>
  );
};
