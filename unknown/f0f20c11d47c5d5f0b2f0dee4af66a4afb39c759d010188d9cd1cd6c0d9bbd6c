"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetClose,
  SheetFooter,
} from "@/components/ui/sheet";
import TwitterIcon from "@/assets/icons/TwitterIcon";
import FacebookIcon from "@/assets/icons/FacebookIcon";
import InstagramIcon from "@/assets/icons/InstagramIcon";
import YoutubeIcon from "@/assets/icons/YoutubeIcon";
import UserIcon from "@/assets/icons/User";
import { cn } from "@/libs/utils";
import MobileMenuChevron from "@/assets/icons/MobileMenuChevron.";
import { MenuType } from "@/types/Components/Common/Menu";
import { getStrapiUrl } from "@/utils/strapiUrl";
import { useAppSelector } from "@/redux/hooks";
import AuthDialog from "@/components/Auth/AuthDialog";

interface MobileMenuProps {
  onClose: () => void;
  isOpen: boolean;
  menuData: MenuType[];
}

const MobileMenu = ({ onClose, isOpen, menuData }: MobileMenuProps) => {
  const [expandedMenu, setExpandedMenu] = useState<string | null>(null);
  const { isAuthenticated, user } = useAppSelector((state) => state.auth);

  const toggleMenu = (menu: string) => {
    setExpandedMenu((prev) => (prev === menu ? null : menu));
  };

  return (
    <Sheet
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          setExpandedMenu(null);
          onClose();
        }
      }}
    >
      <SheetContent
        side="left"
        className="w-[90vw] max-w-sm p-0 sm:max-w-sm top-[96px] h-[calc(100dvh-96px)] border-none"
        aria-describedby="mobile-menu-description"
        onEscapeKeyDown={(e) => {
          e.preventDefault();
          onClose();
        }}
        onPointerDownOutside={(e) => {
          const target = e.target as HTMLElement;
          const sheetContent = document.querySelector('[role="dialog"]');

          if (sheetContent?.contains(target)) {
            e.preventDefault();
            e.stopPropagation();
            return;
          }
        }}
        onInteractOutside={(e) => {
          const target = e.target as HTMLElement;
          const sheetContent = document.querySelector('[role="dialog"]');

          if (sheetContent?.contains(target)) {
            e.preventDefault();
            e.stopPropagation();
            return;
          }
        }}
      >
        <SheetDescription id="mobile-menu-description" className="sr-only">
          Mobile navigation menu
        </SheetDescription>
        <div
          className="h-full bg-white overflow-y-auto flex flex-col justify-between"
          style={{ position: "relative", zIndex: 40 }}
          onMouseDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
          onPointerDown={(e) => {
            e.preventDefault();
            e.stopPropagation();
          }}
        >
          <div className="container mx-auto">
            <nav className="flex flex-col">
              {/* Login Button */}
              <div className="px-6 py-4">
                {isAuthenticated ? (
                  <div className="flex items-center gap-2">
                    <UserIcon className="w-8 h-8" color="#93385D" />
                    <div>
                      <p className="font-semibold text-sm">
                        Hello, {user?.name || "User"}
                      </p>
                      <p className="text-xs text-gray-500">{user?.phone}</p>
                    </div>
                  </div>
                ) : (
                  <SheetClose asChild>
                    <AuthDialog
                      trigger={
                        <button className="font-obviously py-1 px-3 flex items-center gap-2 text-black border border-[#93385D] rounded-[8px] text-xs font-semibold">
                          <UserIcon className="w-8 h-8" color="#93385D" />
                          Login
                        </button>
                      }
                    />
                  </SheetClose>
                )}
              </div>

              {/* Dynamic Menu Items */}
              {menuData.map((menuItem, index) => {
                if (!menuItem.show) return null;

                const menuId = menuItem.title
                  .toLowerCase()
                  .replace(/\s+/g, "-");
                const hasSubMenu =
                  menuItem.sub_menu && menuItem.sub_menu.length > 0;

                return (
                  <div
                    key={`${menuId}-${index}`}
                    className={cn(
                      "border-t-[0.1px] border-[rgba(0,0,0,0.1)] px-6 py-4 cursor-pointer",
                      expandedMenu === menuId && "bg-[#F4EBEF]"
                    )}
                  >
                    {hasSubMenu ? (
                      <>
                        <button
                          type="button"
                          className="flex items-center justify-between w-full text-left font-medium cursor-pointer"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            toggleMenu(menuId);
                          }}
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                        >
                          <span className="font-narrow text-[#1a181e] text-xl font-semibold">
                            {menuItem.title}
                            {menuItem.title === "BYOB" && (
                              <span className="text-[#93385D] text-base pl-1 font-normal font-gooddog">
                                build your own box
                              </span>
                            )}
                          </span>
                          <MobileMenuChevron className="h-5 w-5" />
                        </button>
                        {expandedMenu === menuId && (
                          <div
                            className="mt-2 bg-[#F4EBEF] -mx-4 px-4 py-4"
                            onMouseDown={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                          >
                            <div className="grid grid-cols-2 gap-3">
                              {menuItem.sub_menu?.map((subItem, subIndex) => (
                                <SheetClose
                                  asChild
                                  key={`${subItem.title}-${subIndex}`}
                                >
                                  <Link
                                    href={subItem.action_link || "#"}
                                    className="bg-white flex items-center justify-between gap-2 px-3 py-2 rounded-2xl shadow-[-2px_4px_8px_0px_rgba(0,0,0,0.12)]"
                                    onMouseDown={(e) => {
                                      e.preventDefault();
                                      e.stopPropagation();
                                    }}
                                  >
                                    <p className="font-narrow text-[#1a181e] text-sm font-semibold">
                                      {subItem.title}
                                    </p>
                                    {subItem.image && (
                                      <div className="relative aspect-video flex items-center justify-center w-12 h-12">
                                        <Image
                                          src={getStrapiUrl(subItem.image)}
                                          alt={subItem.title || ""}
                                          fill
                                          className="object-contain"
                                        />
                                      </div>
                                    )}
                                  </Link>
                                </SheetClose>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <SheetClose asChild>
                        <Link
                          href={menuItem.action_link || "#"}
                          className="block font-narrow text-[#1a181e] text-xl font-semibold"
                          onMouseDown={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                          }}
                        >
                          {menuItem.title}
                        </Link>
                      </SheetClose>
                    )}
                  </div>
                );
              })}
            </nav>
          </div>
          <SheetFooter className="flex flex-row items-center justify-between bg-[#1a181e] px-6 py-4">
            <div className="flex flex-row items-center gap-3">
              <SheetClose asChild>
                <Link
                  href="/contact-us"
                  className="text-white font-obviously text-xs"
                >
                  Contact Us
                </Link>
              </SheetClose>
              <SheetClose asChild>
                <Link
                  href="/track-order"
                  className="text-white font-obviously text-xs"
                >
                  Track Order
                </Link>
              </SheetClose>
            </div>

            <div className="flex items-center gap-2">
              <SheetClose asChild>
                <Link
                  href="https://twitter.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Twitter"
                >
                  <TwitterIcon className="w-5 h-5 text-white hover:text-[#CEFD7B] transition-colors" />
                </Link>
              </SheetClose>
              <SheetClose asChild>
                <Link
                  href="https://facebook.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Facebook"
                >
                  <FacebookIcon className="w-5 h-5 text-white hover:text-[#CEFD7B] transition-colors" />
                </Link>
              </SheetClose>
              <SheetClose asChild>
                <Link
                  href="https://instagram.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Instagram"
                >
                  <InstagramIcon className="w-5 h-5 text-white hover:text-[#CEFD7B] transition-colors" />
                </Link>
              </SheetClose>
              <SheetClose asChild>
                <Link
                  href="https://youtube.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="YouTube"
                >
                  <YoutubeIcon className="w-5 h-5 text-white hover:text-[#CEFD7B] transition-colors" />
                </Link>
              </SheetClose>
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileMenu;
