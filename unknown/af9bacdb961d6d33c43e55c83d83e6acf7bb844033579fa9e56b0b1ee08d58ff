// Interface automatically generated by schemas-to-ts

import { TrustBuildingFoodItems } from './TrustBuildingFoodItems';
import { TrustBuildingFoodItems_Plain } from './TrustBuildingFoodItems';
import { TrustBuildingFoodItems_NoRelations } from './TrustBuildingFoodItems';

export interface RebuildingTheTrust {
  title?: any;
  description?: string;
  food_items: TrustBuildingFoodItems[];
  short_story_button_text?: string;
  short_story_description?: any;
}
export interface RebuildingTheTrust_Plain {
  title?: any;
  description?: string;
  food_items: TrustBuildingFoodItems_Plain[];
  short_story_button_text?: string;
  short_story_description?: any;
}

export interface RebuildingTheTrust_NoRelations {
  title?: any;
  description?: string;
  food_items: TrustBuildingFoodItems_NoRelations[];
  short_story_button_text?: string;
  short_story_description?: any;
}

