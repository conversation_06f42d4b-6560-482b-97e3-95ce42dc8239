"use client";

import React, { useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselDots,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Img from "@/components/Elements/img";
import StarRating from "@/components/Common/StarRating";
import ProductAddToCart from "@/components/Common/ProductAddToCart";
import { cn } from "@/libs/utils";
import { SameDayDelivery } from "@/assets/icons/SameDayDelivery";

export interface CarouselProductCardProps {
  title: string;
  price: string | number;
  description?: string;
  rating?: number;
  size?: string;
  images: string[];
  className?: string;
  onAddToCart?: () => void;
  variant?: React.ComponentProps<typeof ProductAddToCart>["variant"];
  enableProductBg?: boolean;
  primaryColor?: string;
  sizes?: string[];
  selectedSize?: string;
  onSizeChange?: (size: string) => void;
  buttonAction?: {
    type: string;
    redirectSlug?: string;
  };
  sameDayDelivery?: boolean;
  deliveryTime?: {
    hours: number;
    minutes: number;
    seconds: number;
  };
}

const CarouselProductCard: React.FC<CarouselProductCardProps> = ({
  title,
  price,
  description = "",
  rating = 0,
  size = "",
  images,
  className,
  onAddToCart = () => console.log("Added to cart"),
  variant = "outline",
  enableProductBg = false,
  primaryColor = "#ff8abb",
  sizes = [],
  selectedSize = "",
  onSizeChange,
  buttonAction = { type: "add_to_cart" },
  sameDayDelivery = false,
  deliveryTime,
}) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  console.log("images", currentSlide);

  return (
    <div
      className={cn(
        "inline-block border rounded-xl  p-4 bg-white max-w-[266px]",
        className
      )}
    >
      {/* carousel product */}
      <div className="w-[234px] relative">
        <Carousel
          className="w-full relative"
          opts={{ loop: true }}
          onSlideChange={(index) => setCurrentSlide(index)}
        >
          <div className="rounded-lg overflow-hidden">
            <CarouselContent>
              {images.map((image, index) => (
                <CarouselItem key={index}>
                  <div className="relative aspect-square w-full flex items-center justify-center">
                    <Img
                      src={image}
                      alt={`${title} - Image ${index + 1}`}
                      width={300}
                      height={300}
                      className="object-contain"
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>

            {/* Dot indicators */}
            <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
              <CarouselDots
                className="gap-2"
                baseClassName="transition-all duration-300"
                activeClassName="bg-white w-6 h-2 rounded-full"
                inactiveClassName="bg-white/50 w-2 h-2 rounded-full"
              />
            </div>
          </div>

          {/* Navigation buttons - inside carousel but positioned outside visually */}
          <CarouselPrevious
            className="hidden md:block absolute -left-4 md:-left-12 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent text-white border-none h-8 w-8 md:h-12 md:w-12 cursor-pointer [&_svg]:!text-white"
            icon={<ChevronLeft className="h-10 w-10 md:h-20 md:w-20" />}
          />
          <CarouselNext
            className="hidden md:block absolute -right-4 md:-right-12 top-1/2 -translate-y-1/2 bg-transparent hover:bg-transparent text-white border-none h-8 w-8 md:h-12 md:w-12 cursor-pointer [&_svg]:!text-white"
            icon={<ChevronRight className="h-10 w-10 md:h-20 md:w-20" />}
          />
        </Carousel>
      </div>
      <div className="mt-4">
        <h3 className="font-narrow line-clamp-2 text-base font-semibold">
          {title}
        </h3>
        <p className="text-sm text-black font-obviously font-medium mb-2">
          {typeof price === "number" ? `₹${price}` : price}
        </p>
        {sameDayDelivery && (
          <div className="flex items-center gap-[5px] mb-2">
            <div style={{ color: primaryColor }}>
              <SameDayDelivery className="h-4.5" />
            </div>

            {deliveryTime && (
              <>
                <p style={{ color: primaryColor }}>|</p>
                <div className="flex flex-col items-start gap-[3px] md:gap-1.5">
                  <p className="text-[7px]" style={{ color: primaryColor }}>
                    Order Within
                  </p>
                  <p
                    className="text-[9px] font-medium"
                    style={{ color: primaryColor }}
                  >
                    {deliveryTime.hours}hr : {deliveryTime.minutes}m :{" "}
                    {deliveryTime.seconds}s
                  </p>
                </div>
              </>
            )}
          </div>
        )}
        {(rating > 0 || size) && (
          <div className="flex gap-[8.5px] items-center h-[24px]">
            {rating > 0 && <StarRating rating={rating} size="md" />}
            {rating > 0 && size && <p>|</p>}
            {size && <p className="text-sm">{size}</p>}
          </div>
        )}
      </div>
      {description && (
        <p className="line-clamp-2 text-sm font-obviously h-[40px] mb-[22px]">
          {description}
        </p>
      )}
      <div
        style={
          enableProductBg
            ? ({
                "--primary": "white",
                "--background": primaryColor,
                "--foreground": "white",
              } as React.CSSProperties)
            : ({ "--primary": primaryColor } as React.CSSProperties)
        }
        className={cn(
          enableProductBg &&
            "bg-transparent [&_button]:bg-transparent [&_button]:text-white [&_button]:border-white [&_button:hover]:bg-white/10"
        )}
      >
        <ProductAddToCart
          onAddToCart={onAddToCart}
          variant={variant}
          selectedSize={
            selectedSize || (sizes.length > 0 ? sizes[0] : undefined)
          }
          enableProductBg={enableProductBg}
          primaryColor={primaryColor}
          sizes={sizes}
          onSizeChange={onSizeChange}
          buttonAction={buttonAction}
        />
      </div>
    </div>
  );
};

export default CarouselProductCard;
