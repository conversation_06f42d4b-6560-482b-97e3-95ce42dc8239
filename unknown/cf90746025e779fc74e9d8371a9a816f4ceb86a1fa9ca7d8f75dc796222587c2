// Interface automatically generated by schemas-to-ts

import { ContentItems } from './ContentItems';
import { ContentItems_Plain } from './ContentItems';
import { ContentItems_NoRelations } from './ContentItems';

export interface LoveForContent {
  title?: any;
  bg_color?: any;
  content_items: ContentItems[];
}
export interface LoveForContent_Plain {
  title?: any;
  bg_color?: any;
  content_items: ContentItems_Plain[];
}

export interface LoveForContent_NoRelations {
  title?: any;
  bg_color?: any;
  content_items: ContentItems_NoRelations[];
}

