{"kind": "collectionType", "collectionName": "pages", "info": {"singularName": "page", "pluralName": "pages", "displayName": "Page", "description": ""}, "options": {"draftAndPublish": true}, "attributes": {"handle": {"type": "string", "unique": true, "required": true}, "page_type": {"type": "enumeration", "enum": ["PAGE", "INDEX", "CATEGORY", "PRODUCT"], "required": true}, "blocks": {"type": "dynamiczone", "components": ["banner.single-banner", "banner.banner-carousel", "common.marquee", "elements.html-editor", "contact-us.contact-us-page", "elements.header", "media.external-media-embed", "refer.refer-and-earn", "career.employee-photo-grid", "reviews.truth-sayers", "reviews.stop-the-press", "reviews.startup-fam", "reviews.our-community", "reviews.love-wall", "reviews.love-for-content", "reviews.join-the-gang", "reviews.influencer-truth", "reviews.celebrity-stars", "media.image", "elements.title", "career.youtube-banner", "career.whole-gang", "career.values", "career.stop-the-lies", "career.people-living-values", "career.job-positions", "career.hiring-process", "review-templates.verified-reviews-template", "review-templates.real-people-reviews-template", "plp.two-sided-product-banner", "plp.shop-chocolate", "plp.sachet-banner", "plp.real-people-real-review-comments", "plp.protein-powder-product-listing", "plp.protein-bar-pro-product-listing", "plp.process-images", "plp.pick-your-protein", "plp.nothing-to-hide", "plp.multi-categories", "plp.milk-chocolate-product-listing", "plp.long-description", "plp.just-ingredients", "plp.indulgent-flavours", "plp.hashtag-comment", "plp.dark-chocolate-product-listing", "plp.borderless-product-listing", "plp.badaam-product-listing", "pdp-templates.why-section-template", "pdp-templates.whats-inside-template", "pdp-templates.review-template", "pdp-templates.quickly-added-template", "pdp-templates.product-details-extra-template", "pdp-templates.product-additional-description-template", "pdp-templates.nutritional-facts-template", "pdp-templates.key-features-template", "pdp-templates.ingredient-details-template", "pdp-templates.got-a-question-template", "pdp-templates.certificate-banner-template", "media.video", "media.image-or-video", "homepage.who-broke-trust", "homepage.stop-the-press", "homepage.stars-love-us", "homepage.rebuilding-the-trust", "homepage.real-people-love", "homepage.real-food", "homepage.real-food-is-flawed", "homepage.real-food-categories", "homepage.pyaar-secret-ingredient", "homepage.head-to-infinite-love", "homepage.go-truth-seekers", "homepage.fixing-food", "faqs.faqs", "faqs.faq-template", "contact-us.contact-us", "certificates.certificate-manager", "common.press-logo-marquee", "common.announcement-bar", "career.employee-images", "banner.protein-banner", "banner.banner-with-title"]}, "categories": {"type": "relation", "relation": "oneToMany", "target": "api::category.category", "mappedBy": "template"}, "products": {"type": "relation", "relation": "oneToMany", "target": "api::product.product", "mappedBy": "template"}}}