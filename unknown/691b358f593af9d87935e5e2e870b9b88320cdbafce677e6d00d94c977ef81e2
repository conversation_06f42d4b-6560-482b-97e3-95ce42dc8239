import * as React from "react";
interface ChevronRightProps {
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}
const ChevronRight = ({
  width = 16,
  height = 31,
  color = "#FFF",
  className = "",
}: ChevronRightProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    viewBox="0 0 16 31"
    className={className}
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth="5"
      d="m3 28 10-12.5L3 3"
    ></path>
  </svg>
);

export default ChevronRight;
