// TypeScript interfaces for VerifiedReviews component with TanStack Query

export interface ReviewMedia {
  id: string;
  url: string;
  type: "IMAGE" | "VIDEO";
  product_review_id: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface Product {
  id: string;
  title: string;
  handle: string;
  subtitle: string;
  description: string;
  is_giftcard: boolean;
  status: "published" | "draft";
  thumbnail: string | null;
  weight: number | null;
  length: number | null;
  height: number | null;
  width: number | null;
  origin_country: string | null;
  hs_code: string | null;
  mid_code: string | null;
  material: string | null;
  discountable: boolean;
  external_id: string | null;
  metadata: Record<string, any> | null;
  type_id: string | null;
  type: any | null;
  collection_id: string | null;
  collection: any | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface ProductReview {
  id: string;
  is_admin_verified: boolean;
  title: string;
  description: string;
  is_verified_purchase: boolean;
  name: string;
  rating: number;
  created_at: string;
  updated_at: string;
  response: string | null;
  associated_medias: ReviewMedia[];
  product: Product;
}

export interface PaginatedResponse<T> {
  data: {
    data: T[];
    count: number;
    offset: number;
    limit: number;
  };
  message: string;
  status: number;
  success: boolean;
}

export interface ReviewStats {
  average_rating: number;
  rating_distribution: { [key: string]: number };
  review_count: number;
}

export interface ReviewsQueryParams {
  product_id?: string;
  rating?: number;
  status?: string;
  limit?: number;
  offset?: number;
}

export interface UseReviewsOptions {
  productId: string;
  limit?: number;
  enabled?: boolean;
}

export interface UseReviewsReturn {
  data: ProductReview[];
  totalCount: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface UseReviewStatsOptions {
  productId: string;
  enabled?: boolean;
}

export interface UseReviewStatsReturn {
  data: ReviewStats | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
}
