// Interface automatically generated by schemas-to-ts

import { Product } from '../../../product/content-types/product/product';
import { Page } from '../../../page/content-types/page/page';
import { Product_Plain } from '../../../product/content-types/product/product';
import { Page_Plain } from '../../../page/content-types/page/page';
import { AdminPanelRelationPropertyModification } from '../../../../common/schemas-to-ts/AdminPanelRelationPropertyModification';

export interface Category {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: { data: Product[] };
  bg_color?: any;
  primary_color?: any;
  template?: { data: Page };
}
export interface Category_Plain {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: Product_Plain[];
  bg_color?: any;
  primary_color?: any;
  template?: Page_Plain;
}

export interface Category_NoRelations {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: number[];
  bg_color?: any;
  primary_color?: any;
  template?: number;
}

export interface Category_AdminPanelLifeCycle {
  documentId: string;
  createdAt: Date;  updatedAt: Date;  publishedAt?: Date;
  systemId?: string;
  title?: string;
  handle: string;
  cross_selling_products?: AdminPanelRelationPropertyModification<Product_Plain>;
  bg_color?: any;
  primary_color?: any;
  template?: AdminPanelRelationPropertyModification<Page_Plain>;
}
