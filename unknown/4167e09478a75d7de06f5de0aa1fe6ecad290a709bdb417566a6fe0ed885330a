import { MediaFormatType } from "./MediaFormat";
export interface MediaType {
  documentId: string;
  name: string;
  alternativeText: string;
  caption: string;
  width: number;
  height: number;
  formats: {
    thumbnail: MediaFormatType;
    small: MediaFormatType;
    medium: MediaFormatType;
    large: MediaFormatType;
  };
  hash: string;
  ext: string;
  mime: string;
  size: number;
  url: string;
  previewUrl: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
}
