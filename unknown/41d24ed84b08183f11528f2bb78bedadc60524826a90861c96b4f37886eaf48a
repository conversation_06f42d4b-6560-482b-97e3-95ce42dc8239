"use client";

import React, { useRef } from "react";
import { cn } from "@/libs/utils";
import FeatureCard from "./FeatureCard";
import { UniversalCarousel } from "@/components/Common/CarouselWrapper.tsx";
import { MiniStrikeThrough } from "@/assets/icons/MiniStrikeThrough";
import MoveArrow from "@/assets/icons/MoveArrow";

export interface FeatureHighlightSectionProps {
  title?: string;
  subtitle?: string;
  images: string[];
  className?: string;
  backgroundColor?: string;
  textColor?: string;
  subtitleColor?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  erasedText?: string;
  visibleText?: string;
  strikethroughColor?: string;
  visibleTextColor?: string;
  phraseColor?: string;
  controls?: boolean;
  showScrollbar?: boolean;
  scrollbarThumbColor?: string;
}

const FeatureHighlightSection: React.FC<FeatureHighlightSectionProps> = ({
  title,
  subtitle,
  images = [],
  className,
  backgroundColor = "#FFFFFF",
  textColor = "text-white",
  subtitleColor = "#1a181e",
  titleClassName,
  subtitleClassName,
  erasedText,
  visibleText,
  strikethroughColor = "#FFFFFF",
  visibleTextColor,
  phraseColor,
  controls = false,
  showScrollbar = false,
  scrollbarThumbColor,
}) => {
  const carouselRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (carouselRef.current) {
      const scrollContainer =
        carouselRef.current.querySelector(".carousel-content");
      if (scrollContainer) {
        scrollContainer.scrollBy({ left: -320, behavior: "smooth" });
      }
    }
  };

  const scrollRight = () => {
    if (carouselRef.current) {
      const scrollContainer =
        carouselRef.current.querySelector(".carousel-content");
      if (scrollContainer) {
        scrollContainer.scrollBy({ left: 320, behavior: "smooth" });
      }
    }
  };

  return (
    <section
      className={cn(
        backgroundColor,
        textColor,
        "py-4 md:py-12 px-4 w-full",
        className
      )}
      style={{ backgroundColor }}
    >
      <div className="max-w-6xl mx-auto flex flex-col items-center">
        {/* Section Header */}
        <div className="text-center mb-8 relative">
          <h2
            className={cn(
              "text-[32px] lg:text-[38px] font-narrow font-semibold",
              titleClassName
            )}
            style={{ color: phraseColor }}
          >
            {erasedText && visibleText ? (
              <>
                <span
                  className="absolute top-[-20px] lg:top-[-18px] left-[-12px] rotate-[-13.40deg] text-lg lg:text-[24px] font-gooddog font-normal"
                  style={{ color: visibleTextColor || "#E91E63" }}
                >
                  {visibleText}
                </span>
                <span className="relative inline-block">
                  <span className="relative z-10 text-[32px] lg:text-[38px] font-narrow font-semibold">
                    {erasedText}
                  </span>
                  <MiniStrikeThrough
                    className="absolute top-5 left-0 right-0"
                    strikethroughColor={strikethroughColor}
                  />
                </span>{" "}
                {title}
              </>
            ) : (
              title
            )}
          </h2>
          {subtitle && (
            <p
              className={cn(
                "lg:text-2xl text-lg font-gooddog",
                subtitleClassName
              )}
              style={{ color: subtitleColor }}
            >
              {subtitle}
            </p>
          )}
        </div>

        {/* Cards Container */}
        <div className="w-full relative">
          <div ref={carouselRef}>
            <UniversalCarousel
              useNativeScrollbar={true}
              hideScrollbar={!showScrollbar}
              className="pb-4"
              options={{
                align: "center",
                containScroll: "trimSnaps",
              }}
              gapClassName="gap-2"
              scrollbarColor={scrollbarThumbColor}
              scrollBarTrackColor="#f0f0f0"
            >
              {images.map((image, index) => (
                <FeatureCard
                  key={`card-${index}`}
                  image={image}
                  className="min-w-[280px] md:min-w-[320px] max-w-[350px] rounded-xl overflow-hidden"
                />
              ))}
            </UniversalCarousel>
          </div>

          {/* Navigation buttons positioned below scrollbar */}
          {controls && (
            <div className="flex justify-end items-center gap-3 mt-4">
              <button
                onClick={scrollLeft}
                className="flex items-center justify-center w-6.5 h-6.5 rounded-full transition-all duration-200 hover:scale-110 active:scale-90 active:transition-all active:duration-100"
                aria-label="Previous images"
                style={{
                  transform: "scale(1)",
                  transition:
                    "transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1)",
                }}
                onMouseDown={(e) => {
                  e.currentTarget.style.transform = "scale(0.9)";
                  e.currentTarget.style.transition = "transform 0.1s ease-out";
                }}
                onMouseUp={(e) => {
                  e.currentTarget.style.transform = "scale(1)";
                  e.currentTarget.style.transition =
                    "transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "scale(1)";
                  e.currentTarget.style.transition =
                    "transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)";
                }}
              >
                <MoveArrow
                  className="transform rotate-0"
                  color="#000"
                  width={26}
                  height={26}
                  style={{
                    boxShadow: `-2px 2px 0px ${scrollbarThumbColor}`,
                    borderRadius: "50%",
                  }}
                />
              </button>
              <button
                onClick={scrollRight}
                className="flex items-center justify-center w-6.5 h-6.5 rounded-full transition-all duration-200 hover:scale-110 active:scale-90 active:transition-all active:duration-100"
                aria-label="Next images"
                style={{
                  transform: "scale(1)",
                  transition:
                    "transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1)",
                }}
                onMouseDown={(e) => {
                  e.currentTarget.style.transform = "scale(0.9)";
                  e.currentTarget.style.transition = "transform 0.1s ease-out";
                }}
                onMouseUp={(e) => {
                  e.currentTarget.style.transform = "scale(1)";
                  e.currentTarget.style.transition =
                    "transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = "scale(1)";
                  e.currentTarget.style.transition =
                    "transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1)";
                }}
              >
                <MoveArrow
                  className="transform rotate-180"
                  color="#000"
                  width={26}
                  height={26}
                  style={{
                    boxShadow: `-2px -2px 0px ${scrollbarThumbColor}`,
                    borderRadius: "50%",
                  }}
                />
              </button>
            </div>
          )}
        </div>
      </div>
    </section>
  );
};

export default FeatureHighlightSection;
