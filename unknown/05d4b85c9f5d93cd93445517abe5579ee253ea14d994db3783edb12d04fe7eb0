import { createAction } from "@reduxjs/toolkit";
import {
  loginRequest,
  loginSuccess,
  loginFailure,
  sendOtpRequest,
  sendOtpSuccess,
  sendOtpFailure,
  verifyOtpRequest,
  verifyOtpSuccess,
  verifyOtpFailure,
  logout,
  resetAuthState,
} from "../slices/auth-slice";

// Action types
export const AUTH_SEND_OTP = "auth/sendOtp";
export const AUTH_VERIFY_OTP = "auth/verifyOtp";
export const AUTH_LOGIN = "auth/login";
export const AUTH_LOGOUT = "auth/logout";

// Action creators for saga
export const sendOtp = createAction<{ phone: string }>(AUTH_SEND_OTP);
export const verifyOtp = createAction<{ phone: string; otp: string }>(
  AUTH_VERIFY_OTP
);
export const login = createAction<{ phone: string }>(AUTH_LOGIN);
export const logoutUser = createAction(AUTH_LOGOUT);

// Export all slice actions for direct use
export {
  loginRequest,
  loginSuccess,
  loginFailure,
  sendOtpRequest,
  sendOtpSuccess,
  sendOtpFailure,
  verifyOtpRequest,
  verifyOtpSuccess,
  verifyOtpFailure,
  logout,
  resetAuthState,
};
