import { useMemo } from "react";
import { ExtendedMedusaProductWithStrapiProduct, ExtendedVariant } from "@/types/Medusa/Product";

/**
 * Custom hook for generating combined product images from global and variant-specific sources
 *
 * @param medusaProductData - Complete product data from Medusa with Strapi integration
 * @param activeVariant - Currently selected product variant
 * @returns Array of image URLs combining global and variant-specific images
 */
export const useProductImages = (
  medusaProductData: ExtendedMedusaProductWithStrapiProduct | null,
  activeVariant: ExtendedVariant | null
): string[] => {
  return useMemo(() => {
    const globalImages: string[] = [];
    const variantImages: string[] = [];

    // Extract global product images from medusaProductData
    // Note: StoreProduct typically has an 'images' property
    if (medusaProductData?.images && Array.isArray(medusaProductData.images)) {
      globalImages.push(
        ...medusaProductData.images.map((img: any) => img.url || img)
      );
    }

    // Extract variant-specific images from activeVariant
    if (
      activeVariant?.variant_image &&
      Array.isArray(activeVariant.variant_image)
    ) {
      // Sort by rank to maintain proper order
      const sortedVariantImages = [...activeVariant.variant_image].sort(
        (a, b) => a.rank - b.rank
      );
      variantImages.push(...sortedVariantImages.map((img) => img.url));
    }

    // Combine global images first, then variant-specific images
    const combined = [...globalImages, ...variantImages];

    // Fallback to sample images if no images are available
    if (combined.length === 0) {
      return [
        "/images/products/pdp/image.png",
        "/images/products/badaam/highlight/2.webp",
        "/images/products/badaam/highlight/3.webp",
        "/images/products/badaam/highlight/4.webp",
        "/images/products/badaam/highlight/5.webp",
      ];
    }

    return combined;
  }, [medusaProductData?.images, activeVariant?.variant_image]);
};
