// =========================================
// CMS Configuration (Strapi)
// =========================================
export const STRAPI_URL = process.env.STRAPI_URL!;
export const STRAPI_TOKEN = process.env.STRAPI_TOKEN!;

// =========================================
// E-Commerce Configuration (Medusa)
// =========================================
export const MEDUSA_URL = process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL!;
export const MEDUSA_TOKEN = process.env.MEDUSA_TOKEN!;
export const MEDUSA_PUBLISHABLE_KEY =
  process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY!;
